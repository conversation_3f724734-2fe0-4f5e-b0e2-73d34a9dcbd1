{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\forgetPhone.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\forgetPhone.vue", "mtime": 1754050582485}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { captchaApi, configApi, updateHoneApi, phoneValidatorApi } from '@/api/sms';\nexport default {\n    name: 'forgetPhone',\n    props: {\n        isIndex: {\n            type: Boolean,\n            default: false\n        }\n    },\n    data () {\n        const validatePhone = (rule, value, callback) => {\n            if (!value) {\n                return callback(new Error('请填写手机号'));\n            } else if (!/^1[3456789]\\d{9}$/.test(value)) {\n                callback(new Error('手机号格式不正确!'));\n            } else {\n                callback();\n            }\n        };\n        var validatePass = (rule, value, callback) => {\n            if (value === '') {\n                callback(new Error('请输入密码'));\n            } else {\n                if (this.formInline.checkPass !== '') {\n                    this.$refs.formInline.validateField('checkPass');\n                }\n                callback();\n            }\n        };\n\n        return {\n            cutNUm: '获取验证码',\n            canClick: true,\n            current: 0,\n            formInline: {\n                account: '',\n                phone: '',\n                code: '',\n                password: '',\n            },\n            ruleInline: {\n                phone: [\n                    { required: true, validator: validatePhone, trigger: 'blur' }\n                ],\n                code: [\n                    { required: true, message: '请输入验证码', trigger: 'blur' }\n                ],\n                password: [\n                    { required: true, message: '请输入密码', trigger: 'blur' }\n                ],\n                account: [\n                    { required: true, message: '请输入当前账号', trigger: 'blur' }\n                ]\n            }\n        }\n    },\n    methods: {\n        // 短信验证码\n        cutDown () {\n            if (this.formInline.phone) {\n                if (!this.canClick) return;\n                this.canClick = false;\n                this.cutNUm = 60;\n                let data = {\n                    phone: this.formInline.phone,\n                    types: 1\n                };\n                captchaApi(data).then(async res => {\n                    this.$message.success(res.msg);\n                })\n                let time = setInterval(() => {\n                    this.cutNUm--;\n                    if (this.cutNUm === 0) {\n                        this.cutNUm = '获取验证码';\n                        this.canClick = true;\n                        clearInterval(time)\n                    }\n                }, 1000)\n            } else {\n                this.$message.warning('请填写手机号!');\n            }\n        },\n        handleSubmit1 (name) {\n            this.$refs[name].validate((valid) => {\n                if (valid) {\n                  phoneValidatorApi(this.formInline).then(async res => {\n                    this.$message.success('操作成功')\n                    this.current = 1;\n                  })\n                } else {\n                    return false;\n                }\n            })\n        },\n        handleSubmit2(name) {\n            this.$refs[name].validate((valid) => {\n                if (valid) {\n                    updateHoneApi(this.formInline).then(async res => {\n                        this.$message.success('操作成功')\n                        this.current = 2;\n                    })\n                } else {\n                    return false;\n                }\n            })\n        },\n        //登录\n        handleSubmit (name,num) {\n            this.$refs[name].validate((valid) => {\n                if (valid) {\n                    configApi({\n                        account: this.formInline.account,\n                        password: this.formInline.password\n                    }).then(async res => {\n                        num===1?this.$message.success(\"原手机号密码正确\"):this.$message.success(\"登录成功\");\n                        num===1?this.current = 1:this.$emit('on-Login');\n                    })\n                } else {\n                    return false;\n                }\n            })\n        },\n        returns () {\n            this.current === 0 ? this.$emit('gobackPhone'): this.current = 0\n        }\n    }\n}\n", null]}