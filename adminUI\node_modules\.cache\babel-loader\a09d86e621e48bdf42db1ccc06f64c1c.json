{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\integral\\config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\integral\\config\\index.vue", "mtime": 1754050582450}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _ZBParser = _interopRequireDefault(require(\"@/components/FormGenerator/components/parser/ZBParser\"));\nvar _systemConfig = require(\"@/api/systemConfig.js\");\nvar _validate = require(\"@/utils/validate\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"integralconfig\",\n  components: {\n    zbParser: _ZBParser.default\n  },\n  data: function data() {\n    return {\n      isShow: false,\n      isCreate: 0,\n      editData: {},\n      formId: 109\n    };\n  },\n  mounted: function mounted() {\n    this.getFormInfo();\n  },\n  methods: {\n    resetForm: function resetForm(formValue) {\n      this.editData = {};\n    },\n    handlerSubmit: (0, _validate.Debounce)(function (data) {\n      var _this = this;\n      var tempArr = [];\n      for (var key in data) {\n        var obj = {};\n        obj.name = key;\n        obj.title = key;\n        obj.value = data[key];\n        tempArr.push(obj);\n      }\n      var _pram = {\n        'fields': tempArr,\n        'id': this.formId,\n        'sort': 0,\n        'status': true\n      };\n      (0, _systemConfig.configSaveForm)(_pram).then(function (res) {\n        _this.getFormInfo();\n        _this.$message.success('操作成功');\n      });\n    }),\n    // 获取表单详情\n    getFormInfo: function getFormInfo() {\n      var _this2 = this;\n      (0, _systemConfig.configInfo)({\n        id: this.formId\n      }).then(function (res) {\n        _this2.isShow = false;\n        _this2.editData = res;\n        _this2.isCreate = 1;\n        setTimeout(function () {\n          // 让表单重复渲染待编辑数据\n          _this2.isShow = true;\n        }, 80);\n      });\n    }\n  }\n};", null]}