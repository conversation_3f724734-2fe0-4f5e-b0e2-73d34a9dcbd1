{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillConfig\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillConfig\\index.vue", "mtime": 1754050582453}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport zbParser from '@/components/FormGenerator/components/parser/ZBParser'\nimport { configSaveForm, configInfo } from '@/api/systemConfig.js'\nimport { seckill<PERSON>ist<PERSON><PERSON>, seckill<PERSON>pdate<PERSON><PERSON>, seckillInfo<PERSON><PERSON>, seckill<PERSON>ave<PERSON><PERSON>, seckillDeleteApi, seckillConfigStatusApi } from '@/api/marketing'\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nimport {Debounce} from '@/utils/validate'\nexport default {\n  name: \"SeckillConfig\",\n  components: { zbParser },\n  data() {\n    return {\n      dialogVisible: false,\n      isShow: true,\n      isCreate: 0,\n      editData: {},\n      formId: 123,\n      listLoading: true,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        name: '',\n        isDel: false,\n        status: ''\n      },\n      seckillId: '',\n      loading: false\n    }\n  },\n  mounted() {\n   this.getList()\n  },\n  methods: {\n    checkPermi,\n    resetForm(formValue) {\n      this.dialogVisible = false\n    },\n    // 删除\n    handleDelete(id, idx) {\n      this.$modalSure().then(() => {\n        seckillDeleteApi({ id: id }).then(() => {\n          this.$message.success('删除成功')\n          this.tableData.data.splice(idx, 1)\n        })\n      })\n    },\n    onchangeIsShow(row) {\n      seckillConfigStatusApi(row.id, {status: row.status})\n        .then(async () => {\n          this.$message.success('修改成功');\n          this.getList()\n        }).catch(()=>{\n        row.status = !row.status\n      })\n    },\n    onEditSort(row) {\n      this.$set(row, 'isEdit', true)\n    },\n    onBlur(row) {\n      this.$set(row, 'isEdit', false)\n      this.onEdit(row.id, row)\n    },\n    // 获取表单详情\n    getFormInfo(id) {\n      this.loading = true\n      seckillInfoApi({ id: id }).then(res => {\n        this.editData = res\n        this.dialogVisible = true\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n      })\n    },\n    // 编辑\n    handleEdit(id) {\n      this.seckillId = id\n      this.getFormInfo(id)\n      this.isCreate = 1\n    },\n    // 编辑\n    onEdit(id, obj) {\n      const data = obj ? obj : this.editData\n      seckillUpdateApi({id}, data).then(res => {\n        this.isSuccess()\n      }).catch((res) => {\n        this.listLoading = false\n      })\n    },\n    // 提交\n    handlerSubmit:Debounce(function(formValue) {\n     if(formValue.time.split(',')[0].split(':')[0] > formValue.time.split(',')[1].split(':')[0]) return this.$message.error('请填写正确的时间范围')\n      this.isCreate === 0 ? seckillSaveApi(formValue).then(res => {\n        this.isSuccess()\n      }) : seckillUpdateApi({id: this.seckillId}, formValue).then(res => {\n        this.isSuccess()\n      })\n    }),\n    isSuccess(){\n      this.$message.success('操作成功')\n      this.dialogVisible = false\n      this.getList()\n    },\n    // 列表\n    getList(num) {\n      this.listLoading = true\n      this.tableFrom.page = num ? num : this.tableFrom.page;\n      seckillListApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.tableData.data.map(item => this.$set(item, 'isEdit', false))\n        this.listLoading = false\n      }).catch((res) => {\n        this.listLoading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      this.getList()\n    },\n    add() {\n      this.isCreate = 0\n      this.dialogVisible = true\n    },\n    handleClose() {\n      this.dialogVisible = false\n      this.editData = {}\n    }\n  }\n}\n", null]}