{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillConfig\\index.vue?vue&type=template&id=20adda21&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillConfig\\index.vue", "mtime": 1754050582453}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <div class=\"container\">\n        <el-form inline>\n          <el-form-item label=\"是否显示\">\n            <el-select v-model=\"tableFrom.status\" placeholder=\"请选择\" class=\"filter-item selWidth mr20\" @change=\"getList(1)\" clearable>\n              <el-option label=\"关闭\" :value=\"0\" />\n              <el-option label=\"开启\" :value=\"1\" />\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"秒杀名称：\">\n            <el-input v-model=\"tableFrom.name\" placeholder=\"请输入秒杀名称\" class=\"selWidth\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"getList(1)\"/>\n            </el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n      <el-button size=\"mini\" type=\"primary\" @click=\"add\" v-hasPermi=\"['admin:seckill:manger:save']\">添加秒杀配置</el-button>\n    </div>\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      ref=\"multipleTable\"\n      :header-cell-style=\" {fontWeight:'bold'}\"\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        min-width=\"50\"\n      />\n      <el-table-column\n        label=\"秒杀名称\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <router-link :to=\"{ path:'/marketing/seckill/list/' + scope.row.id}\">\n            <el-button type=\"text\" size=\"small\">{{scope.row.name}}</el-button>\n          </router-link>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"name\"\n        label=\"秒杀时段\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          {{scope.row.time.split(',').join(' - ')}}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"轮播图\" min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <div class=\"acea-row\" v-if=\"scope.row.silderImgs\">\n            <div class=\"demo-image__preview mr5\" v-for=\"item in JSON.parse(scope.row.silderImgs)\" :key=\"item.attId\">\n              <el-image\n                style=\"width: 36px; height: 36px\"\n                :src=\"item.sattDir\"\n                :preview-src-list=\"[item.sattDir]\"\n              />\n            </div>\n          </div>\n          <span v-else>无</span>\n        </template>\n      </el-table-column>\n      <!--<el-table-column-->\n        <!--label=\"排序\"-->\n        <!--min-width=\"100\"-->\n      <!--&gt;-->\n        <!--<template slot-scope=\"scope\">-->\n          <!--<div v-if=\"!scope.row.isEdit\" @click=\"onEditSort(scope.row)\" @blur=\"onBlur(scope.row, scope.row.sort)\" style=\"width: 100%;cursor: pointer;\">{{scope.row.sort}}</div>-->\n          <!--<el-input v-model=\"scope.row.sort\" placeholder=\"请输入排序\" v-else @blur=\"onBlur(scope.row)\"></el-input>-->\n        <!--</template>-->\n      <!--</el-table-column>-->\n      <el-table-column\n        label=\"状态\"\n        min-width=\"150\"\n      >\n        <template slot-scope=\"scope\" v-if=\"checkPermi(['admin:seckill:manger:update:status'])\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            :active-value=\"1\"\n            :inactive-value=\"0\"\n            active-text=\"开启\"\n            inactive-text=\"关闭\"\n            @change=\"onchangeIsShow(scope.row)\"\n          />\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"createTime\"\n        label=\"创建时间\"\n        min-width=\"130\"\n      />\n      <el-table-column label=\"操作\" min-width=\"150\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" size=\"small\" @click=\"handleEdit(scope.row.id)\" v-hasPermi=\"['admin:seckill:manger:info','admin:seckill:manger:update']\">编辑</el-button>\n          <el-button type=\"text\" size=\"small\" @click=\"handleDelete(scope.row.id, scope.$index)\"  class=\"mr10\" v-hasPermi=\"['admin:seckill:manger:delete']\">删除</el-button>\n          <router-link :to=\"{ path:'/marketing/seckill/creatSeckill/creat/' + scope.row.id}\">\n            <el-button type=\"text\" size=\"small\" v-hasPermi=\"['admin:seckill:save']\">添加商品</el-button>\n          </router-link>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block mb20\">\n      <el-pagination\n        :page-sizes=\"[10, 20, 30, 40]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n\n  <el-dialog\n    :title=\"isCreate===0 ? '添加数据' : '编辑数据'\"\n    :visible.sync=\"dialogVisible\"\n    width=\"700px\"\n    :before-close=\"handleClose\"\n  >\n    <div v-loading=\"loading\">\n      <zb-parser\n        :form-id=\"formId\"\n        :is-create=\"isCreate\"\n        :edit-data=\"editData\"\n        @submit=\"handlerSubmit\"\n        @resetForm=\"resetForm\"\n        v-if=\"dialogVisible\"\n      />\n    </div>\n  </el-dialog>\n</div>\n", null]}