{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\notification\\index.vue?vue&type=template&id=335d2f14&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\notification\\index.vue", "mtime": 1754050582514}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <div>\n    <el-card :bordered=\"false\" class=\"box-card\">\n      <div>\n        <el-tabs v-model=\"currentTab\" @tab-click=\"changeTab\">\n          <el-tab-pane\n            :label=\"item.label\"\n            :name=\"item.value.toString()\"\n            v-for=\"(item, index) in headerList\"\n            :key=\"index + '-only'\"\n          />\n        </el-tabs>\n      </div>\n      <el-row type=\"flex\" class=\"mb20 mt-1\">\n        <el-col>\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-document\"\n            @click=\"syncRoutine()\"\n            v-hasPermi=\"['admin:wechat:routine:sync']\"\n            >同步小程序订阅消息</el-button\n          >\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-document\"\n            @click=\"syncWechat()\"\n            v-hasPermi=\"['admin:wechat:whcbqhn:sync']\"\n            >同步微信模版消息</el-button\n          >\n        </el-col>\n      </el-row>\n      <div class=\"description\">\n        <p><span class=\"iconfont iconxiaochengxu\"></span> 小程序经营类目：生活服务 > 百货/超市/便利店</p>\n        <p><span class=\"iconfont icongongzhonghao\"></span> 公众号经营类目：IT科技/互联网|电子商务，IT科技/IT软件与服务</p>\n      </div>\n      <el-table \n      :data=\"levelLists\" \n      ref=\"table\" \n      class=\"mt25\" \n      size=\"small\" \n      v-loading=\"loadingList\"\n      :header-cell-style=\" {fontWeight:'bold'}\">\n        <el-table-column label=\"ID\" prop=\"id\" width=\"80\"></el-table-column>\n        <el-table-column label=\"通知类型\" prop=\"type\"></el-table-column>\n        <el-table-column label=\"通知场景说明\" prop=\"description\"></el-table-column>\n        <el-table-column label=\"标识\" prop=\"mark\"></el-table-column>\n        <el-table-column label=\"公众号模板\" prop=\"isWechat\" v-if=\"currentTab == '1'\">\n          <template slot-scope=\"scope\" v-if=\"scope.row.isWechat !== 0\">\n            <el-switch\n              v-model=\"scope.row.isWechat\"\n              :active-value=\"1\"\n              :inactive-value=\"2\"\n              active-text=\"启用\"\n              inactive-text=\"禁用\"\n              @change=\"changeWechat(scope.row)\"\n            >\n            </el-switch>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"小程序订阅\" prop=\"isRoutine\" v-if=\"currentTab == '1'\">\n          <template slot-scope=\"scope\" v-if=\"scope.row.isRoutine !== 0\">\n            <el-switch\n              v-model=\"scope.row.isRoutine\"\n              :active-value=\"1\"\n              :inactive-value=\"2\"\n              active-text=\"启用\"\n              inactive-text=\"禁用\"\n              @change=\"changeRoutine(scope.row)\"\n            >\n            </el-switch>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"发送短信\" prop=\"isSms\">\n          <template slot-scope=\"scope\" v-if=\"scope.row.isSms !== 0\">\n            <el-switch\n              v-model=\"scope.row.isSms\"\n              :active-value=\"1\"\n              :inactive-value=\"2\"\n              active-text=\"启用\"\n              inactive-text=\"禁用\"\n              @change=\"changeSms(scope.row)\"\n            >\n            </el-switch>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"设置\" prop=\"id\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" @click=\"setting(scope.row)\" v-hasPermi=\"['admin:system:notification:detail']\">详情</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-card>\n  </div>\n  <el-dialog\n    title=\"通知详情\"\n    :visible.sync=\"centerDialogVisible\"\n    width=\"50%\"\n    >\n    <el-tabs :value=\"infoTab\" @tab-click=\"changeInfo\">\n      <el-tab-pane\n        :label=\"item.label\"\n        :name=\"item.value.toString()\"\n        v-for=\"(item, index) in currentTab == '1' ? infoList : infoList1\"\n        :key=\"index\"\n      />\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\n        <el-form-item label=\"ID\">\n          <el-input v-model=\"form.id\" disabled></el-input>\n        </el-form-item>\n        <el-form-item label=\"模板名\" v-if=\"form.name\">\n          <el-input v-model=\"form.name\" disabled></el-input>\n        </el-form-item>\n        <el-form-item label=\"模板ID\" v-if=\"form.tempId\">\n          <el-input v-model=\"form.tempId\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"模板编号\" v-if=\"form.tempKey\"> \n          <el-input v-model=\"form.tempKey\" disabled></el-input>\n        </el-form-item>\n        <el-form-item label=\"模板说明\" v-if=\"form.title\"> \n          <el-input v-model=\"form.title\" disabled></el-input>\n        </el-form-item>\n        <el-form-item label=\"模板内容\" v-if=\"form.content\"> \n          <el-input v-model=\"form.content\" disabled></el-input>\n        </el-form-item>\n        <el-form-item label=\"状态\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio label=\"1\">开启</el-radio>\n            <el-radio label=\"2\">关闭</el-radio>\n          </el-radio-group>\n        </el-form-item>\n      </el-form>\n    </el-tabs>\n    <span slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"centerDialogVisible = false\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"submit()\">确 定</el-button>\n    </span>\n  </el-dialog>\n</div>\n", null]}