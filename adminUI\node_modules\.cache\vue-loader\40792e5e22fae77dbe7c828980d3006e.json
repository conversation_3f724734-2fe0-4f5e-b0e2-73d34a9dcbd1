{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillList\\index.vue?vue&type=template&id=2046c57d&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillList\\index.vue", "mtime": 1754050582454}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _c(\n                \"div\",\n                { staticClass: \"container\" },\n                [\n                  _c(\n                    \"el-form\",\n                    { attrs: { inline: \"\" } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"是否显示：\" } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              staticClass: \"filter-item selWidth\",\n                              attrs: { placeholder: \"请选择\", clearable: \"\" },\n                              on: {\n                                change: function ($event) {\n                                  return _vm.getList(1)\n                                },\n                              },\n                              model: {\n                                value: _vm.tableFrom.status,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tableFrom, \"status\", $$v)\n                                },\n                                expression: \"tableFrom.status\",\n                              },\n                            },\n                            [\n                              _c(\"el-option\", {\n                                attrs: { label: \"关闭\", value: 0 },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: { label: \"开启\", value: 1 },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"配置名称：\" } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              staticClass: \"selWidth\",\n                              attrs: { placeholder: \"请选择\", clearable: \"\" },\n                              on: {\n                                change: function ($event) {\n                                  return _vm.getList(1)\n                                },\n                              },\n                              model: {\n                                value: _vm.tableFrom.timeId,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tableFrom, \"timeId\", $$v)\n                                },\n                                expression: \"tableFrom.timeId\",\n                              },\n                            },\n                            _vm._l(_vm.seckillTime, function (item) {\n                              return _c(\"el-option\", {\n                                key: item.id,\n                                attrs: {\n                                  label: item.name + \" - \" + item.time,\n                                  value: item.id,\n                                },\n                              })\n                            }),\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"商品搜索：\" } },\n                        [\n                          _c(\n                            \"el-input\",\n                            {\n                              staticClass: \"selWidth\",\n                              attrs: {\n                                placeholder: \"请输入商品ID/名称\",\n                                clearable: \"\",\n                              },\n                              model: {\n                                value: _vm.tableFrom.keywords,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tableFrom, \"keywords\", $$v)\n                                },\n                                expression: \"tableFrom.keywords\",\n                              },\n                            },\n                            [\n                              _c(\"el-button\", {\n                                attrs: {\n                                  slot: \"append\",\n                                  icon: \"el-icon-search\",\n                                },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.getList(1)\n                                  },\n                                },\n                                slot: \"append\",\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"router-link\",\n                {\n                  attrs: {\n                    to: { path: \"/marketing/seckill/creatSeckill/creat\" },\n                  },\n                },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"hasPermi\",\n                          rawName: \"v-hasPermi\",\n                          value: [\"admin:seckill:save\"],\n                          expression: \"['admin:seckill:save']\",\n                        },\n                      ],\n                      staticClass: \"mr10\",\n                      attrs: { size: \"small\", type: \"primary\" },\n                    },\n                    [_vm._v(\"添加秒杀商品\")]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.listLoading,\n                  expression: \"listLoading\",\n                },\n              ],\n              ref: \"multipleTable\",\n              staticStyle: { width: \"100%\" },\n              attrs: {\n                data: _vm.tableData.data,\n                size: \"mini\",\n                \"header-cell-style\": { fontWeight: \"bold\" },\n              },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { prop: \"id\", label: \"ID\", \"min-width\": \"50\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"配置\", \"min-width\": \"160\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"div\", [\n                          _vm._v(\n                            _vm._s(\n                              scope.row.storeSeckillManagerResponse\n                                ? scope.row.storeSeckillManagerResponse.name\n                                : \"-\"\n                            )\n                          ),\n                        ]),\n                        _vm._v(\" \"),\n                        _c(\"div\", [\n                          _vm._v(\n                            _vm._s(\n                              scope.row.startTime + \" - \" + scope.row.stopTime\n                            )\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"name\", label: \"秒杀时段\", \"min-width\": \"130\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"div\", [\n                          _vm._v(\n                            _vm._s(\n                              scope.row.storeSeckillManagerResponse\n                                ? scope.row.storeSeckillManagerResponse.time\n                                    .split(\",\")\n                                    .join(\" - \")\n                                : \"-\"\n                            )\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"商品图片\", \"min-width\": \"80\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"div\",\n                          { staticClass: \"demo-image__preview\" },\n                          [\n                            _c(\"el-image\", {\n                              staticStyle: { width: \"36px\", height: \"36px\" },\n                              attrs: {\n                                src: scope.row.image,\n                                \"preview-src-list\": [scope.row.image],\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"商品标题\",\n                  prop: \"title\",\n                  \"min-width\": \"300\",\n                  \"show-overflow-tooltip\": true,\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"活动简介\",\n                  \"min-width\": \"300\",\n                  prop: \"info\",\n                  \"show-overflow-tooltip\": true,\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"原价\", prop: \"otPrice\", \"min-width\": \"100\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"秒杀价\", \"min-width\": \"100\", prop: \"price\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"限量\", prop: \"quotaShow\", \"min-width\": \"80\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"限量剩余\", \"min-width\": \"80\", prop: \"quota\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"秒杀状态\",\n                  \"min-width\": \"100\",\n                  prop: \"statusName\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"创建时间\",\n                  prop: \"createTime\",\n                  \"min-width\": \"150\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"状态\", \"min-width\": \"80\", fixed: \"right\" },\n                scopedSlots: _vm._u(\n                  [\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return _vm.checkPermi([\"admin:seckill:update:status\"])\n                          ? [\n                              _c(\"el-switch\", {\n                                attrs: {\n                                  \"active-value\": 1,\n                                  \"inactive-value\": 0,\n                                  \"active-text\": \"开启\",\n                                  \"inactive-text\": \"关闭\",\n                                },\n                                on: {\n                                  change: function ($event) {\n                                    return _vm.onchangeIsShow(scope.row)\n                                  },\n                                },\n                                model: {\n                                  value: scope.row.status,\n                                  callback: function ($$v) {\n                                    _vm.$set(scope.row, \"status\", $$v)\n                                  },\n                                  expression: \"scope.row.status\",\n                                },\n                              }),\n                            ]\n                          : undefined\n                      },\n                    },\n                  ],\n                  null,\n                  true\n                ),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"操作\",\n                  \"min-width\": \"120\",\n                  fixed: \"right\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"router-link\",\n                          {\n                            attrs: {\n                              to: {\n                                path:\n                                  \"/marketing/seckill/creatSeckill/updeta/\" +\n                                  scope.row.productId +\n                                  \"/\" +\n                                  scope.row.id,\n                              },\n                            },\n                          },\n                          [\n                            _c(\n                              \"el-button\",\n                              {\n                                directives: [\n                                  {\n                                    name: \"hasPermi\",\n                                    rawName: \"v-hasPermi\",\n                                    value: [\"admin:seckill:info\"],\n                                    expression: \"['admin:seckill:info']\",\n                                  },\n                                ],\n                                attrs: { type: \"text\", size: \"small\" },\n                              },\n                              [_vm._v(\"编辑\")]\n                            ),\n                          ],\n                          1\n                        ),\n                        _vm._v(\" \"),\n                        scope.row.killStatus !== 2\n                          ? _c(\n                              \"el-button\",\n                              {\n                                directives: [\n                                  {\n                                    name: \"hasPermi\",\n                                    rawName: \"v-hasPermi\",\n                                    value: [\"admin:seckill:delete\"],\n                                    expression: \"['admin:seckill:delete']\",\n                                  },\n                                ],\n                                staticClass: \"mr10\",\n                                attrs: { type: \"text\", size: \"small\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.handleDelete(\n                                      scope.row.id,\n                                      scope.$index\n                                    )\n                                  },\n                                },\n                              },\n                              [_vm._v(\"删除\")]\n                            )\n                          : _vm._e(),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"block mb20\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"page-sizes\": [10, 20, 30, 40],\n                  \"page-size\": _vm.tableFrom.limit,\n                  \"current-page\": _vm.tableFrom.page,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.tableData.total,\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChange,\n                  \"current-change\": _vm.pageChange,\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}