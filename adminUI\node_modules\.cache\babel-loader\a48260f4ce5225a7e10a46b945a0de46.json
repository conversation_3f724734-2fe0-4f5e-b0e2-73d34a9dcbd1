{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\i18n.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\i18n.js", "mtime": 1754553124447}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754052677050}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _vue = _interopRequireDefault(require(\"vue\"));\nvar _vueI18n = _interopRequireDefault(require(\"vue-i18n\"));\nvar _zhCN = _interopRequireDefault(require(\"./lang/zh-CN\"));\nvar _en = _interopRequireDefault(require(\"./lang/en\"));\nvar _id = _interopRequireDefault(require(\"./lang/id\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n_vue.default.use(_vueI18n.default);\nvar messages = {\n  'zh-CN': _zhCN.default,\n  en: _en.default,\n  id: _id.default\n};\nvar i18n = new _vueI18n.default({\n  locale: localStorage.getItem('locale') || 'zh-CN',\n  fallbackLocale: 'zh-CN',\n  messages: messages\n});\nvar _default = exports.default = i18n;", null]}