{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\collateOrder\\index.vue?vue&type=style&index=0&id=48ed33d1&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\collateOrder\\index.vue", "mtime": 1754050582505}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\css-loader\\index.js", "mtime": 1754554385075}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754554389499}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754554387093}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1754554384522}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.demo-table-expand{\n  ::v-deep label{\n    width: 83px !important;\n  }\n}\n.selWidth{\n  width: 300px;\n}\n.el-dropdown-link {\n  cursor: pointer;\n  color: #409EFF;\n  font-size: 12px;\n}\n.el-icon-arrow-down {\n  font-size: 12px;\n}\n.tabBox_tit {\n  width: 60%;\n  font-size: 12px !important;\n  margin: 0 2px 0 10px;\n  letter-spacing: 1px;\n  padding: 5px 0;\n  box-sizing: border-box;\n}\n.text_overflow{\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.pup_card{\n  width: 200px;\n  border-radius: 5px;\n  padding: 5px;\n  box-sizing: border-box;\n  font-size: 12px;\n  line-height: 16px;\n}\n.flex-column{\n  display: flex;\n  flex-direction: column;\n}\n", null]}