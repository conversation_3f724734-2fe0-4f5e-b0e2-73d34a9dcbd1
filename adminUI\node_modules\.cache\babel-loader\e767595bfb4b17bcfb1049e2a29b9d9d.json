{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\collateOrder\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\collateOrder\\index.vue", "mtime": 1754050582505}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _storePoint = require(\"@/api/storePoint\");\nvar _index = _interopRequireDefault(require(\"@/components/cards/index\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  components: {\n    cardsData: _index.default\n  },\n  data: function data() {\n    return {\n      storeSelectList: [],\n      orderId: 0,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      listLoading: true,\n      tableFrom: {\n        keywords: '',\n        storeId: '',\n        dateLimit: '',\n        page: 1,\n        limit: 20\n      },\n      timeVal: [],\n      fromList: this.$constants.fromList,\n      ids: '',\n      cardLists: []\n    };\n  },\n  mounted: function mounted() {\n    this.storeList();\n    this.getList();\n  },\n  methods: {\n    storeList: function storeList() {\n      var _this = this;\n      var artFrom = {\n        page: 1,\n        limit: 999,\n        status: '1',\n        keywords: ''\n      };\n      (0, _storePoint.storeListApi)(artFrom).then(function (res) {\n        _this.storeSelectList = res.list;\n      });\n    },\n    pageChangeLog: function pageChangeLog(page) {\n      this.tableFromLog.page = page;\n      this.getList();\n    },\n    handleSizeChangeLog: function handleSizeChangeLog(val) {\n      this.tableFromLog.limit = val;\n      this.getList();\n    },\n    // 选择时间\n    selectChange: function selectChange(tab) {\n      this.tableFrom.date = tab;\n      this.tableFrom.page = 1;\n      this.timeVal = [];\n      this.getList();\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : '';\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 列表\n    getList: function getList(num) {\n      var _this2 = this;\n      this.listLoading = true;\n      this.tableFrom.page = num ? num : this.tableFrom.page;\n      (0, _storePoint.orderListApi)(this.tableFrom).then(function (res) {\n        _this2.tableData.data = res.list.list;\n        _this2.tableData.total = res.list.total;\n        _this2.cardLists = [{\n          name: '订单数量',\n          count: res.total,\n          color: '#1890FF',\n          class: 'one',\n          icon: 'icondingdan'\n        }, {\n          name: '订单金额',\n          count: res.orderTotalPrice,\n          color: '#A277FF',\n          class: 'two',\n          icon: 'icondingdanjine'\n        }, {\n          name: '退款总单数',\n          count: res.refundTotal,\n          color: '#EF9C20',\n          class: 'three',\n          icon: 'icondingdanguanli'\n        }, {\n          name: '退款总金额',\n          count: res.refundTotalPrice,\n          color: '#1BBE6B',\n          class: 'four',\n          icon: 'iconshangpintuikuanjine'\n        }];\n        // this.cardLists = res.data.stat\n        _this2.listLoading = false;\n      }).catch(function () {\n        _this2.listLoading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    }\n  }\n};", null]}