{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\register.vue?vue&type=template&id=2937ad88&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\register.vue", "mtime": 1754050582487}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"login-container\">\n  <el-form ref=\"formInline\" size=\"small\" :model=\"formInline\" :rules=\"ruleInline\" class=\"login-form\" autocomplete=\"on\" label-position=\"left\">\n    <div class=\"title-container\">\n      <h3 class=\"title mb15\">一号通账户注册</h3>\n    </div>\n    <el-form-item prop=\"phone\">\n      <el-input\n        v-model=\"formInline.phone\"\n        placeholder=\"请输入您的手机号\"\n        prefix-icon=\"el-icon-phone-outline\"\n      />\n    </el-form-item>\n    <el-form-item prop=\"password\">\n      <el-input\n        :key=\"passwordType\"\n        v-model=\"formInline.password\"\n        :type=\"passwordType\"\n        placeholder=\"密码\"\n        tabindex=\"2\"\n        auto-complete=\"off\"\n        prefix-icon=\"el-icon-lock\"\n      />\n      <span class=\"show-pwd\" @click=\"showPwd\">\n        <svg-icon :icon-class=\"passwordType === 'password' ? 'eye' : 'eye-open'\" />\n      </span>\n    </el-form-item>\n    <el-form-item prop=\"domain\">\n      <el-input\n        v-model=\"formInline.domain\"\n        placeholder=\"请输入网址域名\"\n        prefix-icon=\"el-icon-position\"\n      />\n    </el-form-item>\n    <el-form-item prop=\"code\" class=\"captcha\">\n      <div class=\"acea-row\" style=\"flex-wrap: nowrap;\">\n        <el-input\n          v-model=\"formInline.code\"\n          placeholder=\"验证码\"\n          type=\"text\"\n          tabindex=\"1\"\n          autocomplete=\"off\"\n          prefix-icon=\"el-icon-message\"\n          style=\"width: 90%\"\n        />\n        <el-button size=\"mini\" :disabled=!this.canClick @click=\"cutDown\" v-hasPermi=\"['admin:pass:send:code']\">{{cutNUm}}</el-button>\n      </div>\n    </el-form-item>\n    <el-button  :loading=\"loading\" type=\"primary\" style=\"width:100%;margin-bottom:20px;\" @click=\"handleSubmit('formInline')\" v-hasPermi=\"['admin:pass:register']\">注册</el-button>\n    <el-button  type=\"primary\" style=\"width:100%;margin-bottom:20px;\" @click=\"changelogo\" v-hasPermi=\"['admin:pass:login']\">立即登录</el-button>\n  </el-form>\n</div>\n", null]}