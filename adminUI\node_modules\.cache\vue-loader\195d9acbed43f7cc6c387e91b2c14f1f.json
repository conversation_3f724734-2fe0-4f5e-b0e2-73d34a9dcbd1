{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\tableList.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\tableList.vue", "mtime": 1754050582488}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { smsLstApi, serviceOpenApi, exportTempApi, expressAllApi, captchaApi, smsSignApi  } from '@/api/sms'\r\nimport * as commFilter from '@/filters/commFilter';\r\nimport Template from \"../../../appSetting/wxAccount/wxTemplate/index\";\r\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\r\nimport {Debounce} from '@/utils/validate'\r\nexport default {\r\n  name: 'TableList',\r\n  props: {\r\n    copy: {\r\n      type: Object,\r\n      default: null\r\n    },\r\n    dump: {\r\n      type: Object,\r\n      default: null\r\n    },\r\n    query: {\r\n      type: Object,\r\n      default: null\r\n    },\r\n    sms: {\r\n      type: Object,\r\n      default: null\r\n    },\r\n    accountInfo: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  components: {Template},\r\n  data() {\r\n    const validatePhone = (rule, value, callback) => {\r\n      if (!value) {\r\n        return callback(new Error('请填写手机号'));\r\n      } else if (!/^1[3456789]\\d{9}$/.test(value)) {\r\n        callback(new Error('手机号格式不正确!'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n      dialogVisible: false,\r\n      listLoading: false,\r\n      tableData: {\r\n        data: [],\r\n        total: 0\r\n      },\r\n      tableFrom: {\r\n        page: 1,\r\n        limit: 20,\r\n        status: '3',\r\n        type: 'sms'\r\n      },\r\n      columns2: [],\r\n      isSms: false, // 是否开通短信\r\n      isDump: false, // 是否开通电子面单,是否开通物流查询\r\n      isCopy: false, // 是否开通商品采集\r\n      modals: false,\r\n      loading: false,\r\n      formInlineDump: {\r\n        tempId: '',\r\n        sign: '',\r\n        com: '',\r\n        toName: '',\r\n        toTel: '',\r\n        siid: '',\r\n        toAddress: '',\r\n        type: ''\r\n      },\r\n      ruleInline: {\r\n        sign: [\r\n          { required: true, message: '请输入短信签名', trigger: 'blur' }\r\n        ],\r\n        phone: [\r\n          { required: true, validator: validatePhone, trigger: 'blur' }\r\n        ],\r\n        code: [\r\n          { required: true, message: '请输入验证码', trigger: 'blur' }\r\n        ],\r\n        com: [\r\n          { required: true, message: '请选择快递公司', trigger: 'change' }\r\n        ],\r\n        tempId: [\r\n          { required: true, message: '请选择打印模板', trigger: 'change' }\r\n        ],\r\n        toName: [\r\n          { required: true, message: '请输寄件人姓名', trigger: 'blur' }\r\n        ],\r\n        toTel: [\r\n          { required: true, validator: validatePhone, trigger: 'blur' }\r\n        ],\r\n        siid: [\r\n          { required: true, message: '请输入云打印机编号', trigger: 'blur' }\r\n        ],\r\n        toAddress: [\r\n          { required: true, message: '请输寄件人地址', trigger: 'blur' }\r\n        ]\r\n      },\r\n      tempImg: '', // 图片\r\n      exportTempList: [], // 电子面单模板\r\n      exportList: [], // 快递公司列表\r\n      formInline: {\r\n        phone: '',\r\n        code: '',\r\n        sign: ''\r\n      },\r\n      ruleInlineSign: {\r\n        sign: [\r\n          { required: true, message: '请输入短信签名', trigger: 'blur' }\r\n        ],\r\n        phone: [\r\n          { required: true, validator: validatePhone, trigger: 'blur' }\r\n        ],\r\n        code: [\r\n          { required: true, message: '请输入验证码', trigger: 'blur' }\r\n        ]\r\n      },\r\n      cutNUm: '获取验证码',\r\n      canClick: true,\r\n    }\r\n  },\r\n  watch: {\r\n    sms (n) {\r\n      if (n.open === 1) this.getList();\r\n    }\r\n  },\r\n  mounted() {\r\n    if (this.sms.open === 1) this.getList();\r\n    // if (this.isChecked === '1' && this.sms.open === 1) this.getList();\r\n  },\r\n  methods: {\r\n    editSign(){\r\n      this.formInline.account = this.accountInfo.account;\r\n      this.formInline.sign = this.accountInfo.sms.sign;\r\n      this.formInline.phone = this.accountInfo.phone;\r\n      this.dialogVisible = true;\r\n    },\r\n    //修改签名\r\n    handleSubmit:Debounce(function(name) {\r\n      this.$refs[name].validate((valid) => {\r\n        if (valid) {\r\n          smsSignApi(this.formInline).then(async res => {\r\n            this.$message.success('修改签名之后一号通需要审核过后通过!');\r\n            this.dialogVisible = false;\r\n            this.$refs[formName].resetFields();\r\n          })\r\n        } else {\r\n          return false;\r\n        }\r\n      })\r\n    }),\r\n    // 短信验证码\r\n    cutDown () {\r\n      if (this.formInline.phone) {\r\n        if (!this.canClick) return;\r\n        this.canClick = false;\r\n        this.cutNUm = 60;\r\n        let data = {\r\n          phone: this.formInline.phone,\r\n          types: 1\r\n        };\r\n        captchaApi(data).then(async res => {\r\n          this.$message.success(res.msg);\r\n        })\r\n        let time = setInterval(() => {\r\n          this.cutNUm--;\r\n          if (this.cutNUm === 0) {\r\n            this.cutNUm = '获取验证码';\r\n            this.canClick = true;\r\n            clearInterval(time)\r\n          }\r\n        }, 1000)\r\n      } else {\r\n        this.$message.warning('请填写手机号!');\r\n      }\r\n    },\r\n    handleClose(){\r\n      this.dialogVisible = false\r\n      this.$refs['formInline'].resetFields();\r\n    },\r\n    // 首页去开通\r\n    onOpenIndex (val) {\r\n      this.tableFrom.type = val;\r\n      switch (val) {\r\n        case 'sms':\r\n          this.isSms = true;\r\n          break;\r\n        case 'expr_dump':\r\n          this.openDump();\r\n          break;\r\n        default:\r\n          this.openOther();\r\n          break;\r\n      }\r\n    },\r\n    // 开通其他\r\n    openOther () {\r\n      this.$confirm(`确定开通${commFilter.onePassTypeFilter(this.tableFrom.type)}吗?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n       // this.handleSubmitDump('formInlineDump');\r\n        serviceOpenApi({type: this.tableFrom.type}).then(async res => {\r\n          this.$message.success('开通成功!');\r\n          this.getList();\r\n          this.$emit('openService')\r\n        })\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消'\r\n        })\r\n      })\r\n    },\r\n    // 开通电子面单\r\n    openDump () {\r\n      this.exportTempAllList();\r\n      this.isDump = true;\r\n    },\r\n    // 物流公司\r\n    exportTempAllList () {\r\n      expressAllApi({type:'elec'}).then(async res => {\r\n        this.exportList = res;\r\n      })\r\n    },\r\n    // 快递公司选择\r\n    onChangeExport (val) {\r\n      this.formInlineDump.tempId = '';\r\n      this.exportTemp(val);\r\n    },\r\n    // 电子面单模板\r\n    exportTemp (val) {\r\n      exportTempApi({ com: val }).then(async res => {\r\n        this.exportTempList = res.data.data || [];\r\n      })\r\n    },\r\n    onChangeImg (item) {\r\n      this.exportTempList.map(i => {\r\n        if (i.temp_id === item) this.tempImg = i.pic\r\n      })\r\n    },\r\n    handleSubmitDump (name) {\r\n      this.formInlineDump.type = this.tableFrom.type;\r\n      this.$refs[name].validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          serviceOpenApi(this.formInlineDump).then(async res => {\r\n            this.$emit('openService');\r\n            this.$message.success('开通成功!');\r\n            this.getList();\r\n            this.loading = false;\r\n          }).catch(()=>{\r\n            this.loading = false;\r\n          })\r\n        } else {\r\n          return false;\r\n        }\r\n      })\r\n    },\r\n    onChangeType () {\r\n        this.tableFrom.page = 1\r\n        this.getList()\r\n    },\r\n    // 列表\r\n    getList() {\r\n      this.listLoading = true\r\n      smsLstApi(this.tableFrom).then(res => {\r\n        this.tableData.data = res.data;\r\n        if(this.tableFrom.type == 'sms'){\r\n          let obj = new Object();\r\n          let newArr = new Array();\r\n          res.data.forEach(item=>{\r\n            obj = item;\r\n            switch(item.status) {\r\n              case 0:\r\n                  obj.status = '发送中'\r\n                  break;\r\n              case 1:\r\n                  obj.status = '成功'\r\n                  break;\r\n              case 2:\r\n                  obj.status = '失败'\r\n                  break;\r\n              case 3:\r\n                  obj.status = '全部'\r\n                  break;\r\n            }\r\n            newArr.push(obj);\r\n            this.tableData.data = newArr;\r\n          })\r\n        }\r\n        this.tableData.total = res.count\r\n        switch (this.tableFrom.type) {\r\n          case 'sms':\r\n            this.columns2 = [\r\n              {\r\n                title: '手机号',\r\n                key: 'phone',\r\n                minWidth: 100\r\n              },\r\n              {\r\n                title: '模板内容',\r\n                key: 'content',\r\n                minWidth: 590\r\n              },\r\n\r\n              {\r\n                title: '发送时间',\r\n                key: 'add_time',\r\n                minWidth: 150\r\n              },\r\n              // {\r\n              //   title: '状态',\r\n              //   key: 'status',\r\n              //   minWidth: 100\r\n              // }\r\n            ]\r\n            break;\r\n          case 'expr_dump':\r\n            this.columns2 = [\r\n              // {\r\n              //   title: '订单号',\r\n              //   key: 'order_id',\r\n              //   minWidth: 150\r\n              // },\r\n              {\r\n                title: '发货人',\r\n                key: 'from_name',\r\n                minWidth: 120\r\n              },\r\n              {\r\n                title: '收货人',\r\n                key: 'to_name',\r\n                minWidth: 120\r\n              },\r\n              {\r\n                title: '快递单号',\r\n                key: 'num',\r\n                minWidth: 120\r\n              },\r\n              {\r\n                title: '快递公司编码',\r\n                key: 'code',\r\n                minWidth: 120\r\n              },\r\n              {\r\n                title: '状态',\r\n                key: '_resultcode',\r\n                minWidth: 100\r\n              },\r\n              {\r\n                title: '打印时间',\r\n                key: 'add_time',\r\n                minWidth: 150\r\n              }\r\n            ]\r\n            break;\r\n          case 'expr_query':\r\n            this.columns2 = [\r\n              {\r\n                title: '快递单号',\r\n                key: 'content',\r\n                minWidth: 120\r\n              },\r\n              {\r\n                title: '快递公司编码',\r\n                key: 'code',\r\n                minWidth: 120\r\n              },\r\n              {\r\n                title: '状态',\r\n                key: '_resultcode',\r\n                minWidth: 120\r\n              },\r\n              {\r\n                title: '添加时间',\r\n                key: 'add_time',\r\n                minWidth: 150\r\n              }\r\n            ]\r\n            break;\r\n          default:\r\n            this.columns2 = [\r\n              {\r\n                title: '复制URL',\r\n                key: 'url',\r\n                minWidth: 400\r\n              },\r\n              {\r\n                title: '请求状态',\r\n                key: '_resultcode',\r\n                minWidth: 120\r\n              },\r\n              {\r\n                title: '添加时间',\r\n                key: 'add_time',\r\n                minWidth: 150\r\n              }\r\n            ]\r\n            break;\r\n        }\r\n        this.listLoading = false\r\n      }).catch(res => {\r\n        this.listLoading = false\r\n      })\r\n    },\r\n    pageChange(page) {\r\n      this.tableFrom.page = page\r\n      this.getList()\r\n    },\r\n    handleSizeChange(val) {\r\n      this.tableFrom.limit = val\r\n      this.getList()\r\n    }\r\n  }\r\n}\r\n", null]}