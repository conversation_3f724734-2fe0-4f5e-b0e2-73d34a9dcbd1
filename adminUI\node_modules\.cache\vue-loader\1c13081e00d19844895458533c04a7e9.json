{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Settings\\index.vue?vue&type=template&id=126b135a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Settings\\index.vue", "mtime": 1754050582337}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"div\", { staticClass: \"drawer-container\" }, [\n    _c(\"div\", [\n      _c(\"h3\", { staticClass: \"drawer-title\" }, [_vm._v(\"主题风格设置\")]),\n      _vm._v(\" \"),\n      _c(\n        \"div\",\n        { staticClass: \"drawer-item\" },\n        [\n          _c(\"span\", [_vm._v(\"主题颜色\")]),\n          _vm._v(\" \"),\n          _c(\"theme-picker\", {\n            staticStyle: {\n              float: \"right\",\n              height: \"26px\",\n              margin: \"-3px 8px 0 0\",\n            },\n            on: { change: _vm.themeChange },\n          }),\n          _vm._v(\" \"),\n          _vm.topNav\n            ? _c(\n                \"div\",\n                { staticClass: \"drawer-item\" },\n                [\n                  _c(\"span\", [_vm._v(\"开启 Icon\")]),\n                  _vm._v(\" \"),\n                  _c(\"el-switch\", {\n                    staticClass: \"drawer-switch\",\n                    model: {\n                      value: _vm.navIcon,\n                      callback: function ($$v) {\n                        _vm.navIcon = $$v\n                      },\n                      expression: \"navIcon\",\n                    },\n                  }),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"drawer-item\" },\n            [\n              _c(\"span\", [_vm._v(\"开启 Tags-Views\")]),\n              _vm._v(\" \"),\n              _c(\"el-switch\", {\n                staticClass: \"drawer-switch\",\n                model: {\n                  value: _vm.tagsView,\n                  callback: function ($$v) {\n                    _vm.tagsView = $$v\n                  },\n                  expression: \"tagsView\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"drawer-item\" },\n            [\n              _c(\"span\", [_vm._v(\"固定 Header\")]),\n              _vm._v(\" \"),\n              _c(\"el-switch\", {\n                staticClass: \"drawer-switch\",\n                model: {\n                  value: _vm.fixedHeader,\n                  callback: function ($$v) {\n                    _vm.fixedHeader = $$v\n                  },\n                  expression: \"fixedHeader\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"drawer-item\" },\n            [\n              _c(\"span\", [_vm._v(\"显示 Logo\")]),\n              _vm._v(\" \"),\n              _c(\"el-switch\", {\n                staticClass: \"drawer-switch\",\n                model: {\n                  value: _vm.sidebarLogo,\n                  callback: function ($$v) {\n                    _vm.sidebarLogo = $$v\n                  },\n                  expression: \"sidebarLogo\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\"el-divider\"),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              attrs: {\n                size: \"small\",\n                type: \"primary\",\n                plain: \"\",\n                icon: \"el-icon-document-add\",\n              },\n              on: { click: _vm.saveSetting },\n            },\n            [_vm._v(\"保存配置\")]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              attrs: { size: \"small\", plain: \"\", icon: \"el-icon-refresh\" },\n              on: { click: _vm.resetSetting },\n            },\n            [_vm._v(\"重置配置\")]\n          ),\n        ],\n        1\n      ),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}