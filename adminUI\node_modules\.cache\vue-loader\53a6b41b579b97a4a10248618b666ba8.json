{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\setting\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\setting\\index.vue", "mtime": 1754050582515}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport parser from '@/components/FormGenerator/components/parser/Parser'\r\nimport * as categoryApi from '@/api/categoryApi.js'\r\nimport * as selfUtil from '@/utils/ZBKJIutil.js'\r\nimport * as systemFormConfigApi from '@/api/systemFormConfig.js'\r\nimport * as systemSettingApi from '@/api/systemSetting.js'\r\nimport * as systemConfigApi from '@/api/systemConfig.js'\r\nimport Template from \"@/views/appSetting/wxAccount/wxTemplate/index\";\r\nimport {beautifierConf} from \"@/components/FormGenerator/utils\";\r\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\r\nimport {Debounce} from '@/utils/validate'\r\nexport default {\r\n  // name: \"index\",\r\n  components: {Template, parser },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      formConf: { content: { fields: [] }, id: null, render: false, isEdit: false },\r\n      formConfChild: { content: { fields: [] }, id: null, render: false, isEdit: false },\r\n      activeNamel1: null,\r\n      activeNamel2: '',//针对文件特殊处理\r\n      treeList: [],\r\n      editDataChild: {},\r\n      isCreate: 0,\r\n      currentEditId: null,\r\n      currentEditData: null,\r\n      currentSelectedUploadFlag:null,\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handlerGetTreeList()\r\n    this.getCurrentUploadSelectedFlag()\r\n  },\r\n  methods: {\r\n    checkPermi,\r\n    handleTabClick(tab) {\r\n      this.activeNamel2 = tab.$children[0].panes[0].name;\r\n      this.handlerGetLevel2FormConfig(this.activeNamel2);\r\n    },\r\n    handlerGetLevel1FormConfig(id) {\r\n      const formPram = { id: id }\r\n      this.currentEditId = id\r\n      this.formConf.content = { fields: [] }\r\n      this.formConf.render = false\r\n      this.loading = true\r\n      systemFormConfigApi.getFormConfigInfo(formPram).then(data => {\r\n        const { id, name, info, content } = data\r\n        this.formConf.content = JSON.parse(content)\r\n        this.formConf.id = id\r\n        this.handlerGetSettingInfo(id, 1)\r\n        this.loading = false\r\n      }).catch(() =>{\r\n        this.loading = false\r\n      })\r\n    },\r\n    handleItemTabClick(tab, event) { //这里对tabs=tab.name和radio=id做了兼容\r\n      let _id = tab.name ? tab.name : tab\r\n      if(!_id) return this.$message.error('表单配置不正确，请关联正确表单后使用')\r\n      this.handlerGetLevel2FormConfig(_id)\r\n    },\r\n    handlerGetLevel2FormConfig(id) {\r\n      const formPram = { id: id }\r\n      this.currentEditId = id\r\n      this.formConfChild.content = { fields: [] }\r\n      this.formConfChild.render = false\r\n      this.loading = true\r\n      systemFormConfigApi.getFormConfigInfo(formPram).then(data => {\r\n        const { id, name, info, content } = data\r\n        this.formConfChild.content = JSON.parse(content)\r\n        this.formConfChild.id = id\r\n        this.handlerGetSettingInfo(id, 2)\r\n        this.loading = false\r\n      }).catch(() =>{\r\n        this.loading = false\r\n      })\r\n    },\r\n    handlerGetSettingInfo(id, level) {\r\n      systemSettingApi.systemConfigInfo({ id: id }).then(data => {\r\n        this.currentEditData = data\r\n        if (level === 1) {\r\n          this.formConf.isEdit = this.currentEditData !== null\r\n          this.formConf.render = true\r\n        } else {\r\n          this.formConfChild.isEdit = this.currentEditData !== null\r\n          this.formConfChild.render = true\r\n        }\r\n      })\r\n    },\r\n    handlerSubmit:Debounce(function(formValue) {\r\n      this.handlerSave(formValue)\r\n    }),\r\n    handlerSave(formValue) {\r\n      const _pram = this.buildFormPram(formValue)\r\n      let _formId = 0\r\n      systemSettingApi.systemConfigSave(_pram).then(data => {\r\n        this.$message.success('添加数据成功')\r\n      })\r\n    },\r\n    handlerGetTreeList() {\r\n      const _pram = { type: this.$constants.categoryType[5].value, status: 1 }\r\n      this.loading = true\r\n      categoryApi.treeCategroy(_pram).then(data => {\r\n        this.treeList = this.handleAddArrt(data)\r\n        if (this.treeList.length > 0) this.activeNamel1 = this.treeList[0].id.toString();\r\n        if (this.treeList.length > 0 && this.treeList[0].child.length > 0) {\r\n          this.activeNamel2 = this.treeList[0].child[0].extra\r\n        }\r\n        if (this.activeNamel2) {\r\n          this.handlerGetLevel2FormConfig(this.treeList[0].child[0].extra)\r\n        }\r\n       // else {\r\n        //  this.handlerGetLevel1FormConfig(this.treeList[0].extra)\r\n        //}\r\n        this.loading = false\r\n      }).catch(() =>{\r\n        this.loading = false\r\n      })\r\n    },\r\n    handleAddArrt(treeData) {\r\n      // let _result = this.addTreeListLabel(treeData)\r\n      const _result = selfUtil.addTreeListLabel(treeData)\r\n      return _result\r\n    },\r\n    buildFormPram(formValue) {\r\n      const _pram = {\r\n        fields: [],\r\n        id: this.currentEditId,\r\n        sort: 0, // 参数暂时无用\r\n        status: true // 参数暂时无用\r\n      }\r\n      const _fields = []\r\n      Object.keys(formValue).forEach((key) => {\r\n        _fields.push({\r\n          name: key,\r\n          title: key,\r\n          value: formValue[key]\r\n        })\r\n      })\r\n      _pram.fields = _fields\r\n      return _pram\r\n    },\r\n    getCurrentUploadSelectedFlag(){\r\n      systemConfigApi.configGetUniq({key:\"uploadType\"}).then(data => {\r\n        this.currentSelectedUploadFlag = parseInt(data)\r\n      })\r\n    }\r\n  }\r\n}\r\n", null]}