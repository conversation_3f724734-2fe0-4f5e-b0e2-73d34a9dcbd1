{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\deliveryAddress\\addPoint.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\deliveryAddress\\addPoint.vue", "mtime": 1754050582507}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { storeSaveApi, storeInfoApi, storeUpdateApi } from '@/api/storePoint';\nimport * as logistics from '@/api/logistics'\nimport { configInfo } from '@/api/systemConfig';\nimport Templates from \"../../../../appSetting/wxAccount/wxTemplate/index\";\nimport {Debounce} from '@/utils/validate'\nexport default {\n  name: \"index\",\n  components: {Templates},\n  // props: {\n  //   children: 'child',\n  //   label: 'name',\n  //   value: 'name'\n  // },\n  data() {\n    const validatePhone = (rule, value, callback) => {\n      if (!value) {\n        return callback(new Error('请填写手机号'));\n      } else if (!/^1[3456789]\\d{9}$/.test(value)) {\n        callback(new Error('手机号格式不正确!'));\n      } else {\n        callback();\n      }\n    };\n    const validateUpload = (rule, value, callback) => {\n      if (!this.ruleForm.image) {\n        callback(new Error('请上传提货点logo'))\n      } else {\n        callback()\n      }\n    };\n    return {\n      loading: false,\n      dialogFormVisible: false,\n      modalMap: false,\n      keyUrl: '',\n      addresData:[],\n      ruleForm: {\n        name: '',\n        introduction: '',\n        phone: '',\n        address: '',\n        detailedAddress: '',\n        dayTime: '',\n        image:'',\n        latitude: ''\n      },\n      id:0,\n      dayTime:['',''],\n      rules: {\n        name: [\n          { required: true, message: '请输入提货点名称', trigger: 'blur' }\n        ],\n        address: [\n          { required: true, message: '请选择提货点地址', trigger: 'change' }\n        ],\n        dayTime: [\n          { required: true, type: 'array', message: '请选择提货点营业时间', trigger: 'change' }\n        ],\n        phone: [\n          { required: true, validator: validatePhone, trigger: 'blur' }\n        ],\n        detailedAddress: [\n          { required: true, message: '请输入详细地址', trigger: 'blur' }\n        ],\n        image: [\n          { required: true, validator: validateUpload, trigger: 'change' }\n        ],\n        latitude: [\n          { required: true, message: '请选择经纬度', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  created(){\n    this.ruleForm.image = '';\n    let cityList = JSON.parse(sessionStorage.getItem('cityList'));\n    this.addresData = cityList;\n    this.getCityList();\n    this.getKey();\n  },\n  mounted(){\n    window.addEventListener('message', function (event) {\n      // 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息\n      var loc = event.data;\n      if (loc && loc.module === 'locationPicker') { // 防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'\n        window.parent.selectAdderss(loc);\n      }\n    }, false);\n    window.selectAdderss = this.selectAdderss;\n  },\n  methods:{\n    //详情\n    getInfo (id) {\n      let that = this;\n      that.id = id;\n      this.loading = true;\n      storeInfoApi({id:id}).then(res=>{\n        that.ruleForm = res;\n        that.ruleForm.address = res.address.split(\",\");\n        that.dayTime = res.dayTime.split(\",\")\n        this.loading = false;\n      })\n    },\n    //取消\n    cancel (){\n      this.dialogFormVisible = false;\n      this.clearFrom();\n      this.ruleForm.image = '';\n      this.resetForm('ruleForm');\n      this.id = 0\n    },\n    //重置\n    resetForm (name) {\n      this.$refs[name].resetFields();\n    },\n    // 提交\n    submitForm:Debounce(function(name) {\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          storeSaveApi(this.ruleForm).then(async () => {\n            this.$message.success('提交成功');\n            this.dialogFormVisible = false;\n            this.$parent.tableList();\n            this.$parent.storeGetCount();\n            this.clearFrom();\n            this.resetForm(name);\n            this.id = 0;\n          })\n        } else {\n          return false;\n        }\n      })\n    }),\n    //编辑\n    editForm:Debounce(function(name){\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          this.handleChange(this.ruleForm.address);\n          storeUpdateApi(this.ruleForm,this.id).then(async () => {\n            this.$message.success('编辑成功');\n            this.dialogFormVisible = false;\n            this.$parent.tableList();\n            this.clearFrom();\n            this.resetForm(name);\n            this.id = 0;\n          })\n        } else {\n          return false;\n        }\n      })\n    }),\n    //数据归为初始状态\n    clearFrom(){\n      this.ruleForm.introduction = '';\n      this.dayTime = ['',''];\n    },\n    //确认省市区\n    handleChange(e){\n      let province = e[0];\n      let city = e[1];\n      let area = e[2];\n      if(e.length===2){\n        this.ruleForm.address = province + ',' + city;\n      }else if(e.length===3){\n        this.ruleForm.address = province + ',' + city + ',' + area;\n      }\n    },\n    //营业时间\n    onchangeTime(e){\n      this.ruleForm.dayTime = e ? e.join(',') : '';\n    },\n    //上传图片\n    modalPicTap (tit) {\n      const _this = this\n      this.$modalUpload(function(img) {\n        _this.ruleForm.image = img[0].sattDir\n      },tit, 'system')\n    },\n    //查找位置\n    onSearch () {\n      this.modalMap = true;\n    },\n    // 选择经纬度\n    selectAdderss (data) {\n      this.ruleForm.latitude = data.latlng.lat + ',' + data.latlng.lng;\n      this.modalMap = false;\n    },\n    // key值\n    getKey () {\n      const _pram = { id: 74 };\n      configInfo(_pram).then(async res => {\n        let keys = res.tengxun_map_key;\n        this.keyUrl = `https://apis.map.qq.com/tools/locpicker?type=1&key=${keys}&referer=myapp`;\n      })\n    },\n    getCityList() {\n      logistics.cityListTree().then(res => {\n        sessionStorage.setItem('cityList',JSON.stringify(res));\n        let cityList = JSON.parse(sessionStorage.getItem('cityList'));\n        this.addresData = cityList;\n      }).catch(res => {\n        this.$message.error(res.message)\n      })\n    },\n  }\n}\n", null]}