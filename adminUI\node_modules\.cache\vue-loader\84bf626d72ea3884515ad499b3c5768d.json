{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\TagsView\\ScrollPane.vue", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\TagsView\\ScrollPane.vue", "mtime": 1754050582341}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754554385233}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./ScrollPane.vue?vue&type=template&id=be6b7bae&scoped=true\"\nimport script from \"./ScrollPane.vue?vue&type=script&lang=js\"\nexport * from \"./ScrollPane.vue?vue&type=script&lang=js\"\nimport style0 from \"./ScrollPane.vue?vue&type=style&index=0&id=be6b7bae&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"be6b7bae\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\shop\\\\adminUI\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('be6b7bae')) {\n      api.createRecord('be6b7bae', component.options)\n    } else {\n      api.reload('be6b7bae', component.options)\n    }\n    module.hot.accept(\"./ScrollPane.vue?vue&type=template&id=be6b7bae&scoped=true\", function () {\n      api.rerender('be6b7bae', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/layout/components/TagsView/ScrollPane.vue\"\nexport default component.exports"]}