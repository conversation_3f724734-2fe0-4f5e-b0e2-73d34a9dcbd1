{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\HeaderSearch\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\HeaderSearch\\index.vue", "mtime": 1754050582266}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _fuse = _interopRequireDefault(require(\"fuse.js\"));\nvar _path = _interopRequireDefault(require(\"path\"));\nvar _vuex = require(\"vuex\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); } //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// fuse is a lightweight fuzzy-search module\n// make search results more in line with expectations\nvar _default = exports.default = {\n  name: 'HeaderSearch',\n  data: function data() {\n    return {\n      search: '',\n      options: [],\n      searchPool: [],\n      show: false,\n      fuse: undefined\n    };\n  },\n  computed: _objectSpread({}, (0, _vuex.mapGetters)(['permission_routes'])),\n  watch: {\n    routes: function routes(n) {\n      this.searchPool = this.generateRoutes(this.permission_routes);\n    },\n    searchPool: function searchPool(list) {\n      this.initFuse(list);\n    },\n    show: function show(value) {\n      if (value) {\n        document.body.addEventListener('click', this.close);\n      } else {\n        document.body.removeEventListener('click', this.close);\n      }\n    }\n  },\n  mounted: function mounted() {\n    this.searchPool = this.generateRoutes(this.permission_routes);\n  },\n  methods: {\n    click: function click() {\n      this.show = !this.show;\n      if (this.show) {\n        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus();\n      }\n    },\n    close: function close() {\n      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur();\n      this.options = [];\n      this.show = false;\n    },\n    change: function change(val) {\n      var _this = this;\n      this.$router.push(val.path);\n      this.search = '';\n      this.options = [];\n      this.$nextTick(function () {\n        _this.show = false;\n      });\n    },\n    initFuse: function initFuse(list) {\n      this.fuse = new _fuse.default(list, {\n        shouldSort: true,\n        threshold: 0.4,\n        location: 0,\n        distance: 100,\n        maxPatternLength: 32,\n        minMatchCharLength: 1,\n        keys: [{\n          name: 'name',\n          weight: 0.7\n        }, {\n          name: 'url',\n          weight: 0.3\n        }]\n      });\n    },\n    // Filter out the routes that can be displayed in the sidebar\n    // And generate the internationalized title\n    generateRoutes: function generateRoutes(routes) {\n      var basePath = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '/';\n      var prefixTitle = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n      var res = [];\n      var _iterator = _createForOfIteratorHelper(routes),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var router = _step.value;\n          // skip hidden router\n          if (router.hidden) {\n            continue;\n          }\n          var data = {\n            path: _path.default.resolve(basePath, router.url),\n            name: _toConsumableArray(prefixTitle),\n            children: router.child || []\n          };\n          if (router.name) {\n            data.name = [].concat(_toConsumableArray(data.name), [router.name]);\n            if (router.redirect !== 'noRedirect') {\n              // only push the routes with title\n              // special case: need to exclude parent router without redirect\n              res.push(data);\n            }\n          }\n\n          // recursive child routes\n          if (router.child) {\n            var tempRoutes = this.generateRoutes(router.child, data.url, data.name);\n            if (tempRoutes.length >= 1) {\n              res = [].concat(_toConsumableArray(res), _toConsumableArray(tempRoutes));\n            }\n          }\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n      return res;\n    },\n    querySearch: function querySearch(query) {\n      if (query !== '') {\n        this.options = this.fuse.search(query);\n      } else {\n        this.options = [];\n      }\n    }\n  }\n};", null]}