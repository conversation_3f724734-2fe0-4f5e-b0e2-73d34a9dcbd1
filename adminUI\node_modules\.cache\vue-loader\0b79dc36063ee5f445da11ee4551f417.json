{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\integral\\integralLog\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\integral\\integralLog\\index.vue", "mtime": 1754050582451}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { integralListApi } from '@/api/marketing'\nimport cardsData from '@/components/cards/index'\nexport default {\n  components: { cardsData },\n  data() {\n    return {\n      loading: false,\n      options: [],\n      fromList: this.$constants.fromList,\n      listLoading: false,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        dateLimit: '',\n        keywords: ''\n      },\n      userIdList: [],\n      userList: [],\n      timeVal: [],\n      values: []\n    }\n  },\n  mounted() {\n    this.getList()\n    // this.getUserList()\n  },\n  methods: {\n    seachList() {\n      this.tableFrom.page = 1\n      this.getList()\n    },\n    // 选择时间\n    selectChange (tab) {\n      this.tableFrom.dateLimit = tab\n      this.tableFrom.page = 1\n      this.timeVal = [];\n      this.getList();\n    },\n    // 具体日期\n    onchangeTime (e) {\n      this.timeVal = e;\n      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : ''\n      this.tableFrom.page = 1\n      this.getList();\n    },\n    // 列表\n    getList() {\n      this.listLoading = true\n      integralListApi({ limit:this.tableFrom.limit, page: this.tableFrom.page}, this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.listLoading = false\n      }).catch(res => {\n        this.listLoading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      this.getList()\n    },\n  }\n}\n", null]}