{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\integral\\integralLog\\index.vue?vue&type=template&id=63da5f0a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\integral\\integralLog\\index.vue", "mtime": 1754050582451}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <div class=\"container\">\n        <el-form size=\"small\" label-width=\"120px\">\n          <el-form-item label=\"时间选择：\" class=\"width100\">\n            <el-radio-group v-model=\"tableFrom.dateLimit\" type=\"button\" class=\"mr20\" size=\"small\"\n                            @change=\"selectChange(tableFrom.dateLimit)\">\n              <el-radio-button v-for=\"(item,i) in fromList.fromTxt\" :key=\"i\" :label=\"item.val\">{{ item.text }}\n              </el-radio-button>\n            </el-radio-group>\n            <el-date-picker v-model=\"timeVal\" value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\" size=\"small\"\n                            type=\"daterange\" placement=\"bottom-end\" placeholder=\"自定义时间\" style=\"width: 250px;\"\n                            @change=\"onchangeTime\"/>\n          </el-form-item>\n          <el-form-item label=\"用户微信昵称：\">\n            <el-input v-model=\"tableFrom.keywords\" placeholder=\"请输入用户昵称\" class=\"selWidth\" size=\"small\">\n              <el-button slot=\"append\" icon=\"el-icon-search\" size=\"small\" @click=\"getList(1)\" />\n            </el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n      <!--<cards-data :cardLists=\"cardLists\"></cards-data>-->\n    </div>\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      size=\"small\"\n      class=\"table\"\n      highlight-current-row\n      :header-cell-style=\" {fontWeight:'bold'}\"\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        width=\"60\"\n      />\n      <el-table-column\n        prop=\"title\"\n        label=\"标题\"\n        min-width=\"130\"\n      />\n      <el-table-column\n        sortable\n        prop=\"balance\"\n        label=\"积分余量\"\n        min-width=\"120\"\n        :sort-method=\"(a,b)=>{return a.balance - b.balance}\"\n      />\n      <el-table-column\n        sortable\n        label=\"明细数字\"\n        min-width=\"120\"\n        prop=\"integral\"\n        :sort-method=\"(a,b)=>{return a.integral - b.integral}\"\n      />\n      <el-table-column\n        label=\"备注\"\n        min-width=\"120\"\n        prop=\"mark\"\n      />\n      <el-table-column\n        label=\"用户昵称\"\n        min-width=\"120\"\n        prop=\"nickName\"\n      />\n      <el-table-column\n        prop=\"updateTime\"\n        label=\"\t添加时间\"\n        min-width=\"150\"\n      />\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n</div>\n", null]}