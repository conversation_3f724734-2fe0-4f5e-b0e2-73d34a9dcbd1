{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\adminList\\edit.vue?vue&type=template&id=61895a6d&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\adminList\\edit.vue", "mtime": 1754050582500}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div>\n  <el-form\n    ref=\"pram\"\n    :model=\"pram\"\n    :rules=\"rules\"\n    label-width=\"100px\"\n    @submit.native.prevent\n  >\n    <el-form-item :label=\"$t('admin.system.admin.account')\" prop=\"account\">\n      <el-input\n        v-model=\"pram.account\"\n        :placeholder=\"$t('admin.system.admin.account')\"\n      />\n    </el-form-item>\n    <el-form-item :label=\"$t('admin.system.admin.pwd')\" prop=\"pwd\">\n      <el-input\n        v-model=\"pram.pwd\"\n        :placeholder=\"$t('admin.system.admin.pwd')\"\n        clearable\n        @input=\"handlerPwdInput\"\n        @clear=\"handlerPwdInput\"\n      />\n    </el-form-item>\n    <el-form-item\n      v-if=\"pram.pwd\"\n      :label=\"$t('admin.system.admin.repwd')\"\n      prop=\"repwd\"\n    >\n      <el-input\n        v-model=\"pram.repwd\"\n        :placeholder=\"$t('admin.system.admin.repwd')\"\n        clearable\n      />\n    </el-form-item>\n    <el-form-item :label=\"$t('admin.system.admin.realName')\" prop=\"realName\">\n      <el-input\n        v-model=\"pram.realName\"\n        :placeholder=\"$t('admin.system.admin.realName')\"\n      />\n    </el-form-item>\n    <el-form-item :label=\"$t('admin.system.admin.roles')\" prop=\"roles\">\n      <el-select\n        v-model=\"pram.roles\"\n        :placeholder=\"$t('admin.system.admin.roles')\"\n        clearable\n        multiple\n        style=\"width: 100%\"\n      >\n        <el-option\n          v-for=\"(item, index) in roleList.list\"\n          :key=\"index\"\n          :label=\"item.roleName\"\n          :value=\"item.id\"\n        />\n      </el-select>\n    </el-form-item>\n    <el-form-item :label=\"$t('admin.system.admin.phone')\" prop=\"phone\">\n      <el-input\n        type=\"text\"\n        v-model=\"pram.phone\"\n        prefix=\"ios-contact-outline\"\n        :placeholder=\"$t('admin.system.admin.phone')\"\n        size=\"large\"\n      />\n    </el-form-item>\n    <el-form-item :label=\"$t('common.status')\">\n      <el-switch\n        v-model=\"pram.status\"\n        :active-value=\"true\"\n        :inactive-value=\"false\"\n      />\n    </el-form-item>\n    <el-form-item>\n      <el-button\n        type=\"primary\"\n        @click=\"handlerSubmit('pram')\"\n        v-hasPermi=\"['admin:system:admin:update', 'admin:system:admin:save']\"\n        >{{\n          isCreate === 0 ? $t(\"common.confirm\") : $t(\"common.update\")\n        }}</el-button\n      >\n      <el-button @click=\"close\">{{ $t(\"common.cancel\") }}</el-button>\n    </el-form-item>\n  </el-form>\n</div>\n", null]}