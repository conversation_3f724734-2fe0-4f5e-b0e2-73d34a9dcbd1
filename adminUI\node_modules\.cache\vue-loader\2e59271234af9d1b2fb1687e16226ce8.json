{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\FormGenerator\\index\\IconsDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\FormGenerator\\index\\IconsDialog.vue", "mtime": 1754050582252}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport iconList from '../utils/icon.json'\r\n\r\nconst originList = iconList.map(name => `el-icon-${name}`)\r\n\r\nexport default {\r\n  inheritAttrs: false,\r\n  props: ['current'],\r\n  data() {\r\n    return {\r\n      iconList: originList,\r\n      active: null,\r\n      key: ''\r\n    }\r\n  },\r\n  watch: {\r\n    key(val) {\r\n      if (val) {\r\n        this.iconList = originList.filter(name => name.indexOf(val) > -1)\r\n      } else {\r\n        this.iconList = originList\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    onOpen() {\r\n      this.active = this.current\r\n      this.key = ''\r\n    },\r\n    onClose() {},\r\n    onSelect(icon) {\r\n      this.active = icon\r\n      this.$emit('select', icon)\r\n      this.$emit('update:visible', false)\r\n    }\r\n  }\r\n}\r\n", null]}