{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\deliveryAddress\\index.vue?vue&type=template&id=411641cd&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\deliveryAddress\\index.vue", "mtime": 1754050582508}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <el-tabs v-model=\"artFrom.status\" @tab-click=\"onClickTab\" v-if=\"checkPermi(['admin:system:store:count','admin:system:store:list'])\" >\n        <el-tab-pane :label=\"'显示中的提货点('+ headerCount.show +')'\" name=\"1\"></el-tab-pane>\n        <el-tab-pane :label=\"'隐藏中的提货点('+ headerCount.hide +')'\" name=\"0\"></el-tab-pane>\n        <el-tab-pane :label=\"'回收站的提货点('+ headerCount.recycle +')'\" name=\"2\"></el-tab-pane>\n      </el-tabs>\n      <el-form ref=\"form\" inline :model=\"artFrom\" @submit.native.prevent>\n        <el-form-item label=\"关键字：\">\n          <el-input v-model=\"artFrom.keywords\" placeholder=\"请输入提货点名称/电话\" class=\"selWidth\" size=\"small\" clearable>\n            <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"search\" />\n          </el-input>\n        </el-form-item>\n      </el-form>\n      <el-button type=\"primary\" size=\"small\" @click=\"add\" v-hasPermi=\"['admin:system:store:save']\">添加提货点</el-button>\n    </div>\n    <el-table\n      v-loading=\"loading\"\n      size=\"small\"\n      :data=\"tableData\"\n      :header-cell-style=\" {fontWeight:'bold'}\">\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        min-width=\"80\">\n      </el-table-column>\n      <el-table-column\n        prop=\"image\"\n        label=\"提货点图片\"\n        min-width=\"100\">\n        <template slot-scope=\"{ row, index }\" class=\"picMiddle\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"row.image\"\n              :preview-src-list=\"[row.image]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"name\"\n        label=\"提货点名称\"\n        min-width=\"150\">\n      </el-table-column>\n      <el-table-column\n        prop=\"phone\"\n        label=\"提货点电话\"\n        min-width=\"100\">\n      </el-table-column>\n      <el-table-column\n        prop=\"detailedAddress\"\n        label=\"地址\"\n        min-width=\"100\">\n      </el-table-column>\n      <el-table-column\n        prop=\"dayTime\"\n        label=\"营业时间\"\n        min-width=\"180\">\n      </el-table-column>\n      <el-table-column\n        prop=\"isShow\"\n        label=\"是否显示\"\n        min-width=\"100\">\n        <template slot-scope=\"{ row, index }\" v-if=\"checkPermi(['admin:system:store:update:status'])\">\n          <el-switch\n            :active-value=\"true\"\n            :inactive-value=\"false\"\n            active-text=\"显示\"\n            inactive-text=\"隐藏\"\n            v-model=\"row.isShow\"\n            @change=\"onchangeIsShow(row.id,row.isShow)\">\n          </el-switch>\n        </template>\n      </el-table-column>\n      <el-table-column\n        fixed=\"right\"\n        label=\"操作\"\n        min-width=\"120\">\n        <template slot-scope=\"{ row, index }\">\n          <el-button type=\"text\" size=\"small\" @click=\"edit(row.id)\" v-hasPermi=\"['admin:system:store:info']\">编辑</el-button>\n          <el-divider direction=\"vertical\"></el-divider>\n          <el-button v-if=\"artFrom.status==='2'\" type=\"text\" size=\"small\" @click=\"storeRecovery(row.id)\" v-hasPermi=\"['admin:system:store:recovery']\">恢复</el-button>\n          <el-divider v-if=\"artFrom.status==='2'\" direction=\"vertical\"></el-divider>\n          <el-button type=\"text\" size=\"small\" @click=\"artFrom.status==='2'?allDelete(row.id):storeDelete(row.id)\" v-hasPermi=\"['admin:system:store:delete','admin:system:store:completely:delete']\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination\n      class=\"mt20\"\n      @size-change=\"sizeChange\"\n      @current-change=\"pageChange\"\n      :current-page=\"artFrom.page\"\n      :page-sizes=\"[20, 40, 60, 100]\"\n      :page-size=\"artFrom.limit\"\n      layout=\"total, sizes, prev, pager, next, jumper\"\n      :total=\"total\">\n    </el-pagination>\n  </el-card>\n  <system-store ref=\"template\"></system-store>\n</div>\n", null]}