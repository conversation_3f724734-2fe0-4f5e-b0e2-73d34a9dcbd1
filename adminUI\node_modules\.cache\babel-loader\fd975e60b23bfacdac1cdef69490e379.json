{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\adminList\\edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\adminList\\edit.vue", "mtime": 1754050582500}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar roleApi = _interopRequireWildcard(require(\"@/api/role.js\"));\nvar systemAdminApi = _interopRequireWildcard(require(\"@/api/systemadmin.js\"));\nvar _validate = require(\"@/utils/validate\");\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; } //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default2 = exports.default = {\n  // name: \"edit\"\n  components: {},\n  props: {\n    isCreate: {\n      type: Number,\n      required: true\n    },\n    editData: {\n      type: Object,\n      default: function _default() {\n        return {\n          rules: []\n        };\n      }\n    }\n  },\n  data: function data() {\n    var _this = this;\n    var validatePhone = function validatePhone(rule, value, callback) {\n      if (!value) {\n        return callback(new Error(_this.$t(\"admin.system.admin.validatePhone.required\")));\n      } else if (!/^1[3456789]\\d{9}$/.test(value)) {\n        callback(new Error(_this.$t(\"admin.system.admin.validatePhone.formatError\")));\n      } else {\n        callback();\n      }\n    };\n    var validatePass = function validatePass(rule, value, callback) {\n      if (value === \"\") {\n        callback(new Error(_this.$t(\"admin.system.admin.validatePass.required\")));\n      } else if (value !== _this.pram.pwd) {\n        callback(new Error(_this.$t(\"admin.system.admin.validatePass.notMatch\")));\n      } else {\n        callback();\n      }\n    };\n    return {\n      constants: this.$constants,\n      pram: {\n        account: null,\n        level: null,\n        pwd: null,\n        repwd: null,\n        realName: null,\n        roles: [],\n        status: null,\n        id: null,\n        phone: null\n      },\n      roleList: [],\n      rules: {\n        account: [{\n          required: true,\n          message: this.$t(\"admin.system.admin.validateAccount.required\"),\n          trigger: [\"blur\", \"change\"]\n        }],\n        pwd: [{\n          required: true,\n          message: this.$t(\"admin.system.admin.validatePassword.required\"),\n          trigger: [\"blur\", \"change\"]\n        }],\n        repwd: [{\n          required: true,\n          message: this.$t(\"admin.system.admin.validateConfirmPassword.required\"),\n          validator: validatePass,\n          trigger: [\"blur\", \"change\"]\n        }],\n        realName: [{\n          required: true,\n          message: this.$t(\"admin.system.admin.validateRealName.required\"),\n          trigger: [\"blur\", \"change\"]\n        }],\n        roles: [{\n          required: true,\n          message: this.$t(\"admin.system.admin.validateRoles.required\"),\n          trigger: [\"blur\", \"change\"]\n        }],\n        phone: [{\n          required: true,\n          message: this.$t(\"admin.system.admin.validatePhone.required\"),\n          trigger: [\"blur\", \"change\"]\n        }]\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.initEditData();\n    this.handleGetRoleList();\n  },\n  methods: {\n    close: function close() {\n      this.$emit(\"hideEditDialog\");\n    },\n    handleGetRoleList: function handleGetRoleList() {\n      var _this2 = this;\n      var _pram = {\n        page: 1,\n        limit: this.constants.page.limit[4],\n        status: 1\n      };\n      roleApi.getRoleList(_pram).then(function (data) {\n        _this2.roleList = data;\n        var arr = [];\n        data.list.forEach(function (item) {\n          arr.push(item.id);\n        });\n        if (!arr.includes(Number.parseInt(_this2.pram.roles))) {\n          _this2.$set(_this2.pram, \"roles\", []);\n        }\n      });\n    },\n    initEditData: function initEditData() {\n      if (this.isCreate !== 1) return;\n      var _this$editData = this.editData,\n        account = _this$editData.account,\n        realName = _this$editData.realName,\n        roles = _this$editData.roles,\n        level = _this$editData.level,\n        status = _this$editData.status,\n        id = _this$editData.id,\n        phone = _this$editData.phone;\n      this.pram.account = account;\n      this.pram.realName = realName;\n      var _roles = [];\n      if (roles.length > 0 && !roles.includes(\",\")) {\n        //如果权限id集合有长度并且是只有一个，就将它Push进_roles这个数组\n        _roles.push(Number.parseInt(roles));\n      } else {\n        //否则就将多个id集合解构以后push进roles并且转换为整型\n        _roles.push.apply(_roles, _toConsumableArray(roles.split(\",\").map(function (item) {\n          return Number.parseInt(item);\n        })));\n      }\n      this.pram.roles = _roles;\n      this.pram.status = status;\n      this.pram.id = id;\n      this.pram.phone = phone;\n      this.rules.pwd = [];\n      this.rules.repwd = [];\n    },\n    handlerSubmit: (0, _validate.Debounce)(function (form) {\n      var _this3 = this;\n      this.$refs[form].validate(function (valid) {\n        if (!valid) return;\n        if (_this3.isCreate === 0) {\n          _this3.handlerSave();\n        } else {\n          _this3.handlerEdit();\n        }\n      });\n    }),\n    handlerSave: function handlerSave() {\n      var _this4 = this;\n      systemAdminApi.adminAdd(this.pram).then(function (data) {\n        _this4.$message.success(_this4.$t(\"admin.system.admin.message.createSuccess\"));\n        _this4.$emit(\"hideEditDialog\");\n      });\n    },\n    handlerEdit: function handlerEdit() {\n      var _this5 = this;\n      this.pram.roles = this.pram.roles.join(\",\");\n      systemAdminApi.adminUpdate(this.pram).then(function (data) {\n        _this5.$message.success(_this5.$t(\"admin.system.admin.message.updateSuccess\"));\n        _this5.$emit(\"hideEditDialog\");\n      });\n    },\n    rulesSelect: function rulesSelect(selectKeys) {\n      this.pram.rules = selectKeys;\n    },\n    handlerPwdInput: function handlerPwdInput(val) {\n      var _this6 = this;\n      if (!val) {\n        this.rules.pwd = [];\n        this.rules.repwd = [];\n        return;\n      }\n      this.rules.pwd = [{\n        required: true,\n        message: this.$t(\"admin.system.admin.validatePassword.required\"),\n        trigger: [\"blur\", \"change\"]\n      }, {\n        min: 6,\n        max: 20,\n        message: this.$t(\"admin.system.admin.validatePassword.lengthError\"),\n        trigger: [\"blur\", \"change\"]\n      }];\n      this.rules.repwd = [{\n        required: true,\n        message: this.$t(\"admin.system.admin.validateConfirmPassword.required\"),\n        validator: function validator(rule, value, callback) {\n          if (value === \"\") {\n            callback(new Error(_this6.$t(\"admin.system.admin.validatePass.notMatch\")));\n          } else if (value !== _this6.pram.pwd) {\n            callback(new Error(_this6.$t(\"admin.system.admin.validatePass.notMatch\")));\n          } else {\n            callback();\n          }\n        },\n        trigger: [\"blur\", \"change\"]\n      }];\n    }\n  }\n};", null]}