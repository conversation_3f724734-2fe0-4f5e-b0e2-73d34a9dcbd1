{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\groupBuy\\groupGoods\\creatGroup.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\groupBuy\\groupGoods\\creatGroup.vue", "mtime": 1754050582445}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Tinymce from '@/components/Tinymce/index'\nimport {  productDetailApi, categoryApi } from '@/api/store'\nimport { shippingTemplatesList } from '@/api/logistics'\nimport { getSeckillList } from '@/libs/public'\nimport {  combinationSaveApi, combinationUpdateApi, combinationInfoApi } from '@/api/marketing'\nimport CreatTemplates from '@/views/systemSetting/logistics/shippingTemplates/creatTemplates'\nimport {formatDates} from \"@/utils\";\nimport {Debounce} from '@/utils/validate'\nconst defaultObj = {\n  image: '',\n  images: '',\n  imagelist: [],\n  title: '',\n  info: '',\n  num: 1,\n  unitName: '',\n  sort: 0,\n  giveIntegral: 0,\n  ficti: 0,\n  isShow: false,\n  tempId: '',\n  attrValue: [{\n    image: '',\n    price: 0,\n    cost: 0,\n    otPrice: 0,\n    stock: 0,\n    quota: 1,\n    weight: 0,\n    volume: 0,\n    barCode: ''\n  }],\n  attr: [],\n  selectRule: '',\n  content: '',\n  specType: false,\n  id: 0,\n  // productId: 0,\n  startTime: '',\n  stopTime: '',\n  timeVal: [],\n  effectiveTime: 0,\n  people: 2,\n  virtualRation: 0\n}\nconst objTitle = {\n  price: {\n    title: '拼团价'\n  },\n  cost: {\n    title: '成本价'\n  },\n  otPrice: {\n    title: '原价'\n  },\n  stock: {\n    title: '库存'\n  },\n  quota: {\n    title: \"限量\",\n  },\n  barCode: {\n    title: '商品编号'\n  },\n  weight: {\n    title: '重量（KG）'\n  },\n  volume: {\n    title: '体积(m³)'\n  }\n}\nexport default {\n  name: \"creatSeckill\",\n  components: { CreatTemplates,Tinymce },\n  data() {\n    return {\n      pickerOptions: {\n        disabledDate(time) {\n          return time.getTime() < new Date().setTime(new Date().getTime() - 3600 * 1000 * 24);\n        }\n      },\n      props2: {\n        children: 'child',\n        label: 'name',\n        value: 'id',\n        multiple: true,\n        emitPath: false\n      },\n      grid2: {\n        xl: 8,\n        lg: 10,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      currentTab: 0,\n      formThead: Object.assign({}, objTitle),\n      formValidate: Object.assign({}, defaultObj),\n      loading: false,\n      fullscreenLoading: false,\n      merCateList: [], // 商户分类筛选\n      shippingList: [], // 运费模板\n      seckillTime: [],\n      ruleValidate: {\n        productId: [\n          { required: true, message: '请选择商品', trigger: 'change' }\n        ],\n        title: [\n          { required: true, message: '请输入商品标题', trigger: 'blur' }\n        ],\n        attrValue: [\n          { required: true, message: '请选择商品属相', trigger: 'change', type: 'array', min: '1' }\n        ],\n        num: [\n          { required: true, message: '请输入购买数量限制', trigger: 'blur' }\n        ],\n        unitName: [\n          { required: true, message: '请输入单位', trigger: 'blur' }\n        ],\n        info: [\n          { required: true, message: '请输入拼团商品简介', trigger: 'blur' }\n        ],\n        tempId: [\n          { required: true, message: '请选择运费模板', trigger: 'change' }\n        ],\n        image: [\n          { required: true, message: '请上传商品图', trigger: 'change' }\n        ],\n        imagelist: [\n          { required: true, message: '请上传商品轮播图', type: 'array', trigger: 'change' }\n        ],\n        specType: [\n          { required: true, message: '请选择商品规格', trigger: 'change' }\n        ],\n        timeVal:[\n          { required: true, message: '请选择活动日期', trigger: 'change', type: 'array'}\n        ],\n        virtualRation:[\n          { required: true, message: '请输入补齐人数', trigger: 'blur'}\n        ],\n        onceNum: [\n          { required: true, message: '请输入单次购买数量限制', trigger: 'blur'}\n        ],\n        people: [\n          { required: true, message: '请输入拼团人数', trigger: 'blur'}\n        ],\n        effectiveTime: [\n          { required: true, message: '请输入拼团时效', trigger: 'blur'}\n        ]\n      },\n      manyTabDate: {},\n      manyTabTit: {},\n      attrInfo: {},\n      tempRoute: {},\n      multipleSelection: [],\n      productId: 0,\n      radio: '',\n      ManyAttrValue: [Object.assign({}, defaultObj.attrValue[0])], // 多规格\n    }\n  },\n  computed: {\n    attrValue() {\n      const obj = Object.assign({}, defaultObj.attrValue[0])\n      delete obj.image\n      return obj\n    }\n  },\n  created() {\n    this.$watch('formValidate.attr', this.watCh)\n    this.tempRoute = Object.assign({}, this.$route)\n  },\n  mounted() {\n    getSeckillList(1).then((res) => {\n      this.seckillTime = res.list\n    })\n   this.formValidate.imagelist = []\n    if ( this.$route.params.id ) {\n      this.setTagsViewTitle()\n      this.getInfo()\n      this.currentTab = 1\n    }\n    this.getShippingList()\n    this.getCategorySelect()\n  },\n  methods: {\n    handleSelectionChange(val) {\n      val.map(item => {\n        item.checked = true\n      })\n      this.multipleSelection = val;\n    },\n    watCh(val) {\n      const tmp = {}\n      const tmpTab = {}\n      this.formValidate.attr.forEach((o, i) => {\n        // tmp['value' + i] = { title: o.attrName }\n        // tmpTab['value' + i] = ''\n        tmp[o.attrName] = { title: o.attrName };\n        tmpTab[o.attrName] = '';\n      })\n      this.manyTabTit = tmp\n      this.manyTabDate = tmpTab\n      this.formThead = Object.assign({}, this.formThead, tmp)\n    },\n    handleRemove (i) {\n      this.formValidate.imagelist.splice(i, 1)\n    },\n    // 点击商品图\n    modalPicTap (tit, num, i) {\n      const _this = this\n      this.$modalUpload(function(img) {\n        if(tit==='1'&& !num){\n          _this.formValidate.image = img[0].sattDir\n          _this.ManyAttrValue[0].image = img[0].sattDir\n        }\n        if(tit==='2'&& !num){\n          if(img.length>10) return this.$message.warning(\"最多选择10张图片！\");\n          if(img.length + _this.formValidate.imagelist.length > 10) return this.$message.warning(\"最多选择10张图片！\");\n          img.map((item) => {\n            _this.formValidate.imagelist.push(item.sattDir)\n          });\n        }\n        if(tit==='1'&& num === 'duo' ){\n          _this.ManyAttrValue[i].image = img[0].sattDir\n        }\n      },tit, 'content')\n\n    },\n    // 具体日期\n    onchangeTime(e) {\n      this.formValidate.timeVal = e;\n      this.formValidate.startTime = e ? e[0] : \"\";\n      this.formValidate.stopTime = e ? e[1] : \"\";\n    },\n    changeGood(){\n      const _this = this\n      this.$modalGoodList(function(row) {\n        _this.formValidate.image = row.image\n        _this.productId = row.id\n      })\n    },\n    handleSubmitNest1() {\n      if (!this.formValidate.image) {\n        return this.$message.warning(\"请选择商品！\");\n      } else {\n        this.currentTab++;\n        if (!this.$route.params.id) this.getProdect(this.productId);\n      }\n    },\n    // 商品分类；\n    getCategorySelect() {\n      categoryApi({ status: -1, type: 1 }).then(res => {\n        this.merCateList = this.filerMerCateList(res)\n      })\n    },\n    filerMerCateList(treeData) {\n      return treeData.map((item) => {\n        if(!item.child){\n          item.disabled = true\n        }\n        item.label = item.name\n        return item\n      })\n    },\n    // 运费模板；\n    getShippingList() {\n      shippingTemplatesList(this.tempData).then(res => {\n        this.shippingList = res.list\n      })\n    },\n    // 运费模板\n    addTem() {\n      this.$refs.addTemplates.dialogVisible = true\n      this.$refs.addTemplates.getCityList()\n    },\n    // 商品详情\n    getInfo () {\n      if(!this.$route.params.id){\n        this.getProdect(this.productId)\n      }else{\n        this.getSekllProdect(this.$route.params.id)\n      }\n    },\n    getProdect(id) {\n      this.fullscreenLoading = true\n      productDetailApi(id).then(async res => {\n        this.formValidate = {\n          image: this.$selfUtil.setDomain(res.image),\n          imagelist: JSON.parse(res.sliderImage),\n          title: res.storeName,\n          info: res.storeInfo,\n          quota: '',\n          unitName: res.unitName,\n          sort: res.sort,\n          tempId: res.tempId,\n          attr: res.attr,\n          selectRule: res.selectRule,\n          content: res.content,\n          specType: res.specType,\n          productId: res.id,\n          giveIntegral: res.giveIntegral,\n          ficti: res.ficti,\n          startTime: res.startTime || '',\n          stopTime: res.stopTime || '',\n          timeVal: [],\n          status: 0,\n            isShow: false,\n          num : 1,\n          isHost : false,\n          people : 2,\n          onceNum : 1,\n          virtualRation : '',\n          effectiveTime : 0,\n          isPostage: false\n        }\n        if(res.specType){\n          res.attrValue.forEach((row) => {\n            row.quota = row.stock;\n            row.attrValue = JSON.parse(row.attrValue);\n            for (let attrValueKey in row.attrValue) {\n              row[attrValueKey] = row.attrValue[attrValueKey];\n            }\n          });\n          this.$nextTick(() => {\n            res.attrValue.forEach((row) => {\n              row.image = this.$selfUtil.setDomain(row.image)\n              this.$refs.multipleTable.toggleRowSelection(row, true);\n              this.$set(row, 'checked', true)\n            });\n          });\n          this.ManyAttrValue = res.attrValue\n          this.multipleSelection = res.attrValue\n        }else{\n          res.attrValue.forEach((row) => {\n            row.quota = row.stock;\n          });\n          this.ManyAttrValue = res.attrValue\n          this.formValidate.attr = res.attr\n        }\n        this.fullscreenLoading = false\n      }).catch(res => {\n        this.fullscreenLoading = false\n      })\n    },\n    getSekllProdect(id) {\n      this.fullscreenLoading = true\n      combinationInfoApi({id:id}).then(async res => {\n        this.formValidate = {\n          image: this.$selfUtil.setDomain(res.image),\n          imagelist: JSON.parse(res.sliderImage),\n          title: res.title,\n          info: res.info,\n          unitName: res.unitName,\n          sort: res.sort,\n          tempId: res.tempId,\n          attr: res.attr,\n          selectRule: res.selectRule,\n          content: res.content,\n          specType: res.specType,\n          productId: res.productId,\n          giveIntegral: res.giveIntegral,\n          ficti: res.ficti,\n          // timeVal: res.startTimeStr && res.stopTimeStr ? [res.startTimeStr, res.stopTimeStr] : [],\n          timeVal: res.startTime && res.stopTime ? [formatDates(new Date(res.startTime), 'yyyy-MM-dd'), formatDates(new Date(res.stopTime), 'yyyy-MM-dd')] : [],\n          status: res.status,\n          isShow: res.isShow,\n          num : res.num,\n          isHost : res.isHost,\n          people : res.people,\n          onceNum : res.onceNum,\n          virtualRation : res.virtualRation,\n          effectiveTime : res.effectiveTime,\n          isPostage: false,\n          startTime: res.startTime || '',\n          stopTime: res.stopTime || '',\n          id: res.id\n        }\n        if(res.specType){\n          this.ManyAttrValue = res.attrValue;\n          this.$nextTick(() => {\n            this.ManyAttrValue.forEach((item, index) => {\n              item.image = this.$selfUtil.setDomain(item.image)\n              item.attrValue = JSON.parse(item.attrValue);\n              for (let attrValueKey in item.attrValue) {\n                item[attrValueKey] = item.attrValue[attrValueKey];\n              }\n              if (item.id) {\n                this.$set(item, 'price', item.price)\n                this.$set(item, 'quota', item.quota)\n                this.$nextTick(() => {\n                  this.$refs.multipleTable.toggleRowSelection(item, true)\n                })\n              }\n            })\n          });\n        }else{\n          this.ManyAttrValue = res.attrValue\n          // this.formValidate.attr = []\n        }\n        this.fullscreenLoading = false\n      }).catch(res => {\n        this.fullscreenLoading = false\n      })\n    },\n    handleSubmitNest2(name) {\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          if(this.formValidate.specType && this.multipleSelection.length ===0 ) return this.$message.warning(\"请选择至少一个商品属性！\");\n          this.currentTab++;\n        } else {\n          return false;\n        }\n      });\n    },\n    // 提交\n    handleSubmit:Debounce(function(name) {\n      if(!this.formValidate.specType){\n        // this.formValidate.attr = []\n        this.formValidate.attrValue = this.ManyAttrValue\n      }else{\n        this.formValidate.attrValue = this.multipleSelection;\n      }\n      this.formValidate.attrValue.forEach(item=>{\n        item.attrValue = JSON.stringify(item.attrValue);\n      });\n      this.formValidate.images = JSON.stringify(this.formValidate.imagelist);\n      this.formValidate.startTime = this.formValidate.timeVal[0];\n      this.formValidate.stopTime = this.formValidate.timeVal[1];\n      // this.formValidate.virtualRation = Math.floor((this.formValidate.people - this.formValidate.peopleNum) / this.formValidate.people * 100)\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          this.fullscreenLoading = true;\n          this.loading = true;\n          this.$route.params.id\n            ? combinationUpdateApi({id: this.$route.params.id}, this.formValidate)\n              .then(async () => {\n                this.fullscreenLoading = false;\n                this.$message.success('编辑成功');\n                this.$router.push({\n                  path: \"/marketing/groupBuy/groupGoods\",\n                });\n                this.$refs[name].resetFields();\n                this.formValidate.imagelist = [];\n                this.loading = false;\n              })\n              .catch(() => {\n                this.fullscreenLoading = false;\n                this.loading = false;\n              })\n            : combinationSaveApi(this.formValidate)\n              .then(async (res) => {\n                this.fullscreenLoading = false;\n                this.$message.success('新增成功');\n                this.$router.push({\n                  path: \"/marketing/groupBuy/groupGoods\",\n                });\n                this.$refs[name].resetFields();\n                this.formValidate.imagelist = [];\n                this.loading = false;\n              })\n              .catch(() => {\n                this.fullscreenLoading = false;\n                this.loading = false;\n              });\n        } else {\n          if (\n            !this.formValidate.storeName ||\n            !this.formValidate.unitName ||\n            !this.formValidate.store_info ||\n            !this.formValidate.image ||\n            !this.formValidate.images\n          ) {\n            this.$message.warning(\"请填写完整商品信息！\");\n          }\n        }\n      });\n\n    }),\n    handleSubmitUp() {\n      if (this.currentTab-- < 0) this.currentTab = 0;\n    },\n    setTagsViewTitle() {\n      const title = '编辑拼团商品'\n      const route = Object.assign({}, this.tempRoute, { title: `${title}-${this.$route.params.id}` })\n      this.$store.dispatch('tagsView/updateVisitedView', route)\n    },\n    // 移动\n    handleDragStart (e, item) {\n      this.dragging = item;\n    },\n    handleDragEnd (e, item) {\n      this.dragging = null\n    },\n    handleDragOver (e) {\n      e.dataTransfer.dropEffect = 'move'\n    },\n    handleDragEnter (e, item) {\n      e.dataTransfer.effectAllowed = 'move'\n      if (item === this.dragging) {\n        return\n      }\n      const newItems = [...this.formValidate.imagelist]\n      const src = newItems.indexOf(this.dragging)\n      const dst = newItems.indexOf(item)\n      newItems.splice(dst, 0, ...newItems.splice(src, 1))\n      this.formValidate.imagelist = newItems;\n    }\n  }\n}\n", null]}