{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\HeaderSearch\\index.vue?vue&type=template&id=032bd1f0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\HeaderSearch\\index.vue", "mtime": 1754050582266}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div :class=\"{'show':show}\" class=\"header-search\">\n  <i class=\"iconfont iconios-search\" style=\"font-size: 20px;\" @click.stop=\"click\"></i>\n  <!--<svg-icon class-name=\"search-icon\" icon-class=\"search\" @click.stop=\"click\" />-->\n  <el-select\n    ref=\"headerSearchSelect\"\n    v-model=\"search\"\n    :remote-method=\"querySearch\"\n    filterable\n    default-first-option\n    remote\n    placeholder=\"搜索菜单\"\n    class=\"header-search-select\"\n    @change=\"change\"\n  >\n    <el-option v-for=\"item in options\" :key=\"item.url\" :value=\"item\" :label=\"item.name.join(' > ')\" />\n  </el-select>\n</div>\n", null]}