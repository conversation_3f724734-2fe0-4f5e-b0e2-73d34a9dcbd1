{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsPay\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsPay\\index.vue", "mtime": 1754050582490}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { smsNumberApi, smsPriceApi, payCodeApi, smsInfoApi } from '@/api/sms'\r\nimport { isLogin } from '@/libs/public'\r\nimport { mapGetters } from 'vuex'\r\nimport QRcode from 'qrcodejs2'\r\nexport default {\r\n  name: 'SmsPay',\r\n  data() {\r\n    return {\r\n      numbers: '',\r\n      account: '',\r\n      list: [],\r\n      current: 0,\r\n      checkList: {},\r\n      fullscreenLoading: false,\r\n      code: {},\r\n      tableFrom: {\r\n        type: 'sms'\r\n      },\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'isLogin'\r\n    ])\r\n  },\r\n  created () {\r\n    this.tableFrom.type = this.$route.query.type;\r\n    this.onIsLogin();\r\n  },\r\n  mounted() {\r\n    if (!this.isLogin) {\r\n      // this.$router.push('/operation/onePass?url=' + this.$route.path)\r\n    } else {\r\n      this.getNumber()\r\n      this.getPrice()\r\n    }\r\n  },\r\n  methods: {\r\n    onChangeType (val) {\r\n      this.current = 0;\r\n      this.getPrice();\r\n      this.getNumber()\r\n    },\r\n    // 查看是否登录\r\n    onIsLogin() {\r\n      this.fullscreenLoading = true\r\n      this.$store.dispatch('user/isLogin').then(async res => {\r\n        const data = res\r\n        if (!data.status) {\r\n          this.$message.warning('请先登录')\r\n          this.$router.push('/operation/onePass?url=' + this.$route.path)\r\n        } else {\r\n          this.getNumber()\r\n          this.getPrice()\r\n        }\r\n        this.fullscreenLoading = false\r\n      }).catch(res => {\r\n        this.$router.push('/operation/onePass?url=' + this.$route.path)\r\n        this.fullscreenLoading = false\r\n      })\r\n    },\r\n    // 剩余条数\r\n    getNumber() {\r\n      smsInfoApi().then(async res => {\r\n        let data = res;\r\n        this.account = data.account;\r\n        switch (this.tableFrom.type) {\r\n          case 'sms':\r\n            this.numbers = data.sms.num\r\n            break;\r\n          case 'copy':\r\n            this.numbers = data.copy.num\r\n            break;\r\n          case 'expr_dump':\r\n            this.numbers = data.dump.num\r\n            break;\r\n          default:\r\n            this.numbers = data.query.num\r\n            break;\r\n        }\r\n      })\r\n    },\r\n    // 支付套餐\r\n    getPrice() {\r\n      this.fullscreenLoading = true\r\n      smsPriceApi(this.tableFrom).then(async res => {\r\n        setTimeout(() => {\r\n          this.fullscreenLoading = false\r\n        }, 800)\r\n        const data = res\r\n        this.list = data.data\r\n        this.checkList = this.list[0]\r\n        this.getCode(this.checkList)\r\n      }).catch(() => {\r\n        this.fullscreenLoading = false\r\n      })\r\n    },\r\n    // 选中\r\n    check(item, index) {\r\n      this.fullscreenLoading = true\r\n      this.current = index\r\n      setTimeout(() => {\r\n        this.getCode(item)\r\n        this.checkList = item\r\n        this.fullscreenLoading = false\r\n      }, 800)\r\n    },\r\n    // 支付码\r\n    getCode(item) {\r\n      const data = {\r\n        payType: 'weixin',\r\n        mealId: item.id,\r\n        price: item.price,\r\n        num: item.num,\r\n        type: this.tableFrom.type\r\n      }\r\n      payCodeApi(data).then(async res => {\r\n        this.code = res\r\n        document.getElementById('payQrcode').innerHTML = '';\r\n        new QRcode('payQrcode', { width:135, height:135,text: res.qr_code})\r\n      })\r\n    }\r\n  }\r\n}\r\n", null]}