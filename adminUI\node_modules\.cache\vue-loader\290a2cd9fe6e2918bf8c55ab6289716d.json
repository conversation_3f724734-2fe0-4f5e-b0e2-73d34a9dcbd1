{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\tableList.vue?vue&type=template&id=327cb9f7&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\tableList.vue", "mtime": 1754050582488}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div>\n  <el-tabs v-model=\"tableFrom.type\" @tab-click=\"onChangeType\">\n    <el-tab-pane label=\"短信\" name=\"sms\"></el-tab-pane>\n    <el-tab-pane label=\"商品采集\" name=\"copy\"></el-tab-pane>\n    <el-tab-pane label=\"物流查询\" name=\"expr_query\"></el-tab-pane>\n    <el-tab-pane label=\"电子面单打印\" name=\"expr_dump\"></el-tab-pane>\n  </el-tabs>\n  <!--短信列表-->\n  <div class=\"note\" v-if=\"(tableFrom.type==='sms' && sms.open === 1) || (tableFrom.type==='expr_query' && query.open === 1) || (tableFrom.type==='copy' && copy.open === 1) || (tableFrom.type==='expr_dump' && dump.open === 1)\">\n    <div class=\"filter-container flex-between mb20\" v-if=\"tableFrom.type === 'sms'\">\n      <div class=\"demo-input-suffix\">\n        <span class=\"seachTiele\">短信状态：</span>\n        <el-radio-group v-model=\"tableFrom.status\" size=\"small\" @change=\"getList\" class=\"mr20\">\n          <el-radio-button label=\"3\">全部</el-radio-button>\n          <el-radio-button label=\"1\">成功</el-radio-button>\n          <el-radio-button label=\"2\">失败</el-radio-button>\n          <el-radio-button label=\"0\">发送中</el-radio-button>\n        </el-radio-group>\n      </div>\n      <div>\n          <router-link :to=\"{path: '/operation/systemSms/template'}\">\n            <el-button type=\"primary\"  class=\"mr20\" v-hasPermi=\"['admin:sms:temps']\">短信模板</el-button>\n          </router-link>\n          <el-button  @click=\"editSign\" v-hasPermi=\"['admin:sms:modify:sign']\">修改签名</el-button>\n        </div>\n    </div>\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      highlight-current-row\n      :header-cell-style=\" {fontWeight:'bold'}\"\n    >\n      <el-table-column\n        v-for=\"(item, index) in columns2\" :key=\"index\"\n        :prop=\"item.key\"\n        :label=\"item.title\"\n        :min-width=\"item.minWidth\">\n        <template slot-scope=\"scope\">\n          <div v-if=\"['content'].indexOf(item.key) > -1 && tableFrom.type==='expr_query'\" class=\"demo-image__preview\">\n            <span>{{scope.row[item.key].num}}</span>\n          </div>\n          <span v-else>{{ scope.row[item.key] }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </div>\n  <!--无开通-->\n  <div v-else>\n    <!--开通按钮-->\n    <div v-if=\"(tableFrom.type==='sms' && !isSms) || (tableFrom.type==='expr_dump' && !isDump) || ((tableFrom.type==='copy' || tableFrom.type==='expr_query') && !isCopy)\" class=\"wuBox acea-row row-column-around row-middle\">\n      <div class=\"wuTu\"><img src=\"../../../../assets/imgs/wutu.png\"></div>\n      <div class=\"mb15\">\n        <span class=\"wuSp1\">{{tableFrom.type | onePassTypeFilter}}未开通哦</span>\n        <span class=\"wuSp2\">点击立即开通按钮，即可使用{{tableFrom.type | onePassTypeFilter}}服务哦～～～</span>\n      </div>\n      <el-button size=\"medium\" type=\"primary\" @click=\"onOpenIndex(tableFrom.type)\">立即开通</el-button>\n    </div>\n    <!--短信立即开通/开通电子面单服务-->\n    <div class=\"smsBox\" v-if=\"(isDump && tableFrom.type==='expr_dump') || (isSms && tableFrom.type==='sms')\">\n      <div class=\"index_from page-account-container\">\n        <div class=\"page-account-top\">\n          <span class=\"page-account-top-tit\">开通{{tableFrom.type | onePassTypeFilter}}服务</span>\n        </div>\n        <el-form ref=\"formInlineDump\" :model=\"formInlineDump\" :rules=\"ruleInline\" @submit.native.prevent @keyup.enter=\"handleSubmitDump('formInlineDump')\">\n          <el-form-item prop=\"sign\" class=\"maxInpt\" v-if=\"isSms && tableFrom.type==='sms'\" key=\"1\">\n            <el-input type=\"text\" v-model=\"formInlineDump.sign\" prefix=\"ios-contact-outline\" placeholder=\"请输入短信签名\"/>\n          </el-form-item>\n          <template v-if=\"isDump && tableFrom.type==='expr_dump'\">\n            <el-form-item prop=\"com\" class=\"maxInpt\">\n              <el-select v-model=\"formInlineDump.com\" filterable  placeholder=\"请选择快递公司\" @change=\"onChangeExport\" style=\"text-align: left;\" class=\"width10\">\n                <el-option v-for=\"(item,index) in exportList\" :value=\"item.code\" :key=\"index\" :label=\"item.name\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item prop=\"temp_id\" class=\"tempId maxInpt\">\n              <div class=\"acea-row\">\n                <el-select v-model=\"formInlineDump.tempId\"  placeholder=\"请选择电子面单模板\" style=\"text-align: left;\" :class=\"[formInlineDump.tempId?'width9':'width10']\" @change=\"onChangeImg\">\n                  <el-option v-for=\"(item, index) in exportTempList\" :value=\"item.temp_id\" :key=\"index\" :label=\"item.title\"></el-option>\n                </el-select>\n                <div v-if=\"formInlineDump.tempId\" style=\"position: relative;\">\n                  <!--<span class=\"tempImg\" @click=\"\">预览</span>-->\n                  <div class=\"tempImgList ml10\">\n                    <div class=\"demo-image__preview\">\n                      <el-image\n                        style=\"width: 36px; height: 36px\"\n                        :src=\"tempImg\"\n                        :preview-src-list=\"[tempImg]\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </el-form-item>\n            <el-form-item prop=\"toName\" class=\"maxInpt\">\n              <el-input type=\"text\" v-model=\"formInlineDump.toName\" prefix=\"ios-contact-outline\"\n                        placeholder=\"请填写寄件人姓名\"/>\n            </el-form-item>\n            <el-form-item prop=\"toTel\" class=\"maxInpt\">\n              <el-input type=\"text\" v-model=\"formInlineDump.toTel\" prefix=\"ios-contact-outline\"\n                        placeholder=\"请填写寄件人电话\"/>\n            </el-form-item>\n            <el-form-item prop=\"toAddress\" class=\"maxInpt\">\n              <el-input type=\"text\" v-model=\"formInlineDump.toAddress\" prefix=\"ios-contact-outline\"\n                        placeholder=\"请填写寄件人详细地址\"/>\n            </el-form-item>\n            <el-form-item prop=\"siid\" class=\"maxInpt\">\n              <el-input type=\"text\" v-model=\"formInlineDump.siid\" prefix=\"ios-contact-outline\"\n                        placeholder=\"请填写云打印编号\"/>\n            </el-form-item>\n          </template>\n\n          <el-form-item class=\"maxInpt\">\n            <el-button type=\"primary\" size=\"medium\" :loading=\"loading\" @click=\"handleSubmitDump('formInlineDump')\" class=\"btn width10\">立即开通</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n    </div>\n  </div>\n  <!--修改签名-->\n  <el-dialog\n    title=\"短信账户签名修改\"\n    :visible.sync=\"dialogVisible\"\n    width=\"500px\"\n    :before-close=\"handleClose\">\n    <el-form ref=\"formInline\" size=\"small\" :model=\"formInline\" :rules=\"ruleInlineSign\" class=\"login-form\" autocomplete=\"on\" label-position=\"left\">\n      <el-form-item>\n        <el-input v-model=\"formInline.account\" :disabled=\"true\"  prefix-icon=\"el-icon-user\">\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"sign\">\n        <el-input\n          v-model=\"formInline.sign\"\n          placeholder=\"请输入短信签名，例如：CRMEB\"\n          prefix-icon=\"el-icon-document\"\n        >\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"phone\">\n        <el-input\n          v-model=\"formInline.phone\"\n          placeholder=\"请输入您的手机号\"\n          :disabled=\"true\"\n          prefix-icon=\"el-icon-phone-outline\"\n        >\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"code\" class=\"captcha\">\n        <div class=\"acea-row\" style=\"flex-wrap: nowrap;\">\n          <el-input\n            ref=\"username\"\n            v-model=\"formInline.code\"\n            placeholder=\"验证码\"\n            name=\"username\"\n            type=\"text\"\n            tabindex=\"1\"\n            autocomplete=\"off\"\n            prefix-icon=\"el-icon-message\"\n            style=\"width: 90%\"\n          />\n          <el-button size=\"mini\" :disabled=!this.canClick @click=\"cutDown\" v-hasPermi=\"['admin:pass:send:code']\">{{cutNUm}}</el-button>\n        </div>\n      </el-form-item>\n      <el-form-item>\n        <el-alert title=\"短信签名提交后需要审核才会生效，请耐心等待或者联系客服\" type=\"success\"></el-alert>\n      </el-form-item>\n    </el-form>\n    <span slot=\"footer\" class=\"dialog-footer\">\n      <el-button type=\"primary\" @click=\"handleSubmit('formInline')\">确 定</el-button>\n    </span>\n  </el-dialog>\n</div>\n", null]}