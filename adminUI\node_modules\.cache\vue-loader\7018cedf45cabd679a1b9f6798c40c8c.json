{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\collateOrder\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\collateOrder\\index.vue", "mtime": 1754050582505}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { orderListApi, storeListApi } from '@/api/storePoint'\nimport cardsData from '@/components/cards/index'\nexport default {\n  components: { cardsData },\n  data() {\n    return {\n      storeSelectList: [],\n      orderId: 0,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      listLoading: true,\n      tableFrom: {\n        keywords: '',\n        storeId: '',\n        dateLimit: '',\n        page: 1,\n        limit: 20\n      },\n      timeVal: [],\n      fromList: this.$constants.fromList,\n      ids: '',\n      cardLists: [],\n    }\n  },\n  mounted() {\n    this.storeList()\n    this.getList()\n  },\n  methods: {\n    storeList() {\n      let artFrom =  {\n        page: 1,\n        limit: 999,\n        status: '1',\n        keywords: ''\n      };\n      storeListApi(artFrom).then(res=>{\n        this.storeSelectList = res.list;\n      })\n    },\n    pageChangeLog(page) {\n      this.tableFromLog.page = page\n      this.getList()\n    },\n    handleSizeChangeLog(val) {\n      this.tableFromLog.limit = val\n      this.getList()\n    },\n    // 选择时间\n    selectChange(tab) {\n      this.tableFrom.date = tab\n      this.tableFrom.page = 1\n      this.timeVal = []\n      this.getList()\n    },\n    // 具体日期\n    onchangeTime(e) {\n      this.timeVal = e\n      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : ''\n      this.tableFrom.page = 1\n      this.getList()\n    },\n    // 列表\n    getList(num) {\n      this.listLoading = true\n      this.tableFrom.page = num ? num : this.tableFrom.page\n      orderListApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list.list\n        this.tableData.total = res.list.total\n        this.cardLists = [\n          { name: '订单数量', count: res.total,color:'#1890FF',class:'one',icon:'icondingdan' },\n          { name: '订单金额', count: res.orderTotalPrice ,color:'#A277FF',class:'two',icon:'icondingdanjine'},\n          { name: '退款总单数', count: res.refundTotal,color:'#EF9C20',class:'three',icon:'icondingdanguanli' },\n          { name: '退款总金额', count: res.refundTotalPrice,color:'#1BBE6B',class:'four',icon:'iconshangpintuikuanjine' }\n        ]\n        // this.cardLists = res.data.stat\n        this.listLoading = false\n      }).catch(() => {\n        this.listLoading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      this.getList()\n    }\n  }\n}\n", null]}