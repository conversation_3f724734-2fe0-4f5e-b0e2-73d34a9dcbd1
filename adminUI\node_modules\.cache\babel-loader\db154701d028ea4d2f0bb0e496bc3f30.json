{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\FormGenerator\\index\\JsonDrawer.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\FormGenerator\\index\\JsonDrawer.vue", "mtime": 1754050582253}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _index = require(\"../utils/index\");\nvar _clipboard = _interopRequireDefault(require(\"clipboard\"));\nvar _fileSaver = require(\"file-saver\");\nvar _loadMonaco = _interopRequireDefault(require(\"../utils/loadMonaco\"));\nvar _loadBeautifier = _interopRequireDefault(require(\"../utils/loadBeautifier\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n// import * as monaco from \"monaco-editor\";\n\nvar beautifier;\nvar monaco;\nvar _default = exports.default = {\n  components: {},\n  props: {\n    jsonStr: {\n      type: String,\n      required: true,\n      beautifier: null,\n      jsonEditor: null\n    }\n  },\n  data: function data() {\n    return {};\n  },\n  computed: {},\n  watch: {},\n  created: function created() {},\n  mounted: function mounted() {\n    var _this = this;\n    window.addEventListener('keydown', this.preventDefaultSave);\n    var clipboard = new _clipboard.default('.copy-json-btn', {\n      text: function text(trigger) {\n        _this.$notify({\n          title: '成功',\n          message: '代码已复制到剪切板，可粘贴。',\n          type: 'success'\n        });\n        return _this.beautifierJson;\n      }\n    });\n    clipboard.on('error', function (e) {\n      _this.$message.error('代码复制失败');\n    });\n  },\n  beforeDestroy: function beforeDestroy() {\n    window.removeEventListener('keydown', this.preventDefaultSave);\n  },\n  methods: {\n    preventDefaultSave: function preventDefaultSave(e) {\n      if (e.key === 's' && (e.metaKey || e.ctrlKey)) {\n        e.preventDefault();\n      }\n    },\n    onOpen: function onOpen() {\n      var _this2 = this;\n      (0, _loadBeautifier.default)(function (btf) {\n        beautifier = btf;\n        _this2.beautifierJson = beautifier.js(_this2.jsonStr, _index.beautifierConf.js);\n        (0, _loadMonaco.default)(function (val) {\n          monaco = val;\n          _this2.setEditorValue('editorJson', _this2.beautifierJson);\n        });\n      });\n      // monaco.editor.create(document.getElementById(\"editorJson\"), {\n      //   value: [\n      //     \"function x() {\",\n      //     '\\tconsole.integralLog(\"Hello world!\");',\n      //     \"}\"].join(\n      //     \"\\n\"\n      //   ),\n      //   language: \"javascript\",\n      //   theme: 'vs-dark',\n      //   automaticLayout: true\n      // });\n    },\n    onClose: function onClose() {},\n    setEditorValue: function setEditorValue(id, codeStr) {\n      var _this3 = this;\n      if (this.jsonEditor) {\n        this.jsonEditor.setValue(codeStr);\n      } else {\n        this.jsonEditor = monaco.editor.create(document.getElementById(id), {\n          value: codeStr,\n          theme: 'vs-dark',\n          language: 'json',\n          automaticLayout: true\n        });\n        // ctrl + s 刷新\n        this.jsonEditor.onKeyDown(function (e) {\n          if (e.keyCode === 49 && (e.metaKey || e.ctrlKey)) {\n            _this3.refresh();\n          }\n        });\n      }\n    },\n    exportJsonFile: function exportJsonFile() {\n      var _this4 = this;\n      this.$prompt('文件名:', '导出文件', {\n        inputValue: \"\".concat(+new Date(), \".json\"),\n        closeOnClickModal: false,\n        inputPlaceholder: '请输入文件名'\n      }).then(function (_ref) {\n        var value = _ref.value;\n        if (!value) value = \"\".concat(+new Date(), \".json\");\n        var codeStr = _this4.jsonEditor.getValue();\n        var blob = new Blob([codeStr], {\n          type: 'text/plain;charset=utf-8'\n        });\n        (0, _fileSaver.saveAs)(blob, value);\n      });\n    },\n    refresh: function refresh() {\n      try {\n        this.$emit('refresh', JSON.parse(this.jsonEditor.getValue()));\n      } catch (error) {\n        this.$notify({\n          title: '错误',\n          message: 'JSON格式错误，请检查',\n          type: 'error'\n        });\n      }\n    }\n  }\n};", null]}