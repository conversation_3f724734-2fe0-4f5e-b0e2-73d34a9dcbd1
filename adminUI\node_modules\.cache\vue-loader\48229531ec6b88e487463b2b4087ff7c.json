{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\FormGenerator\\index\\FormDrawer.vue?vue&type=template&id=14fdd522&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\FormGenerator\\index\\FormDrawer.vue", "mtime": 1754050582250}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    [\n      _c(\n        \"el-drawer\",\n        _vm._g(\n          _vm._b(\n            { on: { opened: _vm.onOpen, close: _vm.onClose } },\n            \"el-drawer\",\n            _vm.$attrs,\n            false\n          ),\n          _vm.$listeners\n        ),\n        [\n          _c(\n            \"div\",\n            { staticStyle: { height: \"100%\" } },\n            [\n              _c(\n                \"el-row\",\n                { staticStyle: { height: \"100%\", overflow: \"auto\" } },\n                [\n                  _c(\n                    \"el-col\",\n                    { staticClass: \"left-editor\", attrs: { md: 24, lg: 12 } },\n                    [\n                      _c(\n                        \"div\",\n                        {\n                          staticClass: \"setting\",\n                          attrs: { title: \"资源引用\" },\n                          on: { click: _vm.showResource },\n                        },\n                        [\n                          _c(\n                            \"el-badge\",\n                            {\n                              staticClass: \"item\",\n                              attrs: { \"is-dot\": !!_vm.resources.length },\n                            },\n                            [_c(\"i\", { staticClass: \"el-icon-setting\" })]\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-tabs\",\n                        {\n                          staticClass: \"editor-tabs\",\n                          attrs: { type: \"card\" },\n                          model: {\n                            value: _vm.activeTab,\n                            callback: function ($$v) {\n                              _vm.activeTab = $$v\n                            },\n                            expression: \"activeTab\",\n                          },\n                        },\n                        [\n                          _c(\"el-tab-pane\", { attrs: { name: \"html\" } }, [\n                            _c(\n                              \"span\",\n                              { attrs: { slot: \"label\" }, slot: \"label\" },\n                              [\n                                _vm.activeTab === \"html\"\n                                  ? _c(\"i\", { staticClass: \"el-icon-edit\" })\n                                  : _c(\"i\", {\n                                      staticClass: \"el-icon-document\",\n                                    }),\n                                _vm._v(\n                                  \"\\n                template\\n              \"\n                                ),\n                              ]\n                            ),\n                          ]),\n                          _vm._v(\" \"),\n                          _c(\"el-tab-pane\", { attrs: { name: \"js\" } }, [\n                            _c(\n                              \"span\",\n                              { attrs: { slot: \"label\" }, slot: \"label\" },\n                              [\n                                _vm.activeTab === \"js\"\n                                  ? _c(\"i\", { staticClass: \"el-icon-edit\" })\n                                  : _c(\"i\", {\n                                      staticClass: \"el-icon-document\",\n                                    }),\n                                _vm._v(\n                                  \"\\n                script\\n              \"\n                                ),\n                              ]\n                            ),\n                          ]),\n                          _vm._v(\" \"),\n                          _c(\"el-tab-pane\", { attrs: { name: \"css\" } }, [\n                            _c(\n                              \"span\",\n                              { attrs: { slot: \"label\" }, slot: \"label\" },\n                              [\n                                _vm.activeTab === \"css\"\n                                  ? _c(\"i\", { staticClass: \"el-icon-edit\" })\n                                  : _c(\"i\", {\n                                      staticClass: \"el-icon-document\",\n                                    }),\n                                _vm._v(\"\\n                css\\n              \"),\n                              ]\n                            ),\n                          ]),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\"div\", {\n                        directives: [\n                          {\n                            name: \"show\",\n                            rawName: \"v-show\",\n                            value: _vm.activeTab === \"html\",\n                            expression: \"activeTab==='html'\",\n                          },\n                        ],\n                        staticClass: \"tab-editor\",\n                        attrs: { id: \"editorHtml\" },\n                      }),\n                      _vm._v(\" \"),\n                      _c(\"div\", {\n                        directives: [\n                          {\n                            name: \"show\",\n                            rawName: \"v-show\",\n                            value: _vm.activeTab === \"js\",\n                            expression: \"activeTab==='js'\",\n                          },\n                        ],\n                        staticClass: \"tab-editor\",\n                        attrs: { id: \"editorJs\" },\n                      }),\n                      _vm._v(\" \"),\n                      _c(\"div\", {\n                        directives: [\n                          {\n                            name: \"show\",\n                            rawName: \"v-show\",\n                            value: _vm.activeTab === \"css\",\n                            expression: \"activeTab==='css'\",\n                          },\n                        ],\n                        staticClass: \"tab-editor\",\n                        attrs: { id: \"editorCss\" },\n                      }),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-col\",\n                    { staticClass: \"right-preview\", attrs: { md: 24, lg: 12 } },\n                    [\n                      _c(\n                        \"div\",\n                        {\n                          staticClass: \"action-bar\",\n                          style: { \"text-align\": \"left\" },\n                        },\n                        [\n                          _c(\n                            \"span\",\n                            {\n                              staticClass: \"bar-btn\",\n                              on: { click: _vm.runCode },\n                            },\n                            [\n                              _c(\"i\", { staticClass: \"el-icon-refresh\" }),\n                              _vm._v(\"\\n              刷新\\n            \"),\n                            ]\n                          ),\n                          _vm._v(\" \"),\n                          _c(\n                            \"span\",\n                            {\n                              staticClass: \"bar-btn\",\n                              on: { click: _vm.exportFile },\n                            },\n                            [\n                              _c(\"i\", { staticClass: \"el-icon-download\" }),\n                              _vm._v(\n                                \"\\n              导出vue文件\\n            \"\n                              ),\n                            ]\n                          ),\n                          _vm._v(\" \"),\n                          _c(\n                            \"span\",\n                            { ref: \"copyBtn\", staticClass: \"bar-btn copy-btn\" },\n                            [\n                              _c(\"i\", { staticClass: \"el-icon-document-copy\" }),\n                              _vm._v(\"\\n              复制代码\\n            \"),\n                            ]\n                          ),\n                          _vm._v(\" \"),\n                          _c(\n                            \"span\",\n                            {\n                              staticClass: \"bar-btn delete-btn\",\n                              on: {\n                                click: function ($event) {\n                                  return _vm.$emit(\"update:visible\", false)\n                                },\n                              },\n                            },\n                            [\n                              _c(\"i\", { staticClass: \"el-icon-circle-close\" }),\n                              _vm._v(\"\\n              关闭\\n            \"),\n                            ]\n                          ),\n                        ]\n                      ),\n                      _vm._v(\" \"),\n                      _c(\"iframe\", {\n                        directives: [\n                          {\n                            name: \"show\",\n                            rawName: \"v-show\",\n                            value: _vm.isIframeLoaded,\n                            expression: \"isIframeLoaded\",\n                          },\n                        ],\n                        ref: \"previewPage\",\n                        staticClass: \"result-wrapper\",\n                        attrs: { frameborder: \"0\", src: \"preview.html\" },\n                        on: { load: _vm.iframeLoad },\n                      }),\n                      _vm._v(\" \"),\n                      _c(\"div\", {\n                        directives: [\n                          {\n                            name: \"show\",\n                            rawName: \"v-show\",\n                            value: !_vm.isIframeLoaded,\n                            expression: \"!isIframeLoaded\",\n                          },\n                          {\n                            name: \"loading\",\n                            rawName: \"v-loading\",\n                            value: true,\n                            expression: \"true\",\n                          },\n                        ],\n                        staticClass: \"result-wrapper\",\n                      }),\n                    ]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ]\n      ),\n      _vm._v(\" \"),\n      _c(\"resource-dialog\", {\n        attrs: {\n          visible: _vm.resourceVisible,\n          \"origin-resource\": _vm.resources,\n        },\n        on: {\n          \"update:visible\": function ($event) {\n            _vm.resourceVisible = $event\n          },\n          save: _vm.setResource,\n        },\n      }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}