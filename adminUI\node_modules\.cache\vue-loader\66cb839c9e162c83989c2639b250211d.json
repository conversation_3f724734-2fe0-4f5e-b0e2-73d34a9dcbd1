{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\loginFrom.vue?vue&type=template&id=5c8cb8c4&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\loginFrom.vue", "mtime": 1754050582486}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["\n<div class=\"login-container\">\n  <el-row type=\"flex\">\n    <el-col :span=\"24\">\n      <el-form ref=\"formInline\" size=\"small\" :model=\"formInline\" :rules=\"ruleInline\" class=\"login-form\"\n               autocomplete=\"on\" label-position=\"left\">\n        <div class=\"title-container\">\n          <h3 class=\"title mb15\">短信账户登录</h3>\n        </div>\n        <el-form-item prop=\"account\">\n          <el-input\n            ref=\"account\"\n            v-model=\"formInline.account\"\n            placeholder=\"用户名\"\n            prefix-icon=\"el-icon-user\"\n            name=\"username\"\n            type=\"text\"\n            tabindex=\"1\"\n            autocomplete=\"off\"\n          />\n        </el-form-item>\n        <el-form-item prop=\"password\">\n          <el-input\n            :key=\"passwordType\"\n            ref=\"password\"\n            v-model=\"formInline.password\"\n            :type=\"passwordType\"\n            placeholder=\"密码\"\n            name=\"password\"\n            tabindex=\"2\"\n            auto-complete=\"off\"\n            prefix-icon=\"el-icon-lock\"\n          />\n          <span class=\"show-pwd\" @click=\"showPwd\">\n            <svg-icon :icon-class=\"passwordType === 'password' ? 'eye' : 'eye-open'\"/>\n          </span>\n        </el-form-item>\n        <el-button size=\"mini\" :loading=\"loading\" type=\"primary\" style=\"width:100%;margin-bottom:20px;\"\n                   @click=\"handleSubmit('formInline')\">登录\n        </el-button>\n        <div class=\"acea-row row-center-wrapper mb20\">\n          <el-button size=\"mini\" type=\"text\" style=\"margin-left: 0\" @click=\"changePassword\">忘记密码</el-button>\n          <el-divider direction=\"vertical\"></el-divider>\n          <el-button size=\"mini\" type=\"text\" style=\"margin-left: 0\" @click=\"changeReg\">注册账户</el-button>\n        </div>\n        <el-tooltip class=\"item\" effect=\"dark\" content=\"\n            一号通为我司一个第三方平台\n            专门提供短信 ， 物流查询，商品复制，电子面单等个性化服务\n            省去了自己单独接入功能的麻烦\n            初次运行代码默认是没有账号的，需要自行注册，\n            登录成功后根据提示购买自己需要用到的服务即可\" placement=\"bottom\">\n          <span style=\"margin-left: 0\">平台说明</span>\n        </el-tooltip>\n      </el-form>\n    </el-col>\n  </el-row>\n</div>\n", null]}