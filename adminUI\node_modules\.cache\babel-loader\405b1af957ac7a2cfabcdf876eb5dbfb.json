{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\permissionRules\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\permissionRules\\index.vue", "mtime": 1754373878576}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _systemadmin = require(\"@/api/systemadmin\");\nvar _vueTreeselect = _interopRequireDefault(require(\"@riophae/vue-treeselect\"));\nrequire(\"@riophae/vue-treeselect/dist/vue-treeselect.css\");\nvar _validate = require(\"@/utils/validate\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"Menu\",\n  components: {\n    Treeselect: _vueTreeselect.default\n  },\n  data: function data() {\n    return {\n      // 遮罩层\n      listLoading: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 菜单表格树数据\n      menuList: [],\n      // 菜单树选项\n      menuOptions: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 是否展开，默认全部折叠\n      isExpandAll: false,\n      // 重新渲染表格状态\n      refreshTable: true,\n      // 查询参数\n      queryParams: {\n        name: \"\",\n        menuType: \"\"\n      },\n      // 表单参数\n      form: {},\n      // 请求到的menu数据\n      menuDataList: [],\n      // 表单校验\n      rules: {\n        name: [{\n          required: true,\n          message: this.$t(\"permissionRules.form.enterMenuName\"),\n          trigger: \"blur\"\n        }],\n        sort: [{\n          required: true,\n          message: this.$t(\"permissionRules.form.sortRequired\"),\n          trigger: \"blur\"\n        }]\n      },\n      statusOptions: [{\n        value: \"M\",\n        label: this.$t(\"permissionRules.menuType.directory\")\n      }, {\n        value: \"C\",\n        label: this.$t(\"permissionRules.menuType.menu\")\n      }, {\n        value: \"A\",\n        label: this.$t(\"permissionRules.menuType.button\")\n      }],\n      showStatus: [{\n        label: this.$t(\"common.show\"),\n        value: true\n      }, {\n        label: this.$t(\"common.hide\"),\n        value: false\n      }]\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    // 点击图标\n    addIcon: function addIcon() {\n      var _this = this;\n      _this.$modalIcon(function (icon) {\n        _this.form.icon = icon;\n      });\n    },\n    /** 查询菜单列表 */getList: function getList() {\n      var _this2 = this;\n      this.listLoading = true;\n      (0, _systemadmin.menuListApi)(this.queryParams).then(function (res) {\n        var obj = {},\n          menuList = [];\n        res.forEach(function (item) {\n          obj = item;\n          obj.parentId = item.pid;\n          obj.children = [];\n          menuList.push(obj);\n        });\n        _this2.menuDataList = menuList;\n        _this2.menuList = _this2.handleTree(menuList, \"menuId\");\n        _this2.listLoading = false;\n      });\n    },\n    /** 转换菜单数据结构 */normalizer: function normalizer(node) {\n      if (node.children && !node.children.length) {\n        delete node.children;\n      }\n      return {\n        id: node.id ? node.id : 0,\n        label: node.name ? node.name : \"主目录\",\n        children: node.children\n      };\n    },\n    /** 查询菜单下拉树结构 */getTreeselect: function getTreeselect() {\n      this.menuOptions = [];\n      var menu = {\n        menuId: 0,\n        menuName: \"主类目\",\n        children: []\n      };\n      menu.children = this.handleTree(this.menuDataList, \"menuId\");\n      this.menuOptions.push(menu);\n    },\n    // 取消按钮\n    cancel: function cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset: function reset() {\n      this.form = {\n        menuId: \"\",\n        parentId: 0,\n        name: \"\",\n        icon: \"\",\n        menuType: \"M\",\n        sort: 0,\n        isShow: true,\n        component: \"\",\n        perms: \"\"\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */handleQuery: function handleQuery() {\n      this.getList();\n    },\n    /** 重置按钮操作 */resetQuery: function resetQuery() {\n      this.queryParams = {\n        name: \"\",\n        menuType: \"\"\n      };\n      this.handleQuery();\n    },\n    /** 新增按钮操作 */handleAdd: function handleAdd(row) {\n      this.reset();\n      if (row != null && row.id) {\n        this.form.pid = row.id;\n      } else {\n        this.form.pid = 0;\n      }\n      this.open = true;\n      this.title = this.$t(\"permissionRules.actions.add\");\n    },\n    /** 展开/折叠操作 */toggleExpandAll: function toggleExpandAll() {\n      var _this3 = this;\n      this.refreshTable = false;\n      this.isExpandAll = !this.isExpandAll;\n      this.$nextTick(function () {\n        _this3.refreshTable = true;\n      });\n    },\n    /** 修改按钮操作 */handleUpdate: function handleUpdate(row) {\n      var _this4 = this;\n      var loading = this.$loading({\n        lock: true,\n        text: \"Loading\"\n      });\n      this.reset();\n      this.getTreeselect();\n      (0, _systemadmin.menuInfo)(row.id).then(function (response) {\n        _this4.form = response;\n        _this4.open = true;\n        _this4.title = _this4.$t(\"permissionRules.actions.edit\");\n        loading.close();\n      });\n    },\n    /** 提交按钮 */\n    submitForm: (0, _validate.Debounce)(function () {\n      var _this5 = this;\n      this.$refs[\"form\"].validate(function (valid) {\n        if (valid) {\n          if (_this5.form.id != undefined) {\n            (0, _systemadmin.menuUpdate)(_this5.form).then(function (response) {\n              _this5.$modal.msgSuccess(_this5.$t(\"common.editSuccess\"));\n              _this5.open = false;\n              _this5.getList();\n            });\n          } else {\n            (0, _systemadmin.menuAdd)(_this5.form).then(function (response) {\n              _this5.$modal.msgSuccess(_this5.$t(\"common.addSuccess\"));\n              _this5.open = false;\n              _this5.getList();\n            });\n          }\n        }\n      });\n    }),\n    /** 删除按钮操作 */handleDelete: function handleDelete(row) {\n      var _this6 = this;\n      this.$modal.confirm(this.$t(\"common.confirmDelete\").replace(\"{name}\", row.name)).then(function () {\n        return (0, _systemadmin.menuDelete)(row.id);\n      }).then(function () {\n        _this6.getList();\n        _this6.$modal.msgSuccess(_this6.$t(\"common.deleteSuccess\"));\n      }).catch(function () {});\n    }\n  }\n};", null]}