{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillList\\creatSeckill.vue?vue&type=template&id=0be8f38b&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillList\\creatSeckill.vue", "mtime": 1754050582454}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <el-steps :active=\"currentTab\" align-center finish-status=\"success\">\n        <el-step title=\"选择秒杀商品\" />\n        <el-step title=\"填写基础信息\" />\n        <el-step title=\"修改商品详情\" />\n      </el-steps>\n    </div>\n    <el-form\n      ref=\"formValidate\"\n      v-loading=\"fullscreenLoading\"\n      class=\"formValidate mt20\"\n      :rules=\"ruleValidate\"\n      :model=\"formValidate\"\n      label-width=\"150px\"\n      @submit.native.prevent\n    >\n      <!-- 秒杀商品-->\n      <div v-show=\"currentTab === 0\">\n        <el-form-item label=\"选择商品：\" prop=\"image\">\n          <div class=\"upLoadPicBox\" @click=\"changeGood\">\n            <div v-if=\"formValidate.image\" class=\"pictrue\"><img :src=\"formValidate.image\"></div>\n            <div v-else class=\"upLoad\">\n              <i class=\"el-icon-camera cameraIconfont\"/>\n            </div>\n          </div>\n        </el-form-item>\n      </div>\n      <!-- 商品信息-->\n      <div v-show=\"currentTab === 1\">\n        <el-row :gutter=\"24\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"商品主图：\" prop=\"image\">\n              <div class=\"upLoadPicBox\" @click=\"modalPicTap('1')\">\n                <div v-if=\"formValidate.image\" class=\"pictrue\"><img :src=\"formValidate.image\"></div>\n                <div v-else class=\"upLoad\">\n                  <i class=\"el-icon-camera cameraIconfont\" />\n                </div>\n              </div>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"商品轮播图：\" prop=\"imagess\">\n              <div class=\"acea-row\">\n                <div\n                  v-for=\"(item,index) in formValidate.imagess\"\n                  :key=\"index\"\n                  class=\"pictrue\"\n                  draggable=\"true\"\n                  @dragstart=\"handleDragStart($event, item)\"\n                  @dragover.prevent=\"handleDragOver($event, item)\"\n                  @dragenter=\"handleDragEnter($event, item)\"\n                  @dragend=\"handleDragEnd($event, item)\"\n                >\n                  <img :src=\"item\">\n                  <i class=\"el-icon-error btndel\" @click=\"handleRemove(index)\" />\n                </div>\n                <div v-if=\"formValidate.imagess.length<10\" class=\"upLoadPicBox\" @click=\"modalPicTap('2')\">\n                  <div class=\"upLoad\">\n                    <i class=\"el-icon-camera cameraIconfont\" />\n                  </div>\n                </div>\n              </div>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"商品标题：\" prop=\"title\">\n              <el-input v-model=\"formValidate.title\" maxlength=\"249\" placeholder=\"请输入商品名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"秒杀活动简介：\">\n              <el-input v-model=\"formValidate.info\" type=\"textarea\" maxlength=\"250\"  :rows=\"3\" placeholder=\"请输入商品简介\" />\n            </el-form-item>\n          </el-col>\n          <!--<el-col v-bind=\"grid2\">-->\n            <!--<el-form-item label=\"商品分类：\" prop=\"cateIds\">-->\n              <!--<el-cascader v-model=\"formValidate.cateIds\" :options=\"merCateList\" :props=\"props2\" clearable class=\"selWidth\" :show-all-levels=\"false\" />-->\n            <!--</el-form-item>-->\n          <!--</el-col>-->\n          <el-col v-bind=\"grid2\">\n            <el-form-item label=\"单位：\" prop=\"unitName\">\n              <el-input v-model=\"formValidate.unitName\" placeholder=\"请输入单位\" class=\"selWidth\"/>\n            </el-form-item>\n          </el-col>\n          <el-col v-bind=\"grid2\">\n            <el-form-item label=\"运费模板：\" prop=\"tempId\">\n              <div class=\"acea-row\">\n                <el-select v-model=\"formValidate.tempId\" placeholder=\"请选择\"  class=\"selWidth\">\n                <el-option\n                    v-for=\"item in shippingList\"\n                    :key=\"item.id\"\n                    :label=\"item.name\"\n                    :value=\"item.id\"\n                  />\n                </el-select>\n                <!--<el-button class=\"mr15\" @click=\"addTem\">添加运费模板</el-button>-->\n              </div>\n            </el-form-item>\n          </el-col>\n          <el-col v-bind=\"grid2\">\n            <el-form-item label=\"当天参与活动次数：\" prop=\"num\">\n              <el-input-number v-model=\"formValidate.num\" :step=\"1\" step-strictly :min=\"1\" placeholder=\"请输入活动次数\" class=\"selWidth\"/>\n            </el-form-item>\n          </el-col>\n          <el-col v-bind=\"grid2\">\n            <el-form-item label=\"活动日期：\" prop=\"timeVal\">\n              <el-date-picker\n                class=\"selWidth\"\n                v-model=\"formValidate.timeVal\"\n                value-format=\"yyyy-MM-dd\"\n                format=\"yyyy-MM-dd\"\n                size=\"small\"\n                type=\"daterange\"\n                placement=\"bottom-end\"\n                :picker-options=\"pickerOptions\"\n                placeholder=\"请选择活动日期\"\n                @change=\"onchangeTime\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col v-bind=\"grid2\">\n            <el-form-item label=\"活动时间：\" prop=\"timeId\">\n              <el-select v-model=\"formValidate.timeId\" placeholder=\"请选择\" class=\"selWidth\">\n                <el-option\n                  v-for=\"item in seckillTime\"\n                  :key=\"item.id\"\n                  :label=\"item.name + ' | ' + item.time\"\n                  :value=\"item.id\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"活动状态：\" required>\n              <el-radio-group v-model=\"formValidate.status\">\n                <el-radio :label=\"0\" class=\"radio\">关闭</el-radio>\n                <el-radio :label=\"1\">开启</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <!-- 规格表格-->\n          <el-col :span=\"24\">\n            <el-form-item label=\"商品属性：\" class=\"labeltop\" required>\n              <el-table\n                ref=\"multipleTable\"\n                :data=\"ManyAttrValue\"\n                tooltip-effect=\"dark\"\n                style=\"width: 100%\"\n                @selection-change=\"handleSelectionChange\">\n                <el-table-column\n                  type=\"selection\"\n                  key=\"1\"\n                  v-if=\"formValidate.specType\"\n                  width=\"55\">\n                </el-table-column>\n                <template v-if=\"manyTabDate && formValidate.specType\">\n                  <el-table-column v-for=\"(item,iii) in manyTabDate\" :key=\"iii\" align=\"center\" :label=\"manyTabTit[iii].title\" min-width=\"100\">\n                    <template slot-scope=\"scope\">\n                      <span class=\"priceBox\" v-text=\"scope.row[iii]\"  />\n                    </template>\n                  </el-table-column>\n                </template>\n                <el-table-column align=\"center\" label=\"图片\" min-width=\"80\">\n                  <template slot-scope=\"scope\">\n                    <div class=\"upLoadPicBox\" @click=\"modalPicTap('1','duo',scope.$index)\">\n                      <div v-if=\"scope.row.image\" class=\"pictrue tabPic\"><img :src=\"scope.row.image\"></div>\n                      <div v-else class=\"upLoad tabPic\">\n                        <i class=\"el-icon-camera cameraIconfont\" />\n                      </div>\n                    </div>\n                  </template>\n                </el-table-column>\n                <el-table-column v-for=\"(item,iii) in attrValue\" :key=\"iii\" :label=\"formThead[iii].title\" align=\"center\" min-width=\"145\">\n                  <template slot-scope=\"scope\">\n                    <el-input-number\n                      size=\"small\"\n                      v-if=\"formThead[iii].title === '秒杀价'\"\n                      v-model=\"scope.row[iii]\"\n                      :min=\"0\"\n                      :precision=\"2\" :step=\"0.1\"\n                      class=\"priceBox\"\n                    />\n                    <el-input-number\n                      size=\"small\"\n                      v-else-if=\"formThead[iii].title === '限量'\"\n                      v-model=\"scope.row[iii]\"\n                      type=\"number\"\n                      :min=\"1\"\n                      :max=\"scope.row.stock\"\n                      :step=\"1\" step-strictly\n                      class=\"priceBox\"\n                    />\n                    <span v-else v-text=\"scope.row[iii]\" class=\"priceBox\" />\n                    <!--<el-input v-model=\"scope.row[iii]\" :type=\"formThead[iii].title==='商品编号'?'text':'number'\" class=\"priceBox\" />-->\n                  </template>\n                </el-table-column>\n              </el-table>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </div>\n      <!-- 商品详情-->\n      <div v-show=\"currentTab === 2\">\n        <el-form-item label=\"商品详情：\">\n           <Tinymce v-model=\"formValidate.content\"></Tinymce>\n        </el-form-item>\n      </div>\n      <el-form-item style=\"margin-top:30px;\">\n        <el-button\n          v-show=\"(!$route.params.id && currentTab > 0) || ($route.params.id && currentTab===2)\"\n          type=\"primary\"\n          class=\"submission\"\n          size=\"small\"\n          @click=\"handleSubmitUp\"\n        >上一步</el-button>\n        <el-button\n          v-show=\"currentTab == 0\"\n          type=\"primary\"\n          class=\"submission\"\n          size=\"small\"\n          @click=\"handleSubmitNest1('formValidate')\"\n        >下一步</el-button>\n        <el-button\n          v-show=\"currentTab == 1\"\n          type=\"primary\"\n          class=\"submission\"\n          size=\"small\"\n          @click=\"handleSubmitNest2('formValidate')\"\n        >下一步</el-button>\n        <el-button\n          v-show=\"currentTab===2\"\n          :loading=\"loading\"\n          type=\"primary\"\n          class=\"submission\"\n          size=\"small\"\n          @click=\"handleSubmit('formValidate')\"\n          v-hasPermi=\"['admin:seckill:update']\"\n        >提交</el-button>\n      </el-form-item>\n    </el-form>\n  </el-card>\n  <CreatTemplates ref=\"addTemplates\" @getList=\"getShippingList\" />\n</div>\n", null]}