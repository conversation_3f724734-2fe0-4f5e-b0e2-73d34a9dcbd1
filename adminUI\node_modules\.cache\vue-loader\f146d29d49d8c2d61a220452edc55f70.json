{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\Logo.vue?vue&type=template&id=6494804b&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\Logo.vue", "mtime": 1754273069867}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    {\n      staticClass: \"sidebar-logo-container\",\n      class: { collapse: _vm.collapse },\n    },\n    [\n      _c(\n        \"transition\",\n        { attrs: { name: \"sidebarLogoFade\" } },\n        [\n          _vm.collapse\n            ? _c(\n                \"router-link\",\n                {\n                  key: \"collapse\",\n                  staticClass: \"sidebar-logo-link\",\n                  attrs: { to: \"/\" },\n                },\n                [\n                  _vm.logoSmall\n                    ? _c(\"img\", {\n                        staticClass: \"sidebar-logo-small\",\n                        attrs: { src: _vm.logoSmall },\n                      })\n                    : _vm._e(),\n                ]\n              )\n            : _c(\n                \"router-link\",\n                {\n                  key: \"expand\",\n                  staticClass: \"sidebar-logo-link\",\n                  attrs: { to: \"/\" },\n                },\n                [\n                  _c(\"img\", {\n                    staticClass: \"sidebar-logo-big\",\n                    attrs: { src: _vm.logobg },\n                  }),\n                ]\n              ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}