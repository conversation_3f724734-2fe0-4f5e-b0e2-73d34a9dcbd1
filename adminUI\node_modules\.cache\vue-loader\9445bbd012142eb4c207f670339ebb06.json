{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\groupBuy\\groupList\\index.vue?vue&type=template&id=0777705b&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\groupBuy\\groupList\\index.vue", "mtime": 1754050582448}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <div class=\"mt10\">\n    <cards-data :cardLists=\"cardLists\" v-if=\"checkPermi(['admin:combination:statistics'])\"></cards-data>\n  </div>\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <div class=\"container\">\n        <el-form size=\"small\" label-width=\"100px\">\n          <el-form-item label=\"时间选择：\" class=\"width100\">\n            <el-radio-group v-model=\"tableFrom.dateLimit\" type=\"button\" class=\"mr20\" size=\"small\" @change=\"selectChange(tableFrom.dateLimit)\">\n              <el-radio-button v-for=\"(item,i) in fromList.fromTxt\" :key=\"i\" :label=\"item.val\">{{ item.text }}</el-radio-button>\n            </el-radio-group>\n            <el-date-picker v-model=\"timeVal\" value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\" size=\"small\" type=\"daterange\" placement=\"bottom-end\" placeholder=\"自定义时间\" style=\"width: 250px;\" @change=\"onchangeTime\" />\n          </el-form-item>\n          <el-form-item label=\"拼团状态：\">\n            <el-select v-model=\"tableFrom.status\" placeholder=\"请选择\" class=\"filter-item selWidth mr20\" @change=\"getList(1)\" clearable>\n              <el-option label=\"进行中\" :value=\"1\" />\n              <el-option label=\"已成功\" :value=\"2\" />\n              <el-option label=\"未完成\" :value=\"3\" />\n            </el-select>\n          </el-form-item>\n        </el-form>\n      </div>\n    </div>\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      ref=\"multipleTable\"\n      highlight-current-row\n      :header-cell-style=\" {fontWeight:'bold'}\"\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        min-width=\"50\"\n      />\n      <el-table-column label=\"头像\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"scope.row.avatar\"\n              :preview-src-list=\"[scope.row.avatar]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"开团团长\"\n        prop=\"nickname\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        label=\"开团时间\"\n        prop=\"addTime\"\n        min-width=\"130\"\n      />\n      <el-table-column \n      label=\"拼团商品\" \n      prop=\"title\" \n      min-width=\"300\" \n      :show-overflow-tooltip=\"true\">\n      </el-table-column>\n      <el-table-column\n        label=\"几人团\"\n        prop=\"people\"\n        min-width=\"100\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"几人参加\"\n        prop=\"countPeople\"\n        min-width=\"100\"\n        align=\"center\"\n      />\n      <el-table-column\n        prop=\"stopTime\"\n        label=\"结束时间\"\n        min-width=\"130\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"拼团状态\"\n        min-width=\"150\"\n        align=\"center\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"scope.row.status | groupColorFilter\">{{scope.row.status | groupStatusFilter}}</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" min-width=\"150\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" size=\"small\" @click=\"handleLook(scope.row.id)\"  class=\"mr10\">查看详情</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block mb20\">\n      <el-pagination\n        :page-sizes=\"[10, 20, 30, 40]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n\n  <el-dialog\n    title=\"查看详情\"\n    :visible.sync=\"dialogVisible\"\n    width=\"650px\"\n    :before-close=\"handleClose\">\n    <el-table\n      v-loading=\"listLoadingPink\"\n      :data=\"tableDataPink.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      ref=\"multipleTable\"\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        min-width=\"50\"\n      />\n      <el-table-column label=\"头像\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"scope.row.avatar\"\n              :preview-src-list=\"[scope.row.avatar]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"用户名称\"\n        prop=\"nickname\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        label=\"订单编号\"\n        prop=\"orderId\"\n        min-width=\"180\"\n      />\n      <el-table-column\n        label=\"金额\"\n        prop=\"totalPrice\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        label=\"订单状态\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.refundStatus==0\">{{scope.row.orderStatus | orderStatusFilter}}</span>\n          <span v-else>{{scope.row.refundStatus | refundStatusFilter}}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n  </el-dialog>\n</div>\n", null]}