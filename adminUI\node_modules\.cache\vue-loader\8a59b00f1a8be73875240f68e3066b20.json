{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\AppMain.vue?vue&type=style&index=0&id=078753dd&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\AppMain.vue", "mtime": 1754050582335}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\css-loader\\index.js", "mtime": 1754554385075}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754554389499}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754554387093}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1754554384522}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n.footers {\r\n  text-align: center;\r\n  font-size: 14px;\r\n  color: #808695;\r\n  .title {\r\n    font-size: 14px;\r\n    color: #808695;\r\n  }\r\n}\r\n.app-main {\r\n  /* 50= navbar  50  */\r\n  min-height: calc(100vh - 50px);\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.fixed-header + .app-main {\r\n  padding-top: 50px;\r\n}\r\n\r\n.hasTagsView {\r\n  .app-main {\r\n    background: #f5f5f5;\r\n    /* 84 = navbar + tags-view = 50 + 34 */\r\n    min-height: calc(100vh - 84px);\r\n    overflow-y: scroll;\r\n  }\r\n\r\n  .fixed-header + .app-main {\r\n    padding-top: 80px;\r\n  }\r\n}\r\n.el-popup-parent--hidden {\r\n  .fixed-header {\r\n    padding-right: 15px;\r\n  }\r\n}\r\n", null]}