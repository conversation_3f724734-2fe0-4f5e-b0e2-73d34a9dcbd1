{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\index.vue?vue&type=style&index=0&id=0a5e70de&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\index.vue", "mtime": 1754050582488}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\css-loader\\index.js", "mtime": 1754554385075}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754554389499}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754554387093}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1754554384522}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  .dashboard{\n    width: auto !important;\n    min-width: 300px;\n  }\n  .header-extra{\n    /*width: 25%;*/\n    border-right: 1px solid #E9E9E9;\n    text-align: center;\n    padding: 0 18px;\n  }\n  $cursor: #1890ff;\n  .content{\n    justify-content: space-between;\n  }\n  .circleUrl{\n    width: 50px;\n    height: 50px;\n  }\n  .circleUrl img{\n    width: 100%;\n    height: 100%;\n    border-radius: 50%;\n    overflow: hidden;\n  }\n  .rR{\n    text-align: center;\n    font-size: 22px;\n    display: block;\n  }\n  .dashboard-workplace-header-tip {\n    display: inline-block;\n    vertical-align: middle;\n  }\n  .dashboard-workplace-header-tip-title {\n    font-size: 20px;\n    font-weight: 700;\n    margin-bottom: 12px;\n  }\n  .dashboard-workplace-header-tip-desc{\n    /*line-height: 0 !important;*/\n    display: block;\n    span{\n      font-size: 12px;\n      color: $cursor;\n      cursor: pointer;\n      display: inline-block;\n    }\n  }\n  .dashboard-workplace-header-extra{\n    width: auto!important;\n    min-width: 400px;\n  }\n  .pfont{\n    font-size: 12px;\n    color: #808695;\n  }\n  .text_overflow{\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.pup_card{\n  width: 240px;\n  border-radius: 5px;\n  padding: 5px;\n  box-sizing: border-box;\n  font-size: 12px;\n  line-height: 16px;\n}\n", null]}