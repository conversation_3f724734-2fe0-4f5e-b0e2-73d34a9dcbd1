{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\deliveryAddress\\addPoint.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\deliveryAddress\\addPoint.vue", "mtime": 1754050582507}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _storePoint = require(\"@/api/storePoint\");\nvar logistics = _interopRequireWildcard(require(\"@/api/logistics\"));\nvar _systemConfig = require(\"@/api/systemConfig\");\nvar _index = _interopRequireDefault(require(\"../../../../appSetting/wxAccount/wxTemplate/index\"));\nvar _validate = require(\"@/utils/validate\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; } //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"index\",\n  components: {\n    Templates: _index.default\n  },\n  // props: {\n  //   children: 'child',\n  //   label: 'name',\n  //   value: 'name'\n  // },\n  data: function data() {\n    var _this2 = this;\n    var validatePhone = function validatePhone(rule, value, callback) {\n      if (!value) {\n        return callback(new Error('请填写手机号'));\n      } else if (!/^1[3456789]\\d{9}$/.test(value)) {\n        callback(new Error('手机号格式不正确!'));\n      } else {\n        callback();\n      }\n    };\n    var validateUpload = function validateUpload(rule, value, callback) {\n      if (!_this2.ruleForm.image) {\n        callback(new Error('请上传提货点logo'));\n      } else {\n        callback();\n      }\n    };\n    return {\n      loading: false,\n      dialogFormVisible: false,\n      modalMap: false,\n      keyUrl: '',\n      addresData: [],\n      ruleForm: {\n        name: '',\n        introduction: '',\n        phone: '',\n        address: '',\n        detailedAddress: '',\n        dayTime: '',\n        image: '',\n        latitude: ''\n      },\n      id: 0,\n      dayTime: ['', ''],\n      rules: {\n        name: [{\n          required: true,\n          message: '请输入提货点名称',\n          trigger: 'blur'\n        }],\n        address: [{\n          required: true,\n          message: '请选择提货点地址',\n          trigger: 'change'\n        }],\n        dayTime: [{\n          required: true,\n          type: 'array',\n          message: '请选择提货点营业时间',\n          trigger: 'change'\n        }],\n        phone: [{\n          required: true,\n          validator: validatePhone,\n          trigger: 'blur'\n        }],\n        detailedAddress: [{\n          required: true,\n          message: '请输入详细地址',\n          trigger: 'blur'\n        }],\n        image: [{\n          required: true,\n          validator: validateUpload,\n          trigger: 'change'\n        }],\n        latitude: [{\n          required: true,\n          message: '请选择经纬度',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  created: function created() {\n    this.ruleForm.image = '';\n    var cityList = JSON.parse(sessionStorage.getItem('cityList'));\n    this.addresData = cityList;\n    this.getCityList();\n    this.getKey();\n  },\n  mounted: function mounted() {\n    window.addEventListener('message', function (event) {\n      // 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息\n      var loc = event.data;\n      if (loc && loc.module === 'locationPicker') {\n        // 防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'\n        window.parent.selectAdderss(loc);\n      }\n    }, false);\n    window.selectAdderss = this.selectAdderss;\n  },\n  methods: {\n    //详情\n    getInfo: function getInfo(id) {\n      var _this3 = this;\n      var that = this;\n      that.id = id;\n      this.loading = true;\n      (0, _storePoint.storeInfoApi)({\n        id: id\n      }).then(function (res) {\n        that.ruleForm = res;\n        that.ruleForm.address = res.address.split(\",\");\n        that.dayTime = res.dayTime.split(\",\");\n        _this3.loading = false;\n      });\n    },\n    //取消\n    cancel: function cancel() {\n      this.dialogFormVisible = false;\n      this.clearFrom();\n      this.ruleForm.image = '';\n      this.resetForm('ruleForm');\n      this.id = 0;\n    },\n    //重置\n    resetForm: function resetForm(name) {\n      this.$refs[name].resetFields();\n    },\n    // 提交\n    submitForm: (0, _validate.Debounce)(function (name) {\n      var _this4 = this;\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          (0, _storePoint.storeSaveApi)(_this4.ruleForm).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n            return _regenerator().w(function (_context) {\n              while (1) switch (_context.n) {\n                case 0:\n                  _this4.$message.success('提交成功');\n                  _this4.dialogFormVisible = false;\n                  _this4.$parent.tableList();\n                  _this4.$parent.storeGetCount();\n                  _this4.clearFrom();\n                  _this4.resetForm(name);\n                  _this4.id = 0;\n                case 1:\n                  return _context.a(2);\n              }\n            }, _callee);\n          })));\n        } else {\n          return false;\n        }\n      });\n    }),\n    //编辑\n    editForm: (0, _validate.Debounce)(function (name) {\n      var _this5 = this;\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          _this5.handleChange(_this5.ruleForm.address);\n          (0, _storePoint.storeUpdateApi)(_this5.ruleForm, _this5.id).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {\n            return _regenerator().w(function (_context2) {\n              while (1) switch (_context2.n) {\n                case 0:\n                  _this5.$message.success('编辑成功');\n                  _this5.dialogFormVisible = false;\n                  _this5.$parent.tableList();\n                  _this5.clearFrom();\n                  _this5.resetForm(name);\n                  _this5.id = 0;\n                case 1:\n                  return _context2.a(2);\n              }\n            }, _callee2);\n          })));\n        } else {\n          return false;\n        }\n      });\n    }),\n    //数据归为初始状态\n    clearFrom: function clearFrom() {\n      this.ruleForm.introduction = '';\n      this.dayTime = ['', ''];\n    },\n    //确认省市区\n    handleChange: function handleChange(e) {\n      var province = e[0];\n      var city = e[1];\n      var area = e[2];\n      if (e.length === 2) {\n        this.ruleForm.address = province + ',' + city;\n      } else if (e.length === 3) {\n        this.ruleForm.address = province + ',' + city + ',' + area;\n      }\n    },\n    //营业时间\n    onchangeTime: function onchangeTime(e) {\n      this.ruleForm.dayTime = e ? e.join(',') : '';\n    },\n    //上传图片\n    modalPicTap: function modalPicTap(tit) {\n      var _this = this;\n      this.$modalUpload(function (img) {\n        _this.ruleForm.image = img[0].sattDir;\n      }, tit, 'system');\n    },\n    //查找位置\n    onSearch: function onSearch() {\n      this.modalMap = true;\n    },\n    // 选择经纬度\n    selectAdderss: function selectAdderss(data) {\n      this.ruleForm.latitude = data.latlng.lat + ',' + data.latlng.lng;\n      this.modalMap = false;\n    },\n    // key值\n    getKey: function getKey() {\n      var _this6 = this;\n      var _pram = {\n        id: 74\n      };\n      (0, _systemConfig.configInfo)(_pram).then(/*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(res) {\n          var keys;\n          return _regenerator().w(function (_context3) {\n            while (1) switch (_context3.n) {\n              case 0:\n                keys = res.tengxun_map_key;\n                _this6.keyUrl = \"https://apis.map.qq.com/tools/locpicker?type=1&key=\".concat(keys, \"&referer=myapp\");\n              case 1:\n                return _context3.a(2);\n            }\n          }, _callee3);\n        }));\n        return function (_x) {\n          return _ref3.apply(this, arguments);\n        };\n      }());\n    },\n    getCityList: function getCityList() {\n      var _this7 = this;\n      logistics.cityListTree().then(function (res) {\n        sessionStorage.setItem('cityList', JSON.stringify(res));\n        var cityList = JSON.parse(sessionStorage.getItem('cityList'));\n        _this7.addresData = cityList;\n      }).catch(function (res) {\n        _this7.$message.error(res.message);\n      });\n    }\n  }\n};", null]}