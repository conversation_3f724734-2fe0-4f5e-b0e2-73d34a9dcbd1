{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\integral\\integralLog\\index.vue?vue&type=template&id=63da5f0a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\integral\\integralLog\\index.vue", "mtime": 1754050582451}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _c(\n                \"div\",\n                { staticClass: \"container\" },\n                [\n                  _c(\n                    \"el-form\",\n                    { attrs: { size: \"small\", \"label-width\": \"120px\" } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        {\n                          staticClass: \"width100\",\n                          attrs: { label: \"时间选择：\" },\n                        },\n                        [\n                          _c(\n                            \"el-radio-group\",\n                            {\n                              staticClass: \"mr20\",\n                              attrs: { type: \"button\", size: \"small\" },\n                              on: {\n                                change: function ($event) {\n                                  return _vm.selectChange(\n                                    _vm.tableFrom.dateLimit\n                                  )\n                                },\n                              },\n                              model: {\n                                value: _vm.tableFrom.dateLimit,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tableFrom, \"dateLimit\", $$v)\n                                },\n                                expression: \"tableFrom.dateLimit\",\n                              },\n                            },\n                            _vm._l(_vm.fromList.fromTxt, function (item, i) {\n                              return _c(\n                                \"el-radio-button\",\n                                { key: i, attrs: { label: item.val } },\n                                [_vm._v(_vm._s(item.text) + \"\\n              \")]\n                              )\n                            }),\n                            1\n                          ),\n                          _vm._v(\" \"),\n                          _c(\"el-date-picker\", {\n                            staticStyle: { width: \"250px\" },\n                            attrs: {\n                              \"value-format\": \"yyyy-MM-dd\",\n                              format: \"yyyy-MM-dd\",\n                              size: \"small\",\n                              type: \"daterange\",\n                              placement: \"bottom-end\",\n                              placeholder: \"自定义时间\",\n                            },\n                            on: { change: _vm.onchangeTime },\n                            model: {\n                              value: _vm.timeVal,\n                              callback: function ($$v) {\n                                _vm.timeVal = $$v\n                              },\n                              expression: \"timeVal\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"用户微信昵称：\" } },\n                        [\n                          _c(\n                            \"el-input\",\n                            {\n                              staticClass: \"selWidth\",\n                              attrs: {\n                                placeholder: \"请输入用户昵称\",\n                                size: \"small\",\n                              },\n                              model: {\n                                value: _vm.tableFrom.keywords,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tableFrom, \"keywords\", $$v)\n                                },\n                                expression: \"tableFrom.keywords\",\n                              },\n                            },\n                            [\n                              _c(\"el-button\", {\n                                attrs: {\n                                  slot: \"append\",\n                                  icon: \"el-icon-search\",\n                                  size: \"small\",\n                                },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.getList(1)\n                                  },\n                                },\n                                slot: \"append\",\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.listLoading,\n                  expression: \"listLoading\",\n                },\n              ],\n              staticClass: \"table\",\n              attrs: {\n                data: _vm.tableData.data,\n                size: \"small\",\n                \"highlight-current-row\": \"\",\n                \"header-cell-style\": { fontWeight: \"bold\" },\n              },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { prop: \"id\", label: \"ID\", width: \"60\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"title\", label: \"标题\", \"min-width\": \"130\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  sortable: \"\",\n                  prop: \"balance\",\n                  label: \"积分余量\",\n                  \"min-width\": \"120\",\n                  \"sort-method\": function (a, b) {\n                    return a.balance - b.balance\n                  },\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  sortable: \"\",\n                  label: \"明细数字\",\n                  \"min-width\": \"120\",\n                  prop: \"integral\",\n                  \"sort-method\": function (a, b) {\n                    return a.integral - b.integral\n                  },\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"备注\", \"min-width\": \"120\", prop: \"mark\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"用户昵称\",\n                  \"min-width\": \"120\",\n                  prop: \"nickName\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"updateTime\",\n                  label: \"\\t添加时间\",\n                  \"min-width\": \"150\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"block\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"page-sizes\": [20, 40, 60, 80],\n                  \"page-size\": _vm.tableFrom.limit,\n                  \"current-page\": _vm.tableFrom.page,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.tableData.total,\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChange,\n                  \"current-change\": _vm.pageChange,\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}