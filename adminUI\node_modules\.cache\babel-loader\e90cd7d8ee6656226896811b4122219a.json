{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Settings\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Settings\\index.vue", "mtime": 1754050582337}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _ThemePicker = _interopRequireDefault(require(\"@/components/ThemePicker\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  components: {\n    ThemePicker: _ThemePicker.default\n  },\n  data: function data() {\n    return {\n      sideTheme: this.$store.state.settings.sideTheme,\n      routers: this.$store.state.permission.routes\n    };\n  },\n  computed: {\n    theme: {\n      get: function get() {\n        return this.$store.state.settings.theme;\n      }\n    },\n    fixedHeader: {\n      get: function get() {\n        return this.$store.state.settings.fixedHeader;\n      },\n      set: function set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'fixedHeader',\n          value: val\n        });\n      }\n    },\n    tagsView: {\n      get: function get() {\n        return this.$store.state.settings.tagsView;\n      },\n      set: function set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'tagsView',\n          value: val\n        });\n      }\n    },\n    sidebarLogo: {\n      get: function get() {\n        return this.$store.state.settings.sidebarLogo;\n      },\n      set: function set(val) {\n        this.$store.dispatch('settings/changeSetting', {\n          key: 'sidebarLogo',\n          value: val\n        });\n      }\n    }\n  },\n  methods: {\n    themeChange: function themeChange(val) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'theme',\n        value: val\n      });\n    },\n    handleTheme: function handleTheme(val) {\n      this.$store.dispatch('settings/changeSetting', {\n        key: 'sideTheme',\n        value: val\n      });\n      this.sideTheme = val;\n    },\n    saveSetting: function saveSetting() {\n      this.$modal.loading(\"正在保存到本地，请稍候...\");\n      //将设置写入缓存\n      this.$cache.local.setJSON('layout-setting', {\n        \"topNav\": this.topNav,\n        \"tagsView\": this.tagsView,\n        \"fixedHeader\": this.fixedHeader,\n        \"sidebarLogo\": this.sidebarLogo,\n        \"dynamicTitle\": this.dynamicTitle,\n        \"sideTheme\": this.sideTheme,\n        \"theme\": this.theme,\n        \"navIcon\": this.navIcon\n      });\n      setTimeout(this.$modal.closeLoading(), 1000);\n    },\n    resetSetting: function resetSetting() {\n      this.$modal.loading(\"正在清除设置缓存并刷新，请稍候...\");\n      this.$cache.local.remove(\"layout-setting\");\n      setTimeout(\"window.location.reload()\", 1000);\n    }\n  }\n};", null]}