{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\deliveryAddress\\index.vue?vue&type=template&id=411641cd&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\deliveryAddress\\index.vue", "mtime": 1754050582508}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _vm.checkPermi([\n                \"admin:system:store:count\",\n                \"admin:system:store:list\",\n              ])\n                ? _c(\n                    \"el-tabs\",\n                    {\n                      on: { \"tab-click\": _vm.onClickTab },\n                      model: {\n                        value: _vm.artFrom.status,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.artFrom, \"status\", $$v)\n                        },\n                        expression: \"artFrom.status\",\n                      },\n                    },\n                    [\n                      _c(\"el-tab-pane\", {\n                        attrs: {\n                          label: \"显示中的提货点(\" + _vm.headerCount.show + \")\",\n                          name: \"1\",\n                        },\n                      }),\n                      _vm._v(\" \"),\n                      _c(\"el-tab-pane\", {\n                        attrs: {\n                          label: \"隐藏中的提货点(\" + _vm.headerCount.hide + \")\",\n                          name: \"0\",\n                        },\n                      }),\n                      _vm._v(\" \"),\n                      _c(\"el-tab-pane\", {\n                        attrs: {\n                          label:\n                            \"回收站的提货点(\" + _vm.headerCount.recycle + \")\",\n                          name: \"2\",\n                        },\n                      }),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _c(\n                \"el-form\",\n                {\n                  ref: \"form\",\n                  attrs: { inline: \"\", model: _vm.artFrom },\n                  nativeOn: {\n                    submit: function ($event) {\n                      $event.preventDefault()\n                    },\n                  },\n                },\n                [\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"关键字：\" } },\n                    [\n                      _c(\n                        \"el-input\",\n                        {\n                          staticClass: \"selWidth\",\n                          attrs: {\n                            placeholder: \"请输入提货点名称/电话\",\n                            size: \"small\",\n                            clearable: \"\",\n                          },\n                          model: {\n                            value: _vm.artFrom.keywords,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.artFrom, \"keywords\", $$v)\n                            },\n                            expression: \"artFrom.keywords\",\n                          },\n                        },\n                        [\n                          _c(\"el-button\", {\n                            attrs: { slot: \"append\", icon: \"el-icon-search\" },\n                            on: { click: _vm.search },\n                            slot: \"append\",\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"hasPermi\",\n                      rawName: \"v-hasPermi\",\n                      value: [\"admin:system:store:save\"],\n                      expression: \"['admin:system:store:save']\",\n                    },\n                  ],\n                  attrs: { type: \"primary\", size: \"small\" },\n                  on: { click: _vm.add },\n                },\n                [_vm._v(\"添加提货点\")]\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.loading,\n                  expression: \"loading\",\n                },\n              ],\n              attrs: {\n                size: \"small\",\n                data: _vm.tableData,\n                \"header-cell-style\": { fontWeight: \"bold\" },\n              },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { prop: \"id\", label: \"ID\", \"min-width\": \"80\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"image\",\n                  label: \"提货点图片\",\n                  \"min-width\": \"100\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (ref) {\n                      var row = ref.row\n                      var index = ref.index\n                      return [\n                        _c(\n                          \"div\",\n                          { staticClass: \"demo-image__preview\" },\n                          [\n                            _c(\"el-image\", {\n                              staticStyle: { width: \"36px\", height: \"36px\" },\n                              attrs: {\n                                src: row.image,\n                                \"preview-src-list\": [row.image],\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"name\",\n                  label: \"提货点名称\",\n                  \"min-width\": \"150\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"phone\",\n                  label: \"提货点电话\",\n                  \"min-width\": \"100\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"detailedAddress\",\n                  label: \"地址\",\n                  \"min-width\": \"100\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"dayTime\",\n                  label: \"营业时间\",\n                  \"min-width\": \"180\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"isShow\",\n                  label: \"是否显示\",\n                  \"min-width\": \"100\",\n                },\n                scopedSlots: _vm._u(\n                  [\n                    {\n                      key: \"default\",\n                      fn: function (ref) {\n                        var row = ref.row\n                        var index = ref.index\n                        return _vm.checkPermi([\n                          \"admin:system:store:update:status\",\n                        ])\n                          ? [\n                              _c(\"el-switch\", {\n                                attrs: {\n                                  \"active-value\": true,\n                                  \"inactive-value\": false,\n                                  \"active-text\": \"显示\",\n                                  \"inactive-text\": \"隐藏\",\n                                },\n                                on: {\n                                  change: function ($event) {\n                                    return _vm.onchangeIsShow(\n                                      row.id,\n                                      row.isShow\n                                    )\n                                  },\n                                },\n                                model: {\n                                  value: row.isShow,\n                                  callback: function ($$v) {\n                                    _vm.$set(row, \"isShow\", $$v)\n                                  },\n                                  expression: \"row.isShow\",\n                                },\n                              }),\n                            ]\n                          : undefined\n                      },\n                    },\n                  ],\n                  null,\n                  true\n                ),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { fixed: \"right\", label: \"操作\", \"min-width\": \"120\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (ref) {\n                      var row = ref.row\n                      var index = ref.index\n                      return [\n                        _c(\n                          \"el-button\",\n                          {\n                            directives: [\n                              {\n                                name: \"hasPermi\",\n                                rawName: \"v-hasPermi\",\n                                value: [\"admin:system:store:info\"],\n                                expression: \"['admin:system:store:info']\",\n                              },\n                            ],\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.edit(row.id)\n                              },\n                            },\n                          },\n                          [_vm._v(\"编辑\")]\n                        ),\n                        _vm._v(\" \"),\n                        _c(\"el-divider\", { attrs: { direction: \"vertical\" } }),\n                        _vm._v(\" \"),\n                        _vm.artFrom.status === \"2\"\n                          ? _c(\n                              \"el-button\",\n                              {\n                                directives: [\n                                  {\n                                    name: \"hasPermi\",\n                                    rawName: \"v-hasPermi\",\n                                    value: [\"admin:system:store:recovery\"],\n                                    expression:\n                                      \"['admin:system:store:recovery']\",\n                                  },\n                                ],\n                                attrs: { type: \"text\", size: \"small\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.storeRecovery(row.id)\n                                  },\n                                },\n                              },\n                              [_vm._v(\"恢复\")]\n                            )\n                          : _vm._e(),\n                        _vm._v(\" \"),\n                        _vm.artFrom.status === \"2\"\n                          ? _c(\"el-divider\", {\n                              attrs: { direction: \"vertical\" },\n                            })\n                          : _vm._e(),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-button\",\n                          {\n                            directives: [\n                              {\n                                name: \"hasPermi\",\n                                rawName: \"v-hasPermi\",\n                                value: [\n                                  \"admin:system:store:delete\",\n                                  \"admin:system:store:completely:delete\",\n                                ],\n                                expression:\n                                  \"['admin:system:store:delete','admin:system:store:completely:delete']\",\n                              },\n                            ],\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                _vm.artFrom.status === \"2\"\n                                  ? _vm.allDelete(row.id)\n                                  : _vm.storeDelete(row.id)\n                              },\n                            },\n                          },\n                          [_vm._v(\"删除\")]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\"el-pagination\", {\n            staticClass: \"mt20\",\n            attrs: {\n              \"current-page\": _vm.artFrom.page,\n              \"page-sizes\": [20, 40, 60, 100],\n              \"page-size\": _vm.artFrom.limit,\n              layout: \"total, sizes, prev, pager, next, jumper\",\n              total: _vm.total,\n            },\n            on: {\n              \"size-change\": _vm.sizeChange,\n              \"current-change\": _vm.pageChange,\n            },\n          }),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\"system-store\", { ref: \"template\" }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}