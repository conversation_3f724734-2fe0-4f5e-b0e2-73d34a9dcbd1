{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\register.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\register.vue", "mtime": 1754050582487}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { captchaApi, registerApi } from '@/api/sms'\r\nexport default {\r\n  name: 'Register',\r\n  data() {\r\n    const validatePhone = (rule, value, callback) => {\r\n      if (!value) {\r\n        return callback(new Error('请填写手机号'))\r\n      } else if (!/^1[3456789]\\d{9}$/.test(value)) {\r\n        callback(new Error('手机号格式不正确!'))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n    return {\r\n      loading: false,\r\n      passwordType: 'password',\r\n      captchatImg: '',\r\n      cutNUm: '获取验证码',\r\n      canClick: true,\r\n      formInline: {\r\n        account: '',\r\n        code: '',\r\n        domain: '',\r\n        phone: '',\r\n        password: ''\r\n      },\r\n      ruleInline: {\r\n        password: [\r\n          { required: true, message: '请输入短信平台密码/token', trigger: 'blur' }\r\n        ],\r\n        domain: [\r\n          { required: true, message: '请输入网址域名', trigger: 'blur' }\r\n        ],\r\n        phone: [\r\n          { required: true, validator: validatePhone, trigger: 'blur' }\r\n        ],\r\n        code: [\r\n          { required: true, message: '请输入验证码', trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    showPwd() {\r\n      if (this.passwordType === 'password') {\r\n        this.passwordType = ''\r\n      } else {\r\n        this.passwordType = 'password'\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.password.focus()\r\n      })\r\n    },\r\n    // 短信验证码\r\n    cutDown() {\r\n      if (this.formInline.phone) {\r\n        if (!this.canClick) return\r\n        this.canClick = false\r\n        this.cutNUm = 60\r\n        captchaApi({\r\n          phone: this.formInline.phone,\r\n          types: 0\r\n        }).then(async res => {\r\n          this.$message.success('发送成功')\r\n        })\r\n        const time = setInterval(() => {\r\n          this.cutNUm--\r\n          if (this.cutNUm === 0) {\r\n            this.cutNUm = '获取验证码'\r\n            this.canClick = true\r\n            clearInterval(time)\r\n          }\r\n        }, 1000)\r\n      } else {\r\n        this.$message.warning('请填写手机号!')\r\n      }\r\n    },\r\n    // 注册\r\n    handleSubmit(name) {\r\n      this.formInline.account = this.formInline.phone\r\n      this.$refs[name].validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          registerApi(this.formInline).then(async res => {\r\n            this.$message.success('注册成功')\r\n            setTimeout(() => {\r\n              this.changelogo()\r\n            }, 1000)\r\n            this.loading = false;\r\n          }).catch(()=>{\r\n            this.loading = false;\r\n          })\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 立即登录\r\n    changelogo() {\r\n      this.$emit('on-change')\r\n    }\r\n  }\r\n}\r\n", null]}