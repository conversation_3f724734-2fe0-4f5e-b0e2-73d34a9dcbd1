{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\FormGenerator\\components\\render\\slots\\el-input.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\FormGenerator\\components\\render\\slots\\el-input.js", "mtime": 1754050582242}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754554385233}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _default = exports.default = {\n  prepend: function prepend(h, conf, key) {\n    return h(\"template\", {\n      \"slot\": 'prepend'\n    }, [conf.__slot__[key]]);\n  },\n  append: function append(h, conf, key) {\n    return h(\"template\", {\n      \"slot\": 'append'\n    }, [conf.__slot__[key]]);\n  }\n};", null]}