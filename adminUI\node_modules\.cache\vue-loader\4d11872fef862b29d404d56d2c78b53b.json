{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\notification\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\notification\\index.vue", "mtime": 1754050582514}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport {notificationListApi,notificationRoutine,notificationWechat,notificationSms,notificationDetail,notificationUpdate} from '@/api/systemFormConfig'\r\nimport {wechat<PERSON><PERSON><PERSON>pi,routine<PERSON><PERSON><PERSON>pi} from '@/api/wxApi'\r\nimport {Debounce} from '@/utils/validate'\r\nexport default {\r\n  data() {\r\n    return {\r\n      modalTitle: \"\",\r\n      notificationModal: false,\r\n      headerList: [\r\n        { label: \"通知会员\", value: \"1\" },\r\n        { label: \"通知平台\", value: \"2\" },\r\n      ],\r\n      id:0,\r\n      levelLists: [],\r\n      currentTab: \"1\",\r\n      loading: false,\r\n      formData: {},\r\n      industry: null,\r\n      loadingList:false,\r\n      centerDialogVisible:false,\r\n      infoList:[],\r\n      infoList1:[\r\n        { label: \"短信\", value: \"sms\" },\r\n      ],\r\n      form:{\r\n        content:'',\r\n        name:'',\r\n        id:'',\r\n        status:null,\r\n        tempId:'',\r\n        tempKey:'',\r\n        title:'',\r\n      },\r\n      detailType:'',\r\n      infoTab:''\r\n    };\r\n  },\r\n  created() {\r\n    this.getNotificationList(Number(this.currentTab));\r\n  },\r\n  methods: {\r\n    changeTab(data) {\r\n      this.getNotificationList(data.name);\r\n    },\r\n    //获取消息列表\r\n    getNotificationList(id){\r\n      this.loadingList = true;\r\n      notificationListApi({sendType:id}).then(res=>{\r\n        this.loadingList = false;\r\n        this.levelLists = res;\r\n      }).catch(res=>{\r\n        this.loadingList = false;\r\n      })\r\n    },\r\n    //公众号消息开关\r\n    changeWechat(row){\r\n      notificationWechat(row.id).then(res=>{\r\n        this.$modal.msgSuccess(\"修改成功\");\r\n      })\r\n    },\r\n    //小程序消息开关\r\n    changeRoutine(row){\r\n      notificationRoutine(row.id).then(res=>{\r\n        this.$modal.msgSuccess(\"修改成功\");\r\n      })\r\n    },\r\n    //短信消息开关\r\n    changeSms(row){\r\n      notificationSms(row.id).then(res=>{\r\n        this.$modal.msgSuccess(\"修改成功\");\r\n      })\r\n    },\r\n   //详情tab切换\r\n    changeInfo(data){\r\n      this.getNotificationDetail(data);\r\n    },\r\n    //详情数据\r\n    getNotificationDetail(param){\r\n      let data = {\r\n        id:this.id,\r\n        type:param.name\r\n      };\r\n      this.$set(this,'detailType',data.type);\r\n      notificationDetail(data).then(res=>{\r\n        this.form = res;\r\n        this.$set(this.form,'status',res.status.toString());\r\n      })\r\n    },\r\n    // 设置\r\n    setting(row) {\r\n      this.infoList = [];\r\n      this.id = row.id;\r\n      this.centerDialogVisible = true;\r\n      if(row.isWechat !== 0){\r\n        this.infoList.push({ label: \"公众号模板消息\", value: \"wechat\" });\r\n      }\r\n       if(row.isRoutine !== 0){\r\n        this.infoList.push({ label: \"小程序订阅消息\", value: \"routine\"});\r\n      }\r\n      if(row.isSms !== 0){\r\n        this.infoList.push({ label: \"短信\", value: \"sms\" });\r\n      }\r\n      this.infoTab = this.infoList[0].value;\r\n      this.getNotificationDetail({name:this.infoTab});\r\n    },\r\n    //修改通知\r\n    submit:Debounce(function(){\r\n      let data = {\r\n        id:this.id,\r\n        status:Number(this.form.status),\r\n        tempId:this.form.tempId,\r\n        type:this.detailType\r\n      };\r\n      notificationUpdate(data).then(res=>{\r\n        this.$modal.msgSuccess(\"修改成功\");\r\n        this.centerDialogVisible = false;\r\n        this.getNotificationList();\r\n      })\r\n    }),\r\n    syncWechat(){\r\n      wechatAsyncApi().then(res=>{\r\n        this.$message.success('同步成功');\r\n      })\r\n    },\r\n    syncRoutine(){\r\n      routineAsyncApi().then(res=>{\r\n        this.$message.success('同步成功');\r\n      })\r\n    }\r\n  },\r\n};\r\n", null]}