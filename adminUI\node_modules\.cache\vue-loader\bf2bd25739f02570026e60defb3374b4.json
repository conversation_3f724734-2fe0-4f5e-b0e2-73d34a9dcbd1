{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsTemplate\\index.vue?vue&type=template&id=271d5054&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsTemplate\\index.vue", "mtime": 1754050582490}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<!--v-if=\"isLogin\"-->\n<div class=\"divBox\" v-if=\"isLogin\">\n  <el-card v-loading=\"fullscreenLoading\" class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <div class=\"container\">\n        <router-link :to=\"{path:'/operation/onePass'}\">\n          <el-button class=\"mb35\" size=\"mini\" icon=\"el-icon-arrow-left\">返回</el-button>\n        </router-link>\n      </div>\n      <el-button size=\"mini\" type=\"primary\" @click=\"add\">添加短信模板</el-button>\n    </div>\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      highlight-current-row\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        min-width=\"50\"\n      />\n      <el-table-column\n        prop=\"temp_id\"\n        label=\"模板ID\"\n        min-width=\"80\"\n      />\n      <el-table-column\n        prop=\"title\"\n        label=\"模板名称\"\n        min-width=\"120\"\n      />\n      <el-table-column\n        prop=\"content\"\n        label=\"模板内容\"\n        min-width=\"500\"\n      />\n      <el-table-column\n        label=\"模板类型\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"{row}\">\n          <span>{{ row.temp_type | typesFilter }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"模板状态\">\n        <template slot-scope=\"{row}\">\n          <span>{{ row.status | statusFilter }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n\n  <!--编辑-->\n  <el-dialog\n    title=\"添加模板\"\n    :visible.sync=\"dialogVisible\"\n    width=\"500px\"\n    :before-close=\"handleClose\">\n    <zb-parser\n      v-if=\"dialogVisible\"\n      :form-id=\"110\"\n      :is-create=\"isCreate\"\n      :edit-data=\"editData\"\n      @submit=\"handlerSubmit\"\n      @resetForm=\"resetForm\"\n    />\n  </el-dialog>\n</div>\n", null]}