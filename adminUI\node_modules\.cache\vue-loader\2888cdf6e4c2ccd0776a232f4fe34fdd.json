{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\groupBuy\\groupGoods\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\groupBuy\\groupGoods\\index.vue", "mtime": 1754050582446}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { combinationListApi, combinationDeleteApi, combinationStatusApi, exportcombiantionApi } from '@/api/marketing'\nimport { formatDates } from '@/utils/index';\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nexport default {\n  name: \"index\",\n  filters: {\n    formatDate (time) {\n      if (time !== 0) {\n        const date = new Date(time);\n        return formatDates(date, 'yyyy-MM-dd hh:mm');\n      }\n    }\n  },\n  data() {\n    return {\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        keywords: '',\n        isShow: ''\n      },\n      listLoading: true,\n      tableData: {\n        data: [],\n        total: 0\n      },\n    }\n  },\n  mounted() {\n    this.getList()\n  },\n  methods: {\n    checkPermi,\n    //导出\n    exportList(){\n      exportcombiantionApi({keywords: this.tableFrom.keywords, isShow:this.tableFrom.isShow}).then((res) => {\n        window.open(res.fileName)\n      })\n    },\n    // 删除\n    handleDelete(id, idx) {\n      this.$modalSure().then(() => {\n        combinationDeleteApi({id: id}).then(() => {\n          this.$message.success('删除成功')\n          this.tableData.data.splice(idx, 1)\n        })\n      })\n    },\n    onchangeIsShow(row) {\n      combinationStatusApi({id:row.id,isShow: row.isShow})\n        .then(async () => {\n          this.$message.success('修改成功');\n          this.getList()\n        }).catch(()=>{\n        row.isShow = !row.isShow\n      })\n    },\n    // 列表\n    getList(num) {\n      this.listLoading = true\n      this.tableFrom.page = num ? num : this.tableFrom.page;\n      combinationListApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.listLoading = false\n      }).catch((res) => {\n        this.listLoading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      this.getList()\n    }\n  }\n}\n", null]}