{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\edit.vue?vue&type=template&id=2028256b&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\edit.vue", "mtime": 1754050582501}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    [\n      _c(\n        \"el-form\",\n        {\n          ref: \"pram\",\n          attrs: { model: _vm.pram, \"label-width\": \"100px\" },\n          nativeOn: {\n            submit: function ($event) {\n              $event.preventDefault()\n            },\n          },\n        },\n        [\n          _c(\n            \"el-form-item\",\n            {\n              attrs: {\n                label: _vm.$t(\"admin.system.role.roleForm.roleNameLabel\"),\n                prop: \"roleName\",\n                rules: [\n                  {\n                    required: true,\n                    message:\n                      _vm.$t(\"common.enter\") +\n                      _vm.$t(\"admin.system.role.roleForm.roleNameLabel\"),\n                    trigger: [\"blur\", \"change\"],\n                  },\n                ],\n              },\n            },\n            [\n              _c(\"el-input\", {\n                attrs: {\n                  placeholder: _vm.$t(\n                    \"admin.system.role.roleForm.roleNamePlaceholder\"\n                  ),\n                },\n                model: {\n                  value: _vm.pram.roleName,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.pram, \"roleName\", $$v)\n                  },\n                  expression: \"pram.roleName\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-form-item\",\n            {\n              attrs: {\n                label: _vm.$t(\"admin.system.role.roleForm.statusLabel\"),\n              },\n            },\n            [\n              _c(\"el-switch\", {\n                attrs: { \"active-value\": true, \"inactive-value\": false },\n                model: {\n                  value: _vm.pram.status,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.pram, \"status\", $$v)\n                  },\n                  expression: \"pram.status\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-form-item\",\n            {\n              attrs: {\n                label: _vm.$t(\"admin.system.role.roleForm.menuPermissions\"),\n              },\n            },\n            [\n              _c(\n                \"el-checkbox\",\n                {\n                  on: {\n                    change: function ($event) {\n                      return _vm.handleCheckedTreeExpand($event, \"menu\")\n                    },\n                  },\n                  model: {\n                    value: _vm.menuExpand,\n                    callback: function ($$v) {\n                      _vm.menuExpand = $$v\n                    },\n                    expression: \"menuExpand\",\n                  },\n                },\n                [\n                  _vm._v(\n                    _vm._s(_vm.$t(\"admin.system.role.roleForm.expandCollapse\"))\n                  ),\n                ]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-checkbox\",\n                {\n                  on: {\n                    change: function ($event) {\n                      return _vm.handleCheckedTreeConnect($event, \"menu\")\n                    },\n                  },\n                  model: {\n                    value: _vm.menuCheckStrictly,\n                    callback: function ($$v) {\n                      _vm.menuCheckStrictly = $$v\n                    },\n                    expression: \"menuCheckStrictly\",\n                  },\n                },\n                [\n                  _vm._v(\n                    _vm._s(_vm.$t(\"admin.system.role.roleForm.parentChildLink\"))\n                  ),\n                ]\n              ),\n              _vm._v(\" \"),\n              _c(\"el-tree\", {\n                ref: \"menu\",\n                staticClass: \"tree-border\",\n                attrs: {\n                  data: _vm.menuOptions,\n                  \"show-checkbox\": \"\",\n                  \"node-key\": \"id\",\n                  \"check-strictly\": !_vm.menuCheckStrictly,\n                  \"empty-text\": _vm.$t(\"common.fetchDataFailed\"),\n                  props: _vm.defaultProps,\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-form-item\",\n            [\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"hasPermi\",\n                      rawName: \"v-hasPermi\",\n                      value: [\n                        \"admin:system:role:update\",\n                        \"admin:system:role:save\",\n                      ],\n                      expression:\n                        \"['admin:system:role:update', 'admin:system:role:save']\",\n                    },\n                  ],\n                  attrs: { type: \"primary\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.handlerSubmit(\"pram\")\n                    },\n                  },\n                },\n                [\n                  _vm._v(\n                    _vm._s(\n                      _vm.isCreate === 0\n                        ? _vm.$t(\"admin.system.role.roleForm.confirm\")\n                        : _vm.$t(\"admin.system.role.roleForm.update\")\n                    )\n                  ),\n                ]\n              ),\n              _vm._v(\" \"),\n              _c(\"el-button\", { on: { click: _vm.close } }, [\n                _vm._v(_vm._s(_vm.$t(\"admin.system.role.roleForm.cancel\"))),\n              ]),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}