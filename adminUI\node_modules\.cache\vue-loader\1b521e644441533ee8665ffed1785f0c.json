{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Navbar.vue?vue&type=template&id=d16d6306&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Navbar.vue", "mtime": 1754553124447}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"navbar\" },\n    [\n      _c(\"hamburger\", {\n        staticClass: \"hamburger-container\",\n        attrs: { id: \"hamburger-container\", \"is-active\": _vm.sidebar.opened },\n        on: { toggleClick: _vm.toggleSideBar },\n      }),\n      _vm._v(\" \"),\n      _c(\"breadcrumb\", {\n        staticClass: \"breadcrumb-container\",\n        attrs: { id: \"breadcrumb-container\" },\n      }),\n      _vm._v(\" \"),\n      _c(\n        \"div\",\n        { staticClass: \"right-menu\" },\n        [\n          _vm.device !== \"mobile\"\n            ? [\n                _c(\"screenfull\", {\n                  staticClass: \"right-menu-item hover-effect\",\n                  attrs: { id: \"screenfull\" },\n                }),\n              ]\n            : _vm._e(),\n          _vm._v(\" \"),\n          _c(\n            \"el-dropdown\",\n            {\n              staticClass: \"avatar-container right-menu-item hover-effect\",\n              attrs: { trigger: \"click\" },\n            },\n            [\n              _c(\"div\", { staticClass: \"avatar-wrapper\" }, [\n                _vm._v(\n                  \"\\n          语言：\" + _vm._s(_vm.langLabel(_vm.nowLanguage))\n                ),\n                _c(\"i\", { staticClass: \"el-icon-arrow-down el-icon--right\" }),\n              ]),\n              _vm._v(\" \"),\n              _c(\n                \"el-dropdown-menu\",\n                { attrs: { slot: \"dropdown\" }, slot: \"dropdown\" },\n                [\n                  _vm._l(_vm.availableLanguages, function (lang) {\n                    return [\n                      lang !== _vm.nowLanguage\n                        ? _c(\n                            \"el-dropdown-item\",\n                            {\n                              key: lang,\n                              nativeOn: {\n                                click: function ($event) {\n                                  return _vm.toggleLang(lang)\n                                },\n                              },\n                            },\n                            [\n                              _vm._v(\n                                \"\\n              \" +\n                                  _vm._s(_vm.langLabel(lang)) +\n                                  \"\\n            \"\n                              ),\n                            ]\n                          )\n                        : _vm._e(),\n                    ]\n                  }),\n                ],\n                2\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-dropdown\",\n            {\n              staticClass: \"avatar-container right-menu-item hover-effect\",\n              attrs: { trigger: \"click\" },\n            },\n            [\n              _c(\"div\", { staticClass: \"avatar-wrapper\" }, [\n                _vm._v(\"\\n          \" + _vm._s(_vm.JavaInfo.realName)),\n                _c(\"i\", { staticClass: \"el-icon-arrow-down el-icon--right\" }),\n              ]),\n              _vm._v(\" \"),\n              _c(\n                \"el-dropdown-menu\",\n                { attrs: { slot: \"dropdown\" }, slot: \"dropdown\" },\n                [\n                  _c(\n                    \"router-link\",\n                    { attrs: { to: \"/\" } },\n                    [\n                      _c(\"el-dropdown-item\", [\n                        _vm._v(_vm._s(_vm.$t(\"navbar.home\"))),\n                      ]),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  !_vm.isPhone\n                    ? _c(\n                        \"router-link\",\n                        { attrs: { to: { path: \"/maintain/user\" } } },\n                        [\n                          _c(\"el-dropdown-item\", [\n                            _vm._v(_vm._s(_vm.$t(\"navbar.profile\"))),\n                          ]),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-dropdown-item\",\n                    {\n                      nativeOn: {\n                        click: function ($event) {\n                          return _vm.logout($event)\n                        },\n                      },\n                    },\n                    [_c(\"span\", [_vm._v(_vm._s(_vm.$t(\"navbar.logout\")))])]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        2\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}