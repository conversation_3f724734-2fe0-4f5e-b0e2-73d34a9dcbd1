{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\setting\\index.vue?vue&type=template&id=26c5d13b&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\setting\\index.vue", "mtime": 1754050582515}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _vm.checkPermi([\"admin:system:config:info\"])\n            ? _c(\n                \"el-tabs\",\n                {\n                  directives: [\n                    {\n                      name: \"loading\",\n                      rawName: \"v-loading\",\n                      value: _vm.loading,\n                      expression: \"loading\",\n                    },\n                  ],\n                  on: { \"tab-click\": _vm.handleTabClick },\n                  model: {\n                    value: _vm.activeNamel1,\n                    callback: function ($$v) {\n                      _vm.activeNamel1 = $$v\n                    },\n                    expression: \"activeNamel1\",\n                  },\n                },\n                _vm._l(_vm.treeList, function (tab, index) {\n                  return _c(\n                    \"el-tab-pane\",\n                    {\n                      key: index,\n                      attrs: { label: tab.name, name: tab.id.toString() },\n                    },\n                    [\n                      [\n                        tab.child.length > 0\n                          ? _c(\n                              \"el-tabs\",\n                              {\n                                attrs: { type: \"border-card\" },\n                                on: { \"tab-click\": _vm.handleItemTabClick },\n                                model: {\n                                  value: _vm.activeNamel2,\n                                  callback: function ($$v) {\n                                    _vm.activeNamel2 = $$v\n                                  },\n                                  expression: \"activeNamel2\",\n                                },\n                              },\n                              _vm._l(tab.child, function (tabItem, itemIndex) {\n                                return _c(\n                                  \"el-tab-pane\",\n                                  {\n                                    key: itemIndex,\n                                    attrs: {\n                                      label: tabItem.name,\n                                      name: tabItem.extra,\n                                    },\n                                  },\n                                  [\n                                    _vm.formConfChild.render\n                                      ? _c(\"parser\", {\n                                          attrs: {\n                                            \"is-edit\": _vm.formConfChild.isEdit,\n                                            \"form-conf\":\n                                              _vm.formConfChild.content,\n                                            \"form-edit-data\":\n                                              _vm.currentEditData,\n                                          },\n                                          on: { submit: _vm.handlerSubmit },\n                                        })\n                                      : _vm._e(),\n                                  ],\n                                  1\n                                )\n                              }),\n                              1\n                            )\n                          : _c(\n                              \"span\",\n                              [\n                                _vm.formConf.render\n                                  ? _c(\"parser\", {\n                                      attrs: {\n                                        \"is-edit\": _vm.formConf.isEdit,\n                                        \"form-conf\": _vm.formConf.content,\n                                        \"form-edit-data\": _vm.currentEditData,\n                                      },\n                                      on: { submit: _vm.handlerSubmit },\n                                    })\n                                  : _vm._e(),\n                              ],\n                              1\n                            ),\n                      ],\n                    ],\n                    2\n                  )\n                }),\n                1\n              )\n            : _vm._e(),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}