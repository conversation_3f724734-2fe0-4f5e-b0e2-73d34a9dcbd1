{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\edit.vue?vue&type=template&id=2028256b&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\edit.vue", "mtime": 1754050582501}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div>\n  <el-form\n    ref=\"pram\"\n    :model=\"pram\"\n    label-width=\"100px\"\n    @submit.native.prevent\n  >\n    <el-form-item\n      :label=\"$t('admin.system.role.roleForm.roleNameLabel')\"\n      prop=\"roleName\"\n      :rules=\"[\n        {\n          required: true,\n          message:\n            $t('common.enter') +\n            $t('admin.system.role.roleForm.roleNameLabel'),\n          trigger: ['blur', 'change']\n        }\n      ]\"\n    >\n      <el-input\n        v-model=\"pram.roleName\"\n        :placeholder=\"$t('admin.system.role.roleForm.roleNamePlaceholder')\"\n      />\n    </el-form-item>\n    <el-form-item :label=\"$t('admin.system.role.roleForm.statusLabel')\">\n      <el-switch\n        v-model=\"pram.status\"\n        :active-value=\"true\"\n        :inactive-value=\"false\"\n      />\n    </el-form-item>\n    <el-form-item :label=\"$t('admin.system.role.roleForm.menuPermissions')\">\n      <el-checkbox\n        v-model=\"menuExpand\"\n        @change=\"handleCheckedTreeExpand($event, 'menu')\"\n        >{{ $t(\"admin.system.role.roleForm.expandCollapse\") }}</el-checkbox\n      >\n      <!-- <el-checkbox v-model=\"menuNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'menu')\">{{ $t('admin.system.role.roleForm.selectAll') }}</el-checkbox> -->\n      <el-checkbox\n        v-model=\"menuCheckStrictly\"\n        @change=\"handleCheckedTreeConnect($event, 'menu')\"\n        >{{ $t(\"admin.system.role.roleForm.parentChildLink\") }}</el-checkbox\n      >\n      <el-tree\n        class=\"tree-border\"\n        :data=\"menuOptions\"\n        show-checkbox\n        ref=\"menu\"\n        node-key=\"id\"\n        :check-strictly=\"!menuCheckStrictly\"\n        :empty-text=\"$t('common.fetchDataFailed')\"\n        :props=\"defaultProps\"\n      ></el-tree>\n    </el-form-item>\n    <el-form-item>\n      <el-button\n        type=\"primary\"\n        @click=\"handlerSubmit('pram')\"\n        v-hasPermi=\"['admin:system:role:update', 'admin:system:role:save']\"\n        >{{\n          isCreate === 0\n            ? $t(\"admin.system.role.roleForm.confirm\")\n            : $t(\"admin.system.role.roleForm.update\")\n        }}</el-button\n      >\n      <el-button @click=\"close\">{{\n        $t(\"admin.system.role.roleForm.cancel\")\n      }}</el-button>\n    </el-form-item>\n  </el-form>\n</div>\n", null]}