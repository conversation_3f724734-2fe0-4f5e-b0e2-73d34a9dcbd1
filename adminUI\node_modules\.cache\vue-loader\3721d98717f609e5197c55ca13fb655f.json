{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\groupBuy\\groupList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\groupBuy\\groupList\\index.vue", "mtime": 1754050582448}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { combineList<PERSON>pi, combineStatistics<PERSON><PERSON>,combineOrderPinkApi } from '@/api/marketing'\nimport cardsData from '@/components/cards/index'\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nexport default {\n  name: \"groupList\",\n  components: {\n    cardsData\n  },\n  data() {\n    return {\n      listLoadingPink: false,\n      dialogVisible: false,\n      tableDataPink: {\n        data: []\n      },\n      tableData: {\n        data: [],\n        total: 0\n      },\n      listLoading: false,\n      tableFrom: {\n        dateLimit: '',\n        status: '',\n        page: 1,\n        limit: 20\n      },\n      fromList: this.$constants.fromList,\n      timeVal: [],\n      cardLists: []\n    }\n  },\n  mounted() {\n    this.getStatistics();\n    this.getList();\n  },\n  methods:{\n    checkPermi,\n    handleClose(){\n      this.dialogVisible = false\n    },\n    handleLook(id){\n      this.dialogVisible = true;\n      this.getPink(id);\n    },\n    getPink(id) {\n      this.listLoadingPink = true;\n      combineOrderPinkApi(id).then(res => {\n        this.tableDataPink.data = res\n        this.listLoadingPink = false\n      }).catch(() => {\n        this.listLoadingPink = false\n      })\n    },\n    selectChange(tab) {\n      this.tableFrom.dateLimit = tab\n      this.tableFrom.page = 1\n      this.timeVal = []\n      this.getList()\n    },\n    // 具体日期\n    onchangeTime(e) {\n      this.timeVal = e\n      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : ''\n      this.tableFrom.page = 1\n      this.getList()\n    },\n    // 列表\n    getList(num) {\n      this.listLoading = true;\n      this.tableFrom.page = num ? num : this.tableFrom.page;\n      combineListApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.listLoading = false\n      }).catch(() => {\n        this.listLoading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      this.getList()\n    },\n    // 统计\n    getStatistics() {\n      combineStatisticsApi().then(res => {\n        this.cardLists = [\n          { name: '参与人数(人)', count: res.countPeople,color:'#1890FF',class:'one',icon:'iconleijiyonghushu' },\n          { name: '成团数量(个)', count: res.countTeam,color:'#A277FF',class:'two',icon:'iconxinzengyonghu' }\n        ]\n      }).catch(() => {\n        this.listLoading = false\n      })\n    },\n  }\n}\n", null]}