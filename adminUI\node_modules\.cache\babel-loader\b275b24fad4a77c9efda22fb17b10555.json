{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\element-ui\\lib\\locale\\lang\\id.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\element-ui\\lib\\locale\\lang\\id.js", "mtime": 1754554392187}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}], "contextDependencies": [], "result": ["'use strict';\n\nexports.__esModule = true;\nexports.default = {\n  el: {\n    colorpicker: {\n      confirm: '<PERSON>lih',\n      clear: 'Kosongkan'\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>ng',\n      today: 'Hari ini',\n      cancel: '<PERSON><PERSON>',\n      clear: 'Kosongkan',\n      confirm: 'Ya',\n      selectDate: '<PERSON>lih tanggal',\n      selectTime: 'Pilih waktu',\n      startDate: 'Tanggal Mulai',\n      startTime: 'Waktu <PERSON>',\n      endDate: 'Tanggal Seles<PERSON>',\n      endTime: '<PERSON><PERSON><PERSON>',\n      prevYear: 'Previous Year',\n      // to be translated\n      nextYear: 'Next Year',\n      // to be translated\n      prevMonth: 'Previous Month',\n      // to be translated\n      nextMonth: 'Next Month',\n      // to be translated\n      year: '<PERSON>hun',\n      month1: 'Januari',\n      month2: 'Februari',\n      month3: 'Maret',\n      month4: 'April',\n      month5: 'Mei',\n      month6: 'Juni',\n      month7: 'Juli',\n      month8: 'Agus<PERSON>',\n      month9: 'September',\n      month10: 'Oktober',\n      month11: 'November',\n      month12: 'Desember',\n      // week: 'minggu',\n      weeks: {\n        sun: 'Min',\n        mon: 'Sen',\n        tue: 'Sel',\n        wed: 'Rab',\n        thu: 'Kam',\n        fri: 'Jum',\n        sat: 'Sab'\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Mei',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Agu',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Des'\n      }\n    },\n    select: {\n      loading: 'Memuat',\n      noMatch: 'Tidak ada data yg cocok',\n      noData: 'Tidak ada data',\n      placeholder: 'Pilih'\n    },\n    cascader: {\n      noMatch: 'Tidak ada data yg cocok',\n      loading: 'Memuat',\n      placeholder: 'Pilih',\n      noData: 'Tidak ada data'\n    },\n    pagination: {\n      goto: 'Pergi ke',\n      pagesize: '/laman',\n      total: 'Total {total}',\n      pageClassifier: ''\n    },\n    messagebox: {\n      title: 'Pesan',\n      confirm: 'Ya',\n      cancel: 'Batal',\n      error: 'Masukan ilegal'\n    },\n    upload: {\n      deleteTip: 'press delete to remove',\n      // to be translated\n      delete: 'Hapus',\n      preview: 'Pratinjau',\n      continue: 'Lanjutkan'\n    },\n    table: {\n      emptyText: 'Tidak ada data',\n      confirmFilter: 'Konfirmasi',\n      resetFilter: 'Atur ulang',\n      clearFilter: 'Semua',\n      sumText: 'Jml'\n    },\n    tree: {\n      emptyText: 'Tidak ada data'\n    },\n    transfer: {\n      noMatch: 'Tidak ada data yg cocok',\n      noData: 'Tidak ada data',\n      titles: ['Senarai 1', 'Senarai 2'],\n      filterPlaceholder: 'Masukan kata kunci',\n      noCheckedFormat: '{total} butir',\n      hasCheckedFormat: '{checked}/{total} terpilih'\n    },\n    image: {\n      error: 'FAILED' // to be translated\n    },\n    pageHeader: {\n      title: 'Back' // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes',\n      // to be translated\n      cancelButtonText: 'No' // to be translated\n    }\n  }\n};", null]}