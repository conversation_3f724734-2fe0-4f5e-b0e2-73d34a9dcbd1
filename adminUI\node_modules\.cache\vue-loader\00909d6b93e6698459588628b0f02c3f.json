{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=template&id=2d2bbdc2", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1754050582340}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return !_vm.item.hidden\n    ? _c(\n        \"div\",\n        [\n          _vm.hasOneShowingChild(_vm.item.child, _vm.item) &&\n          (!_vm.onlyOneChild.child || _vm.onlyOneChild.noShowingChildren) &&\n          !_vm.item.alwaysShow\n            ? [\n                _vm.onlyOneChild\n                  ? _c(\n                      \"app-link\",\n                      { attrs: { to: _vm.resolvePath(_vm.onlyOneChild.url) } },\n                      [\n                        _c(\n                          \"el-menu-item\",\n                          {\n                            class: { \"submenu-title-noDropdown\": !_vm.isNest },\n                            attrs: {\n                              index: _vm.resolvePath(_vm.onlyOneChild.url),\n                            },\n                          },\n                          [\n                            _c(\"item\", {\n                              attrs: {\n                                icon:\n                                  _vm.onlyOneChild.extra ||\n                                  (_vm.item.meta && _vm.item.extra),\n                                title: _vm.$t(\n                                  \"dashboard.\" + _vm.onlyOneChild.name\n                                ),\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                      ],\n                      1\n                    )\n                  : _vm._e(),\n              ]\n            : _c(\n                \"el-submenu\",\n                {\n                  ref: \"subMenu\",\n                  attrs: {\n                    index: _vm.resolvePath(_vm.item.url),\n                    \"popper-append-to-body\": \"\",\n                  },\n                },\n                [\n                  _c(\n                    \"template\",\n                    { slot: \"title\" },\n                    [\n                      _vm.item\n                        ? _c(\"item\", {\n                            attrs: {\n                              icon: _vm.item && _vm.item.extra,\n                              title: _vm.$t(\"dashboard.\" + _vm.item.name),\n                            },\n                          })\n                        : _vm._e(),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _vm._l(_vm.item.child, function (childs) {\n                    return _c(\"sidebar-item\", {\n                      key: childs.url,\n                      staticClass: \"nest-menu\",\n                      attrs: {\n                        \"is-nest\": true,\n                        item: childs,\n                        \"base-path\": _vm.resolvePath(childs.url),\n                      },\n                    })\n                  }),\n                ],\n                2\n              ),\n        ],\n        2\n      )\n    : _vm._e()\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}