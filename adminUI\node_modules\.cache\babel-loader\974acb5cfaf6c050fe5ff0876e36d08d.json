{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\deliveryAddress\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\deliveryAddress\\index.vue", "mtime": 1754050582508}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _addPoint = _interopRequireDefault(require(\"./addPoint\"));\nvar _storePoint = require(\"@/api/storePoint\");\nvar _permission = require(\"@/utils/permission\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// 权限判断函数\nvar _default = exports.default = {\n  name: 'Point',\n  components: {\n    systemStore: _addPoint.default\n  },\n  data: function data() {\n    return {\n      artFrom: {\n        page: 1,\n        limit: 20,\n        status: '1',\n        keywords: ''\n      },\n      loading: false,\n      tableData: [],\n      total: 0,\n      headerCount: {}\n    };\n  },\n  created: function created() {\n    this.storeGetCount();\n    this.tableList();\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    //头部数量显示；\n    storeGetCount: function storeGetCount() {\n      var that = this;\n      (0, _storePoint.storeGetCountApi)().then(function (res) {\n        that.headerCount = res;\n      });\n    },\n    //表格列表\n    tableList: function tableList() {\n      var that = this;\n      that.loading = true;\n      (0, _storePoint.storeListApi)(that.artFrom).then(function (res) {\n        that.loading = false;\n        that.tableData = res.list;\n        that.total = res.total;\n      });\n    },\n    //切换页数\n    pageChange: function pageChange(index) {\n      this.artFrom.page = index;\n      this.tableList();\n    },\n    //切换显示条数\n    sizeChange: function sizeChange(index) {\n      this.artFrom.limit = index;\n      this.tableList();\n    },\n    //头部切换\n    onClickTab: function onClickTab() {\n      this.artFrom.keywords = '';\n      this.tableList();\n    },\n    //搜索\n    search: function search() {\n      this.artFrom.page = 1;\n      this.tableList();\n    },\n    //是否显示\n    onchangeIsShow: function onchangeIsShow(id, isShow) {\n      var that = this;\n      (0, _storePoint.storeUpdateStatusApi)({\n        id: id,\n        status: isShow\n      }).then(function () {\n        that.$message.success(\"操作成功\");\n        that.tableList();\n        that.storeGetCount();\n      }).catch(function () {\n        row.isShow = !row.isShow;\n      });\n    },\n    // 恢复\n    storeRecovery: function storeRecovery(id) {\n      var _this = this;\n      this.$modalSure('恢复提货吗').then(function () {\n        (0, _storePoint.storeRecoveryApi)({\n          id: id\n        }).then(function () {\n          _this.$message.success('恢复成功');\n          _this.storeGetCount();\n          _this.tableList();\n        });\n      });\n    },\n    //刪除\n    storeDelete: function storeDelete(id) {\n      var that = this;\n      that.$modalSure('删除提货点吗？').then(function () {\n        (0, _storePoint.storeDeleteApi)({\n          id: id\n        }).then(function () {\n          that.$message.success('删除成功');\n          that.storeGetCount();\n          that.tableList();\n        });\n      });\n    },\n    allDelete: function allDelete(id) {\n      var _this2 = this;\n      this.$modalSure().then(function () {\n        (0, _storePoint.allDeleteApi)({\n          id: id\n        }).then(function () {\n          _this2.$message.success('删除成功');\n          _this2.storeGetCount();\n          _this2.tableList();\n        });\n      });\n    },\n    //添加\n    add: function add() {\n      this.$refs.template.dialogFormVisible = true;\n    },\n    //编辑\n    edit: function edit(id) {\n      this.$refs.template.dialogFormVisible = true;\n      this.$refs.template.getInfo(id);\n    }\n  }\n};", null]}