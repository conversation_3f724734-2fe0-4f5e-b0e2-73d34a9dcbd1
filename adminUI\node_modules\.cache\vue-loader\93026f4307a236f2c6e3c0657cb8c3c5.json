{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\HeaderSearch\\index.vue?vue&type=template&id=032bd1f0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\HeaderSearch\\index.vue", "mtime": 1754050582266}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"header-search\", class: { show: _vm.show } },\n    [\n      _c(\"i\", {\n        staticClass: \"iconfont iconios-search\",\n        staticStyle: { \"font-size\": \"20px\" },\n        on: {\n          click: function ($event) {\n            $event.stopPropagation()\n            return _vm.click($event)\n          },\n        },\n      }),\n      _vm._v(\" \"),\n      _c(\n        \"el-select\",\n        {\n          ref: \"headerSearchSelect\",\n          staticClass: \"header-search-select\",\n          attrs: {\n            \"remote-method\": _vm.querySearch,\n            filterable: \"\",\n            \"default-first-option\": \"\",\n            remote: \"\",\n            placeholder: \"搜索菜单\",\n          },\n          on: { change: _vm.change },\n          model: {\n            value: _vm.search,\n            callback: function ($$v) {\n              _vm.search = $$v\n            },\n            expression: \"search\",\n          },\n        },\n        _vm._l(_vm.options, function (item) {\n          return _c(\"el-option\", {\n            key: item.url,\n            attrs: { value: item, label: item.name.join(\" > \") },\n          })\n        }),\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}