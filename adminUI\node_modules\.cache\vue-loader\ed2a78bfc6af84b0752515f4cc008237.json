{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\collateOrder\\index.vue?vue&type=template&id=48ed33d1&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\collateOrder\\index.vue", "mtime": 1754050582505}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div class=\"clearfix\">\n      <div class=\"container\">\n        <el-form size=\"small\" label-width=\"100px\" :inline=\"true\" >\n          <el-form-item label=\"时间选择：\" class=\"width100\">\n            <el-radio-group v-model=\"tableFrom.dateLimit\" type=\"button\" class=\"mr20\" size=\"small\" @change=\"selectChange(tableFrom.dateLimit)\">\n              <el-radio-button v-for=\"(item,i) in fromList.fromTxt\" :key=\"i\" :label=\"item.val\">{{ item.text }}</el-radio-button>\n            </el-radio-group>\n            <el-date-picker v-model=\"timeVal\" value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\" size=\"small\" type=\"daterange\" placement=\"bottom-end\" placeholder=\"自定义时间\" style=\"width: 250px;\" @change=\"onchangeTime\" />\n          </el-form-item>\n          <el-form-item label=\"选择门店：\">\n            <el-select v-model=\"tableFrom.storeId\" filterable placeholder=\"请选择\" class=\"selWidth\" clearable @change=\"getList(1)\">\n              <el-option\n                v-for=\"item in storeSelectList\"\n                :key=\"item.id\"\n                :label=\"item.name\"\n                :value=\"item.id\">\n              </el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"关键字：\">\n            <el-input v-model=\"tableFrom.keywords\" placeholder=\"请输入姓名、电话、订单ID\" class=\"selWidth\" size=\"small\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\" size=\"small\" @click=\"getList(1)\" />\n            </el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n    </div>\n  </el-card>\n  <div class=\"mt20\">\n    <cards-data :card-lists=\"cardLists\" />\n  </div>\n  <el-card class=\"box-card\">\n     <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      class=\"table\"\n      highlight-current-row\n      :header-cell-style=\" {fontWeight:'bold'}\"\n    >\n      <el-table-column\n        label=\"订单号\"\n        prop=\"orderId\"\n        min-width=\"200\"\n      />\n      <el-table-column\n        prop=\"realName\"\n        label=\"用户信息\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        label=\"推荐人信息\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.spreadInfo.name }}</span>\n        </template>\n      </el-table-column>\n       <el-table-column\n        label=\"商品信息\"\n        min-width=\"400\"\n      >\n        <template slot-scope=\"scope\">\n          <el-popover trigger=\"hover\" placement=\"right\" >\n            <div v-if=\" scope.row.productList && scope.row.productList.length\" slot=\"reference\">\n              <div v-for=\"(val, i ) in scope.row.productList\" :key=\"i\" class=\"tabBox acea-row row-middle\" style=\"flex-wrap: inherit;\">\n                <div class=\"demo-image__preview mr10\">\n                  <el-image\n                    :src=\"val.info.image\"\n                    :preview-src-list=\"[val.info.image]\"\n                  />\n                </div>\n                <div class=\"text_overflow\">\n                  <span class=\"tabBox_tit mr10\">{{ val.info.productName + ' | ' }}{{ val.info.sku ? val.info.sku:'-' }}</span>\n                  <span class=\"tabBox_pice\">{{ '￥'+ val.info.price ? val.info.price + ' x '+ val.info.payNum : '-' }}</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"pup_card\" v-if=\" scope.row.productList && scope.row.productList.length\">\n              <div v-for=\"(val, i ) in scope.row.productList\" :key=\"i\" class=\"tabBox acea-row row-middle\" style=\"flex-wrap: inherit;\">\n                <div class=\"\">\n                  <span class=\"tabBox_tit mr10\">{{ val.info.productName + ' | ' }}{{ val.info.sku ? val.info.sku:'-' }}</span>\n                  <span class=\"tabBox_pice\">{{ '￥'+ val.info.price ? val.info.price + ' x '+ val.info.payNum : '-' }}</span>\n                </div>\n              </div>\n            </div>\n          </el-popover>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"payPrice\"\n        label=\"实际支付\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        prop=\"clerkName\"\n        label=\"核销员\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        prop=\"storeName\"\n        label=\"核销门店\"\n        min-width=\"150\"\n      />\n      <el-table-column\n        label=\"支付状态\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.paid | paidFilter }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"订单状态\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.statusStr.value}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"createTime\"\n        label=\"下单时间\"\n        min-width=\"150\"\n      />\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n</div>\n", null]}