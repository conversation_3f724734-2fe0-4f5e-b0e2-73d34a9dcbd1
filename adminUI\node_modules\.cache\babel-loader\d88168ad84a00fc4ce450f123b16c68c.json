{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\groupBuy\\groupList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\groupBuy\\groupList\\index.vue", "mtime": 1754050582448}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _marketing = require(\"@/api/marketing\");\nvar _index = _interopRequireDefault(require(\"@/components/cards/index\"));\nvar _permission = require(\"@/utils/permission\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// 权限判断函数\nvar _default = exports.default = {\n  name: \"groupList\",\n  components: {\n    cardsData: _index.default\n  },\n  data: function data() {\n    return {\n      listLoadingPink: false,\n      dialogVisible: false,\n      tableDataPink: {\n        data: []\n      },\n      tableData: {\n        data: [],\n        total: 0\n      },\n      listLoading: false,\n      tableFrom: {\n        dateLimit: '',\n        status: '',\n        page: 1,\n        limit: 20\n      },\n      fromList: this.$constants.fromList,\n      timeVal: [],\n      cardLists: []\n    };\n  },\n  mounted: function mounted() {\n    this.getStatistics();\n    this.getList();\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    handleClose: function handleClose() {\n      this.dialogVisible = false;\n    },\n    handleLook: function handleLook(id) {\n      this.dialogVisible = true;\n      this.getPink(id);\n    },\n    getPink: function getPink(id) {\n      var _this = this;\n      this.listLoadingPink = true;\n      (0, _marketing.combineOrderPinkApi)(id).then(function (res) {\n        _this.tableDataPink.data = res;\n        _this.listLoadingPink = false;\n      }).catch(function () {\n        _this.listLoadingPink = false;\n      });\n    },\n    selectChange: function selectChange(tab) {\n      this.tableFrom.dateLimit = tab;\n      this.tableFrom.page = 1;\n      this.timeVal = [];\n      this.getList();\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : '';\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 列表\n    getList: function getList(num) {\n      var _this2 = this;\n      this.listLoading = true;\n      this.tableFrom.page = num ? num : this.tableFrom.page;\n      (0, _marketing.combineListApi)(this.tableFrom).then(function (res) {\n        _this2.tableData.data = res.list;\n        _this2.tableData.total = res.total;\n        _this2.listLoading = false;\n      }).catch(function () {\n        _this2.listLoading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    },\n    // 统计\n    getStatistics: function getStatistics() {\n      var _this3 = this;\n      (0, _marketing.combineStatisticsApi)().then(function (res) {\n        _this3.cardLists = [{\n          name: '参与人数(人)',\n          count: res.countPeople,\n          color: '#1890FF',\n          class: 'one',\n          icon: 'iconleijiyonghushu'\n        }, {\n          name: '成团数量(个)',\n          count: res.countTeam,\n          color: '#A277FF',\n          class: 'two',\n          icon: 'iconxinzengyonghu'\n        }];\n      }).catch(function () {\n        _this3.listLoading = false;\n      });\n    }\n  }\n};", null]}