{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsMessage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsMessage\\index.vue", "mtime": 1754050582490}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport zbParser from '@/components/FormGenerator/components/parser/ZBParser'\nimport { configSaveForm, configInfo } from '@/api/systemConfig.js'\nexport default {\n  name: \"SmsMessage\",\n  components: { zbParser },\n  data() {\n    return {\n      isShow: true,\n      isCreate: 0,\n      editData: {},\n      formId: 111\n    }\n  },\n  mounted() {\n    this.getFormInfo()\n  },\n  methods: {\n    resetForm(formValue) {\n      this.isShow = false;\n    },\n    handlerSubmit(data) {\n      const tempArr = []\n      for (var key in data) {\n        const obj = {}\n        obj.name = key\n        obj.title = key\n        obj.value = data[key]\n        tempArr.push(obj)\n      }\n      const _pram = {\n        'fields': tempArr,\n        'id': this.formId,\n        'sort': 0,\n        'status': true\n      }\n      configSaveForm(_pram).then(res => {\n        this.getFormInfo()\n        this.$message.success('操作成功')\n      })\n    },\n    // 获取表单详情\n    getFormInfo() {\n      configInfo({ id: this.formId }).then(res => {\n        this.isShow = false\n        this.editData = res\n        this.isCreate = 1\n        setTimeout(() => { // 让表单重复渲染待编辑数据\n          this.isShow = true\n        }, 80)\n      })\n    }\n  }\n}\n", null]}