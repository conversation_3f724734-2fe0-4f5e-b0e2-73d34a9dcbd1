{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\index.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\index.js", "mtime": 1754050582342}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754554385233}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"AppMain\", {\n  enumerable: true,\n  get: function get() {\n    return _AppMain.default;\n  }\n});\nObject.defineProperty(exports, \"Navbar\", {\n  enumerable: true,\n  get: function get() {\n    return _Navbar.default;\n  }\n});\nObject.defineProperty(exports, \"Settings\", {\n  enumerable: true,\n  get: function get() {\n    return _Settings.default;\n  }\n});\nObject.defineProperty(exports, \"Sidebar\", {\n  enumerable: true,\n  get: function get() {\n    return _index.default;\n  }\n});\nObject.defineProperty(exports, \"TagsView\", {\n  enumerable: true,\n  get: function get() {\n    return _index2.default;\n  }\n});\nvar _AppMain = _interopRequireDefault(require(\"./AppMain\"));\nvar _Navbar = _interopRequireDefault(require(\"./Navbar\"));\nvar _Settings = _interopRequireDefault(require(\"./Settings\"));\nvar _index = _interopRequireDefault(require(\"./Sidebar/index.vue\"));\nvar _index2 = _interopRequireDefault(require(\"./TagsView/index.vue\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }", null]}