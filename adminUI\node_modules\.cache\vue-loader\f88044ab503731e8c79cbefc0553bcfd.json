{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\groupBuy\\groupGoods\\creatGroup.vue?vue&type=template&id=6e5df8ee&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\groupBuy\\groupGoods\\creatGroup.vue", "mtime": 1754050582445}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <el-steps :active=\"currentTab\" align-center finish-status=\"success\">\n        <el-step title=\"选择拼团商品\" />\n        <el-step title=\"填写基础信息\" />\n        <el-step title=\"修改商品详情\" />\n      </el-steps>\n    </div>\n    <el-form\n      ref=\"formValidate\"\n      v-loading=\"fullscreenLoading\"\n      class=\"formValidate mt20\"\n      :rules=\"ruleValidate\"\n      :model=\"formValidate\"\n      label-width=\"180px\"\n      @submit.native.prevent\n    >\n      <!-- 拼团商品-->\n      <div v-show=\"currentTab === 0\">\n        <el-form-item label=\"选择商品：\" prop=\"image\">\n          <div class=\"upLoadPicBox\" @click=\"changeGood\">\n            <div v-if=\"formValidate.image\" class=\"pictrue\"><img :src=\"formValidate.image\"></div>\n            <div v-else class=\"upLoad\">\n              <i class=\"el-icon-camera cameraIconfont\"/>\n            </div>\n          </div>\n        </el-form-item>\n      </div>\n      <!-- 商品信息-->\n      <div v-show=\"currentTab === 1\">\n        <el-row :gutter=\"24\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"商品主图：\" prop=\"image\">\n              <div class=\"upLoadPicBox\" @click=\"modalPicTap('1')\">\n                <div v-if=\"formValidate.image\" class=\"pictrue\"><img :src=\"formValidate.image\"></div>\n                <div v-else class=\"upLoad\">\n                  <i class=\"el-icon-camera cameraIconfont\" />\n                </div>\n              </div>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"商品轮播图：\" prop=\"imagelist\">\n              <div class=\"acea-row\">\n                <div\n                  v-for=\"(item,index) in formValidate.imagelist\"\n                  :key=\"index\"\n                  class=\"pictrue\"\n                  draggable=\"true\"\n                  @dragstart=\"handleDragStart($event, item)\"\n                  @dragover.prevent=\"handleDragOver($event, item)\"\n                  @dragenter=\"handleDragEnter($event, item)\"\n                  @dragend=\"handleDragEnd($event, item)\"\n                >\n                  <img :src=\"item\">\n                  <i class=\"el-icon-error btndel\" @click=\"handleRemove(index)\" />\n                </div>\n                <div v-if=\"formValidate.imagelist.length<10\" class=\"upLoadPicBox\" @click=\"modalPicTap('2')\">\n                  <div class=\"upLoad\">\n                    <i class=\"el-icon-camera cameraIconfont\" />\n                  </div>\n                </div>\n              </div>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"拼团名称：\" prop=\"title\">\n              <el-input v-model=\"formValidate.title\" maxlength=\"249\" placeholder=\"请输入拼团名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"拼团简介：\" prop=\"info\">\n              <el-input v-model=\"formValidate.info\"  maxlength=\"250\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入拼团简介\" />\n            </el-form-item>\n          </el-col>\n          <el-col v-bind=\"grid2\">\n            <el-form-item label=\"单位：\" prop=\"unitName\">\n              <el-input v-model=\"formValidate.unitName\" placeholder=\"请输入单位\" class=\"selWidthd\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"活动日期：\" prop=\"timeVal\">\n              <el-date-picker\n                class=\"mr20\"\n                v-model=\"formValidate.timeVal\"\n                type=\"daterange\"\n                value-format=\"yyyy-MM-dd\"\n                format=\"yyyy-MM-dd\"\n                range-separator=\"至\"\n                start-placeholder=\"开始日期\"\n                end-placeholder=\"结束日期\"\n                :picker-options=\"pickerOptions\"\n                @change=\"onchangeTime\">\n              </el-date-picker>\n              <span>设置活动开启结束时间，用户可以在设置时间内发起参与拼团</span>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"拼团时效(单位 小时)：\" prop=\"effectiveTime\">\n              <el-input-number v-model=\"formValidate.effectiveTime\" :min=\"1\"  :step=\"1\" step-strictly placeholder=\"请输入拼团人数\" class=\"selWidthd mr20\"/>\n              <span>用户发起拼团后开始计时，需在设置时间内邀请到规定好友人数参团，超过时效时间，则系统判定拼团失败，自动发起退款</span>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"拼团人数：\" prop=\"people\">\n              <el-input-number v-model=\"formValidate.people\" :min=\"2\"  :step=\"1\" step-strictly placeholder=\"请输入拼团人数\" class=\"selWidthd mr20\"/>\n              <span>单次拼团需要参与的用户数</span>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"购买数量限制：\" prop=\"num\">\n              <el-input-number v-model=\"formValidate.num\" :min=\"1\"  :step=\"1\" step-strictly placeholder=\"请输入帮砍次数\" class=\"selWidthd mr20\"/>\n              <span>活动时间内每个用户参与拼团的次数限制。例如设置为4，表示本次活动有效期内，每个用户最多可参与购买4次</span>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"单次购买数量限制：\" prop=\"onceNum\">\n              <el-input-number v-model=\"formValidate.onceNum\" :min=\"1\" :max=\"formValidate.num\"  :step=\"1\" step-strictly placeholder=\"请输入购买数量限制\" class=\"selWidthd mr20\"/>\n              <span>用户参与拼团时，一次购买最大数量限制。例如设置为2，表示参与拼团时，用户一次购买数量最大可选择2个</span>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"补齐人数：\" prop=\"virtualRation\">\n              <el-input-number v-model=\"formValidate.virtualRation\" :min=\"0\" :max=\"formValidate.people - 1\"   :step=\"1\" step-strictly placeholder=\"请输入补齐人数\" class=\"selWidthd mr20\"/>\n              <span>当用户参与拼团后，成团时效内未成团情况下，设置补齐人数可虚拟成团。例如：成团人数为10人，补齐人数为4人，真实用户需要参与6人成团才可以在最后未成团时自动补齐虚拟人员</span>\n            </el-form-item>\n          </el-col>\n          <el-col v-bind=\"grid2\">\n            <el-form-item label=\"排序：\" prop=\"sort\">\n              <el-input-number v-model=\"formValidate.sort\" :max=\"9999\" placeholder=\"请输入排序\" class=\"selWidthd\"/>\n            </el-form-item>\n          </el-col>\n          <el-col v-bind=\"grid2\">\n            <el-form-item label=\"运费模板：\" prop=\"tempId\">\n              <div class=\"acea-row\">\n                <el-select v-model=\"formValidate.tempId\" placeholder=\"请选择\"  class=\"selWidthd\">\n                  <el-option\n                    v-for=\"item in shippingList\"\n                    :key=\"item.id\"\n                    :label=\"item.name\"\n                    :value=\"item.id\"\n                  />\n                </el-select>\n                <!--<el-button class=\"mr15\" @click=\"addTem\">添加运费模板</el-button>-->\n              </div>\n            </el-form-item>\n          </el-col>\n          <!--<el-col v-bind=\"grid2\">-->\n            <!--<el-form-item label=\"热门推荐：\" required>-->\n              <!--<el-radio-group v-model=\"formValidate.isHost\">-->\n                <!--<el-radio :label=\"false\" class=\"radio\">关闭</el-radio>-->\n                <!--<el-radio :label=\"true\">开启</el-radio>-->\n              <!--</el-radio-group>-->\n            <!--</el-form-item>-->\n          <!--</el-col>-->\n          <el-col v-bind=\"grid2\">\n            <el-form-item label=\"活动状态：\" required>\n              <el-radio-group v-model=\"formValidate.isShow\">\n                <el-radio :label=\"false\" class=\"radio\">关闭</el-radio>\n                <el-radio :label=\"true\">开启</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <!-- 规格表格-->\n          <el-col :span=\"24\">\n            <el-form-item label=\"商品属性：\" class=\"labeltop\" required>\n              <el-table\n                ref=\"multipleTable\"\n                :data=\"ManyAttrValue\"\n                tooltip-effect=\"dark\"\n                style=\"width: 100%\"\n                @selection-change=\"handleSelectionChange\">\n                <el-table-column\n                  type=\"selection\"\n                  key=\"1\"\n                  v-if=\"formValidate.specType\"\n                  width=\"55\">\n                </el-table-column>\n                <template v-if=\"manyTabDate && formValidate.specType\">\n                  <el-table-column v-for=\"(item,iii) in manyTabDate\" :key=\"iii\" align=\"center\" :label=\"manyTabTit[iii].title\" min-width=\"80\">\n                    <template slot-scope=\"scope\">\n                      <span class=\"priceBox\" v-text=\"scope.row[iii]\" />\n                    </template>\n                  </el-table-column>\n                </template>\n                <el-table-column align=\"center\" label=\"图片\" min-width=\"80\">\n                  <template slot-scope=\"scope\">\n                    <div class=\"upLoadPicBox\" @click=\"modalPicTap('1','duo',scope.$index)\">\n                      <div v-if=\"scope.row.image\" class=\"pictrue tabPic\"><img :src=\"scope.row.image\"></div>\n                      <div v-else class=\"upLoad tabPic\">\n                        <i class=\"el-icon-camera cameraIconfont\" />\n                      </div>\n                    </div>\n                  </template>\n                </el-table-column>\n                <el-table-column v-for=\"(item,iii) in attrValue\" :key=\"iii\" :label=\"formThead[iii].title\" align=\"center\" min-width=\"140\">\n                  <template slot-scope=\"scope\">\n                    <el-input-number\n                      size=\"small\"\n                      v-if=\"formThead[iii].title === '拼团价'\"\n                      v-model=\"scope.row[iii]\"\n                      :min=\"0\"\n                      :precision=\"2\" :step=\"0.1\"\n                      class=\"priceBox\"\n                    />\n                    <el-input-number\n                      size=\"small\"\n                      v-else-if=\"formThead[iii].title === '限量'\"\n                      v-model=\"scope.row[iii]\"\n                      type=\"number\"\n                      :min=\"1\"\n                      :max=\"scope.row.stock\"\n                      :step=\"1\" step-strictly\n                      class=\"priceBox\"\n                    />\n                    <span v-else v-text=\"scope.row[iii]\" class=\"priceBox\" />\n                  </template>\n                </el-table-column>\n              </el-table>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </div>\n      <!-- 商品详情-->\n      <div v-show=\"currentTab === 2\">\n        <el-form-item label=\"商品详情：\">\n          <Tinymce v-model=\"formValidate.content\"></Tinymce>\n        </el-form-item>\n      </div>\n      <el-form-item style=\"margin-top:30px;\">\n        <el-button\n          v-show=\"(!$route.params.id && currentTab > 0) || ($route.params.id && currentTab===2)\"\n          type=\"primary\"\n          class=\"submission\"\n          size=\"small\"\n          @click=\"handleSubmitUp\"\n        >上一步</el-button>\n        <el-button\n          v-show=\"currentTab == 0\"\n          type=\"primary\"\n          class=\"submission\"\n          size=\"small\"\n          @click=\"handleSubmitNest1('formValidate')\"\n        >下一步</el-button>\n        <el-button\n          v-show=\"currentTab == 1\"\n          type=\"primary\"\n          class=\"submission\"\n          size=\"small\"\n          @click=\"handleSubmitNest2('formValidate')\"\n        >下一步</el-button>\n        <el-button\n          v-show=\"currentTab===2\"\n          :loading=\"loading\"\n          type=\"primary\"\n          class=\"submission\"\n          size=\"small\"\n          @click=\"handleSubmit('formValidate')\"\n          v-hasPermi=\"['admin:combination:update']\"\n        >提交</el-button>\n      </el-form-item>\n    </el-form>\n  </el-card>\n  <CreatTemplates ref=\"addTemplates\" @getList=\"getShippingList\" />\n</div>\n", null]}