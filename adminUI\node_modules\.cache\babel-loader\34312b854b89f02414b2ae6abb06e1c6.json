{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\Screenfull\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\Screenfull\\index.vue", "mtime": 1754050582270}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _screenfull = _interopRequireDefault(require(\"screenfull\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: 'Screenfull',\n  data: function data() {\n    return {\n      isFullscreen: false\n    };\n  },\n  mounted: function mounted() {\n    this.init();\n  },\n  beforeDestroy: function beforeDestroy() {\n    this.destroy();\n  },\n  methods: {\n    click: function click() {\n      if (!_screenfull.default.enabled) {\n        this.$message({\n          message: 'you browser can not work',\n          type: 'warning'\n        });\n        return false;\n      }\n      _screenfull.default.toggle();\n    },\n    change: function change() {\n      this.isFullscreen = _screenfull.default.isFullscreen;\n    },\n    init: function init() {\n      if (_screenfull.default.enabled) {\n        _screenfull.default.on('change', this.change);\n      }\n    },\n    destroy: function destroy() {\n      if (_screenfull.default.enabled) {\n        _screenfull.default.off('change', this.change);\n      }\n    }\n  }\n};", null]}