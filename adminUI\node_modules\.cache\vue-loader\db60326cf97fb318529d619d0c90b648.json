{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Settings\\index.vue?vue&type=template&id=126b135a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Settings\\index.vue", "mtime": 1754050582337}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"drawer-container\">\n  <div>\n    <h3 class=\"drawer-title\">主题风格设置</h3>\n    <div class=\"drawer-item\">\n       <span>主题颜色</span>\n      <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\" />\n    <div class=\"drawer-item\" v-if=\"topNav\">\n      <span>开启 Icon</span>\n      <el-switch v-model=\"navIcon\" class=\"drawer-switch\" />\n    </div>\n\n    <div class=\"drawer-item\">\n      <span>开启 Tags-Views</span>\n      <el-switch v-model=\"tagsView\" class=\"drawer-switch\" />\n    </div>\n\n    <div class=\"drawer-item\">\n      <span>固定 Header</span>\n      <el-switch v-model=\"fixedHeader\" class=\"drawer-switch\" />\n    </div>\n\n    <div class=\"drawer-item\">\n      <span>显示 Logo</span>\n      <el-switch v-model=\"sidebarLogo\" class=\"drawer-switch\" />\n    </div>\n\n    <el-divider/>\n    <el-button size=\"small\" type=\"primary\" plain icon=\"el-icon-document-add\" @click=\"saveSetting\">保存配置</el-button>\n    <el-button size=\"small\" plain icon=\"el-icon-refresh\" @click=\"resetSetting\">重置配置</el-button>\n\n  </div>\n  </div>\n</div>\n", null]}