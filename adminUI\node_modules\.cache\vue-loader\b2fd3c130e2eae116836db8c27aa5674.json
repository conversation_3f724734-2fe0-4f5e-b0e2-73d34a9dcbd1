{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsTemplate\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsTemplate\\index.vue", "mtime": 1754050582490}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { smsTempLstApi, tempCreateApi } from '@/api/sms'\r\nimport { roterPre } from '@/settings'\r\nimport { mapGetters } from 'vuex'\r\nimport zbParser from '@/components/FormGenerator/components/parser/ZBParser'\r\nimport {Debounce} from '@/utils/validate'\r\nexport default {\r\n  name: 'SmsTemplate',\r\n  components: { zbParser },\r\n  filters: {\r\n    statusFilter(status) {\r\n      const statusMap = {\r\n        0: '不可用',\r\n        1: '可用'\r\n      }\r\n      return statusMap[status]\r\n    },\r\n    typesFilter(status) {\r\n      const statusMap = {\r\n        1: '验证码',\r\n        2: '通知',\r\n        3: '推广'\r\n      }\r\n      return statusMap[status]\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isCreate: 0,\r\n      editData: {},\r\n      dialogVisible: false,\r\n      fullscreenLoading: false,\r\n      listLoading: false,\r\n      tableData: {\r\n        data: [],\r\n        total: 0\r\n      },\r\n      tableFrom: {\r\n        page: 1,\r\n        limit: 20\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'isLogin'\r\n    ])\r\n  },\r\n  mounted() {\r\n    if (!this.isLogin) {\r\n      this.$router.push('/operation/onePass?url=' + this.$route.path)\r\n    } else {\r\n      this.getList()\r\n    }\r\n  },\r\n  methods: {\r\n    resetForm(formValue) {\r\n      this.handleClose();\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false\r\n      this.editData = {}\r\n    },\r\n    handlerSubmit:Debounce(function(formValue) {\r\n      tempCreateApi(formValue).then(data => {\r\n        this.$message.success('新增成功')\r\n        this.dialogVisible = false\r\n        this.editData = {}\r\n        this.getList()\r\n      })\r\n    }),\r\n    add() {\r\n      this.dialogVisible = true\r\n    },\r\n    // 查看是否登录\r\n    onIsLogin() {\r\n      this.fullscreenLoading = true\r\n      this.$store.dispatch('user/isLogin').then(async res => {\r\n        const data = res\r\n        if (!data.status) {\r\n          this.$message.warning('请先登录')\r\n          this.$router.push( '/operation/onePass?url=' + this.$route.path)\r\n        } else {\r\n          this.getList()\r\n        }\r\n        this.fullscreenLoading = false\r\n      }).catch(res => {\r\n        this.$router.push( '/operation/onePass?url=' + this.$route.path)\r\n        this.fullscreenLoading = false\r\n      })\r\n    },\r\n    // 列表\r\n    getList() {\r\n      this.listLoading = true\r\n      smsTempLstApi(this.tableFrom).then(res => {\r\n        this.tableData.data = res.data\r\n        this.tableData.total = res.count\r\n        this.listLoading = false\r\n      }).catch(res => {\r\n        this.listLoading = false\r\n      })\r\n    },\r\n    pageChange(page) {\r\n      this.tableFrom.page = page\r\n      this.getList()\r\n    },\r\n    handleSizeChange(val) {\r\n      this.tableFrom.limit = val\r\n      this.getList()\r\n    },\r\n    // 表格搜索\r\n    userSearchs() {\r\n      this.tableFrom.page = 1\r\n      this.getList()\r\n    }\r\n  }\r\n}\r\n", null]}