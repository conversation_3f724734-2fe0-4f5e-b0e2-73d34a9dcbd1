{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\deliveryAddress\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\deliveryAddress\\index.vue", "mtime": 1754050582508}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n  import systemStore from './addPoint';\n  import { storeListApi, storeGetCountApi, storeUpdateStatusApi, storeDeleteApi, allDeleteApi, storeRecoveryApi } from '@/api/storePoint';\n  import { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nexport default {\n  name: 'Point',\n  components: { systemStore },\n  data () {\n    return{\n      artFrom: {\n        page: 1,\n        limit: 20,\n        status: '1',\n        keywords: ''\n      },\n      loading: false,\n      tableData:[],\n      total:0,\n      headerCount:{}\n    }\n  },\n  created () {\n    this.storeGetCount();\n    this.tableList();\n  },\n  methods: {\n    checkPermi,\n    //头部数量显示；\n    storeGetCount(){\n      let that = this;\n      storeGetCountApi().then(res=>{\n        that.headerCount = res\n      })\n    },\n    //表格列表\n    tableList(){\n      let that = this;\n      that.loading = true;\n      storeListApi (that.artFrom).then(res=>{\n        that.loading = false;\n        that.tableData = res.list;\n        that.total = res.total;\n      })\n    },\n    //切换页数\n    pageChange(index){\n      this.artFrom.page = index;\n      this.tableList();\n    },\n    //切换显示条数\n    sizeChange(index){\n      this.artFrom.limit = index;\n      this.tableList();\n    },\n    //头部切换\n    onClickTab() {\n      this.artFrom.keywords = '';\n      this.tableList();\n    },\n    //搜索\n    search(){\n      this.artFrom.page = 1;\n      this.tableList();\n    },\n    //是否显示\n    onchangeIsShow(id, isShow){\n      let that = this;\n      storeUpdateStatusApi({id:id,status:isShow}).then(() => {\n        that.$message.success(\"操作成功\");\n        that.tableList();\n        that.storeGetCount();\n      }).catch(()=>{\n        row.isShow = !row.isShow\n      })\n    },\n    // 恢复\n    storeRecovery(id){\n      this.$modalSure('恢复提货吗').then(() => {\n        storeRecoveryApi({ id: id }).then(() => {\n          this.$message.success('恢复成功')\n          this.storeGetCount();\n          this.tableList();\n        })\n      })\n    },\n    //刪除\n    storeDelete(id){\n      let that = this;\n      that.$modalSure('删除提货点吗？').then(() => {\n        storeDeleteApi({ id: id }).then(() => {\n          that.$message.success('删除成功')\n          that.storeGetCount();\n          that.tableList();\n        })\n      })\n    },\n    allDelete(id){\n      this.$modalSure().then(() => {\n        allDeleteApi({ id: id }).then(() => {\n          this.$message.success('删除成功')\n          this.storeGetCount();\n          this.tableList();\n        })\n      })\n    },\n    //添加\n    add() {\n      this.$refs.template.dialogFormVisible = true;\n    },\n   //编辑\n    edit(id) {\n      this.$refs.template.dialogFormVisible = true;\n      this.$refs.template.getInfo(id);\n    }\n  }\n}\n", null]}