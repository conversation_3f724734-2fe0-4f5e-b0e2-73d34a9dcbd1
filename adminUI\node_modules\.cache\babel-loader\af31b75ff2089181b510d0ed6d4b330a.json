{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillConfig\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillConfig\\index.vue", "mtime": 1754050582453}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _ZBParser = _interopRequireDefault(require(\"@/components/FormGenerator/components/parser/ZBParser\"));\nvar _systemConfig = require(\"@/api/systemConfig.js\");\nvar _marketing = require(\"@/api/marketing\");\nvar _permission = require(\"@/utils/permission\");\nvar _validate = require(\"@/utils/validate\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; } //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// 权限判断函数\nvar _default = exports.default = {\n  name: \"SeckillConfig\",\n  components: {\n    zbParser: _ZBParser.default\n  },\n  data: function data() {\n    return {\n      dialogVisible: false,\n      isShow: true,\n      isCreate: 0,\n      editData: {},\n      formId: 123,\n      listLoading: true,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        name: '',\n        isDel: false,\n        status: ''\n      },\n      seckillId: '',\n      loading: false\n    };\n  },\n  mounted: function mounted() {\n    this.getList();\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    resetForm: function resetForm(formValue) {\n      this.dialogVisible = false;\n    },\n    // 删除\n    handleDelete: function handleDelete(id, idx) {\n      var _this = this;\n      this.$modalSure().then(function () {\n        (0, _marketing.seckillDeleteApi)({\n          id: id\n        }).then(function () {\n          _this.$message.success('删除成功');\n          _this.tableData.data.splice(idx, 1);\n        });\n      });\n    },\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this2 = this;\n      (0, _marketing.seckillConfigStatusApi)(row.id, {\n        status: row.status\n      }).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              _this2.$message.success('修改成功');\n              _this2.getList();\n            case 1:\n              return _context.a(2);\n          }\n        }, _callee);\n      }))).catch(function () {\n        row.status = !row.status;\n      });\n    },\n    onEditSort: function onEditSort(row) {\n      this.$set(row, 'isEdit', true);\n    },\n    onBlur: function onBlur(row) {\n      this.$set(row, 'isEdit', false);\n      this.onEdit(row.id, row);\n    },\n    // 获取表单详情\n    getFormInfo: function getFormInfo(id) {\n      var _this3 = this;\n      this.loading = true;\n      (0, _marketing.seckillInfoApi)({\n        id: id\n      }).then(function (res) {\n        _this3.editData = res;\n        _this3.dialogVisible = true;\n        _this3.loading = false;\n      }).catch(function () {\n        _this3.loading = false;\n      });\n    },\n    // 编辑\n    handleEdit: function handleEdit(id) {\n      this.seckillId = id;\n      this.getFormInfo(id);\n      this.isCreate = 1;\n    },\n    // 编辑\n    onEdit: function onEdit(id, obj) {\n      var _this4 = this;\n      var data = obj ? obj : this.editData;\n      (0, _marketing.seckillUpdateApi)({\n        id: id\n      }, data).then(function (res) {\n        _this4.isSuccess();\n      }).catch(function (res) {\n        _this4.listLoading = false;\n      });\n    },\n    // 提交\n    handlerSubmit: (0, _validate.Debounce)(function (formValue) {\n      var _this5 = this;\n      if (formValue.time.split(',')[0].split(':')[0] > formValue.time.split(',')[1].split(':')[0]) return this.$message.error('请填写正确的时间范围');\n      this.isCreate === 0 ? (0, _marketing.seckillSaveApi)(formValue).then(function (res) {\n        _this5.isSuccess();\n      }) : (0, _marketing.seckillUpdateApi)({\n        id: this.seckillId\n      }, formValue).then(function (res) {\n        _this5.isSuccess();\n      });\n    }),\n    isSuccess: function isSuccess() {\n      this.$message.success('操作成功');\n      this.dialogVisible = false;\n      this.getList();\n    },\n    // 列表\n    getList: function getList(num) {\n      var _this6 = this;\n      this.listLoading = true;\n      this.tableFrom.page = num ? num : this.tableFrom.page;\n      (0, _marketing.seckillListApi)(this.tableFrom).then(function (res) {\n        _this6.tableData.data = res.list;\n        _this6.tableData.total = res.total;\n        _this6.tableData.data.map(function (item) {\n          return _this6.$set(item, 'isEdit', false);\n        });\n        _this6.listLoading = false;\n      }).catch(function (res) {\n        _this6.listLoading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    },\n    add: function add() {\n      this.isCreate = 0;\n      this.dialogVisible = true;\n    },\n    handleClose: function handleClose() {\n      this.dialogVisible = false;\n      this.editData = {};\n    }\n  }\n};", null]}