{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\collateOrder\\index.vue?vue&type=template&id=48ed33d1&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\collateOrder\\index.vue", "mtime": 1754050582505}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\"el-card\", { staticClass: \"box-card\" }, [\n        _c(\"div\", { staticClass: \"clearfix\" }, [\n          _c(\n            \"div\",\n            { staticClass: \"container\" },\n            [\n              _c(\n                \"el-form\",\n                {\n                  attrs: {\n                    size: \"small\",\n                    \"label-width\": \"100px\",\n                    inline: true,\n                  },\n                },\n                [\n                  _c(\n                    \"el-form-item\",\n                    { staticClass: \"width100\", attrs: { label: \"时间选择：\" } },\n                    [\n                      _c(\n                        \"el-radio-group\",\n                        {\n                          staticClass: \"mr20\",\n                          attrs: { type: \"button\", size: \"small\" },\n                          on: {\n                            change: function ($event) {\n                              return _vm.selectChange(_vm.tableFrom.dateLimit)\n                            },\n                          },\n                          model: {\n                            value: _vm.tableFrom.dateLimit,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.tableFrom, \"dateLimit\", $$v)\n                            },\n                            expression: \"tableFrom.dateLimit\",\n                          },\n                        },\n                        _vm._l(_vm.fromList.fromTxt, function (item, i) {\n                          return _c(\n                            \"el-radio-button\",\n                            { key: i, attrs: { label: item.val } },\n                            [_vm._v(_vm._s(item.text))]\n                          )\n                        }),\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\"el-date-picker\", {\n                        staticStyle: { width: \"250px\" },\n                        attrs: {\n                          \"value-format\": \"yyyy-MM-dd\",\n                          format: \"yyyy-MM-dd\",\n                          size: \"small\",\n                          type: \"daterange\",\n                          placement: \"bottom-end\",\n                          placeholder: \"自定义时间\",\n                        },\n                        on: { change: _vm.onchangeTime },\n                        model: {\n                          value: _vm.timeVal,\n                          callback: function ($$v) {\n                            _vm.timeVal = $$v\n                          },\n                          expression: \"timeVal\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"选择门店：\" } },\n                    [\n                      _c(\n                        \"el-select\",\n                        {\n                          staticClass: \"selWidth\",\n                          attrs: {\n                            filterable: \"\",\n                            placeholder: \"请选择\",\n                            clearable: \"\",\n                          },\n                          on: {\n                            change: function ($event) {\n                              return _vm.getList(1)\n                            },\n                          },\n                          model: {\n                            value: _vm.tableFrom.storeId,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.tableFrom, \"storeId\", $$v)\n                            },\n                            expression: \"tableFrom.storeId\",\n                          },\n                        },\n                        _vm._l(_vm.storeSelectList, function (item) {\n                          return _c(\"el-option\", {\n                            key: item.id,\n                            attrs: { label: item.name, value: item.id },\n                          })\n                        }),\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"关键字：\" } },\n                    [\n                      _c(\n                        \"el-input\",\n                        {\n                          staticClass: \"selWidth\",\n                          attrs: {\n                            placeholder: \"请输入姓名、电话、订单ID\",\n                            size: \"small\",\n                            clearable: \"\",\n                          },\n                          model: {\n                            value: _vm.tableFrom.keywords,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.tableFrom, \"keywords\", $$v)\n                            },\n                            expression: \"tableFrom.keywords\",\n                          },\n                        },\n                        [\n                          _c(\"el-button\", {\n                            attrs: {\n                              slot: \"append\",\n                              icon: \"el-icon-search\",\n                              size: \"small\",\n                            },\n                            on: {\n                              click: function ($event) {\n                                return _vm.getList(1)\n                              },\n                            },\n                            slot: \"append\",\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ]),\n      ]),\n      _vm._v(\" \"),\n      _c(\n        \"div\",\n        { staticClass: \"mt20\" },\n        [_c(\"cards-data\", { attrs: { \"card-lists\": _vm.cardLists } })],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.listLoading,\n                  expression: \"listLoading\",\n                },\n              ],\n              staticClass: \"table\",\n              staticStyle: { width: \"100%\" },\n              attrs: {\n                data: _vm.tableData.data,\n                size: \"mini\",\n                \"highlight-current-row\": \"\",\n                \"header-cell-style\": { fontWeight: \"bold\" },\n              },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { label: \"订单号\", prop: \"orderId\", \"min-width\": \"200\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"realName\",\n                  label: \"用户信息\",\n                  \"min-width\": \"100\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"推荐人信息\", \"min-width\": \"100\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [_vm._v(_vm._s(scope.row.spreadInfo.name))]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"商品信息\", \"min-width\": \"400\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-popover\",\n                          { attrs: { trigger: \"hover\", placement: \"right\" } },\n                          [\n                            scope.row.productList &&\n                            scope.row.productList.length\n                              ? _c(\n                                  \"div\",\n                                  {\n                                    attrs: { slot: \"reference\" },\n                                    slot: \"reference\",\n                                  },\n                                  _vm._l(\n                                    scope.row.productList,\n                                    function (val, i) {\n                                      return _c(\n                                        \"div\",\n                                        {\n                                          key: i,\n                                          staticClass:\n                                            \"tabBox acea-row row-middle\",\n                                          staticStyle: {\n                                            \"flex-wrap\": \"inherit\",\n                                          },\n                                        },\n                                        [\n                                          _c(\n                                            \"div\",\n                                            {\n                                              staticClass:\n                                                \"demo-image__preview mr10\",\n                                            },\n                                            [\n                                              _c(\"el-image\", {\n                                                attrs: {\n                                                  src: val.info.image,\n                                                  \"preview-src-list\": [\n                                                    val.info.image,\n                                                  ],\n                                                },\n                                              }),\n                                            ],\n                                            1\n                                          ),\n                                          _vm._v(\" \"),\n                                          _c(\n                                            \"div\",\n                                            { staticClass: \"text_overflow\" },\n                                            [\n                                              _c(\n                                                \"span\",\n                                                {\n                                                  staticClass:\n                                                    \"tabBox_tit mr10\",\n                                                },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      val.info.productName +\n                                                        \" | \"\n                                                    ) +\n                                                      _vm._s(\n                                                        val.info.sku\n                                                          ? val.info.sku\n                                                          : \"-\"\n                                                      )\n                                                  ),\n                                                ]\n                                              ),\n                                              _vm._v(\" \"),\n                                              _c(\n                                                \"span\",\n                                                { staticClass: \"tabBox_pice\" },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      \"￥\" + val.info.price\n                                                        ? val.info.price +\n                                                            \" x \" +\n                                                            val.info.payNum\n                                                        : \"-\"\n                                                    )\n                                                  ),\n                                                ]\n                                              ),\n                                            ]\n                                          ),\n                                        ]\n                                      )\n                                    }\n                                  ),\n                                  0\n                                )\n                              : _vm._e(),\n                            _vm._v(\" \"),\n                            scope.row.productList &&\n                            scope.row.productList.length\n                              ? _c(\n                                  \"div\",\n                                  { staticClass: \"pup_card\" },\n                                  _vm._l(\n                                    scope.row.productList,\n                                    function (val, i) {\n                                      return _c(\n                                        \"div\",\n                                        {\n                                          key: i,\n                                          staticClass:\n                                            \"tabBox acea-row row-middle\",\n                                          staticStyle: {\n                                            \"flex-wrap\": \"inherit\",\n                                          },\n                                        },\n                                        [\n                                          _c(\"div\", {}, [\n                                            _c(\n                                              \"span\",\n                                              {\n                                                staticClass: \"tabBox_tit mr10\",\n                                              },\n                                              [\n                                                _vm._v(\n                                                  _vm._s(\n                                                    val.info.productName + \" | \"\n                                                  ) +\n                                                    _vm._s(\n                                                      val.info.sku\n                                                        ? val.info.sku\n                                                        : \"-\"\n                                                    )\n                                                ),\n                                              ]\n                                            ),\n                                            _vm._v(\" \"),\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"tabBox_pice\" },\n                                              [\n                                                _vm._v(\n                                                  _vm._s(\n                                                    \"￥\" + val.info.price\n                                                      ? val.info.price +\n                                                          \" x \" +\n                                                          val.info.payNum\n                                                      : \"-\"\n                                                  )\n                                                ),\n                                              ]\n                                            ),\n                                          ]),\n                                        ]\n                                      )\n                                    }\n                                  ),\n                                  0\n                                )\n                              : _vm._e(),\n                          ]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"payPrice\",\n                  label: \"实际支付\",\n                  \"min-width\": \"100\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"clerkName\",\n                  label: \"核销员\",\n                  \"min-width\": \"100\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"storeName\",\n                  label: \"核销门店\",\n                  \"min-width\": \"150\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"支付状态\", \"min-width\": \"80\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(_vm._s(_vm._f(\"paidFilter\")(scope.row.paid))),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"订单状态\", \"min-width\": \"100\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [_vm._v(_vm._s(scope.row.statusStr.value))]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"createTime\",\n                  label: \"下单时间\",\n                  \"min-width\": \"150\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"block\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"page-sizes\": [20, 40, 60, 80],\n                  \"page-size\": _vm.tableFrom.limit,\n                  \"current-page\": _vm.tableFrom.page,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.tableData.total,\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChange,\n                  \"current-change\": _vm.pageChange,\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}