{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\AppMain.vue?vue&type=template&id=078753dd&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\AppMain.vue", "mtime": 1754050582335}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"section\",\n    { staticClass: \"app-main\" },\n    [\n      _c(\n        \"transition\",\n        { attrs: { name: \"fade-transform\", mode: \"out-in\" } },\n        [\n          _c(\n            \"keep-alive\",\n            { attrs: { include: _vm.cachedViews } },\n            [_c(\"router-view\", { key: _vm.key })],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"div\",\n        { staticClass: \"footers\" },\n        [\n          _vm._l(_vm.links, function (item) {\n            return _c(\n              \"el-link\",\n              {\n                key: item.key,\n                staticClass: \"mr15 mb20\",\n                attrs: { href: item.href, target: \"_blank\" },\n              },\n              [_vm._v(_vm._s(item.title))]\n            )\n          }),\n          _vm._v(\" \"),\n          _c(\"div\", {\n            staticClass: \"title mb15\",\n            domProps: { innerHTML: _vm._s(_vm.$t(\"appMain.copyright\")) },\n          }),\n        ],\n        2\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}