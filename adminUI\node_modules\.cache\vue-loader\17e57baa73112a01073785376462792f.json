{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\loginFrom.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\loginFrom.vue", "mtime": 1754050582486}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { configApi } from '@/api/sms'\r\nexport default {\r\n  name: 'Login',\r\n  data() {\r\n    return {\r\n      formInline: {\r\n        account: '',\r\n        password: ''\r\n      },\r\n      ruleInline: {\r\n        account: [\r\n          { required: true, message: '请输入用户名', trigger: 'blur' }\r\n        ],\r\n        password: [\r\n          { required: true, message: '请输入密码', trigger: 'blur' }\r\n        ]\r\n      },\r\n      passwordType: 'password',\r\n      loading: false\r\n    }\r\n  },\r\n  created() {\r\n    var _this = this\r\n    document.onkeydown = function(e) {\r\n      const key = window.event.keyCode\r\n      if (key === 13) {\r\n        _this.handleSubmit('formInline')\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    showPwd() {\r\n      if (this.passwordType === 'password') {\r\n        this.passwordType = ''\r\n      } else {\r\n        this.passwordType = 'password'\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.password.focus()\r\n      })\r\n    },\r\n    handleSubmit(name) {\r\n      this.$refs[name].validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          configApi(this.formInline).then(async res => {\r\n            this.$message.success('登录成功!')\r\n            this.$store.dispatch('user/isLogin')\r\n            this.$emit('on-Login')\r\n            this.loading = false;\r\n          }).catch(()=>{\r\n            this.loading = false;\r\n          })\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    // 修改密码\r\n    changePassword() {\r\n      this.$emit('on-change')\r\n    },\r\n    changeReg() {\r\n      this.$emit('on-changes')\r\n    }\r\n  }\r\n}\r\n", null]}