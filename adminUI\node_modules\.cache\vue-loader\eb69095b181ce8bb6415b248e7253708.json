{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=template&id=2d2bbdc2", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1754050582340}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div v-if=\"!item.hidden\">\n  <template v-if=\"hasOneShowingChild(item.child, item) && (!onlyOneChild.child || onlyOneChild.noShowingChildren) && !item.alwaysShow\">\n    <app-link v-if=\"onlyOneChild\" :to=\"resolvePath(onlyOneChild.url)\">\n      <el-menu-item :index=\"resolvePath(onlyOneChild.url)\" :class=\"{'submenu-title-noDropdown': !isNest}\">\n        <item :icon=\"onlyOneChild.extra || (item.meta && item.extra)\" :title=\"$t('dashboard.'+onlyOneChild.name)\" />\n      </el-menu-item>\n    </app-link>\n  </template>\n  <el-submenu v-else ref=\"subMenu\" :index=\"resolvePath(item.url)\" popper-append-to-body>\n    <template slot=\"title\">\n      <item v-if=\"item\" :icon=\"item && item.extra\" :title=\"$t('dashboard.'+item.name)\" />\n    </template>\n    <sidebar-item\n      v-for=\"childs in item.child\"\n      :key=\"childs.url\"\n      :is-nest=\"true\"\n      :item=\"childs\"\n      :base-path=\"resolvePath(childs.url)\"\n      class=\"nest-menu\"\n    />\n  </el-submenu>\n</div>\n", null]}