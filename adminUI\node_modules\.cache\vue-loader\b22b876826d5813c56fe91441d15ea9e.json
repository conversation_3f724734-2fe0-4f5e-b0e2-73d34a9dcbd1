{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\deliveryAddress\\addPoint.vue?vue&type=template&id=fd59e378", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\deliverGoods\\takeGoods\\deliveryAddress\\addPoint.vue", "mtime": 1754050582507}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<el-dialog v-model=\"dialogFormVisible\" :title=\"id?'修改提货点':'添加提货点'\" :visible.sync=\"dialogFormVisible\" width=\"750px\" @close=\"cancel\">\n  <template v-if=\"dialogFormVisible\">\n    <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"150px\" class=\"demo-ruleForm\" @submit.native.prevent v-loading=\"loading\">\n      <el-form-item label=\"提货点名称：\" prop=\"name\">\n        <el-input v-model=\"ruleForm.name\" maxlength=\"40\" placeholder=\"请输入提货点名称\" class=\"dialogWidth\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"提货点简介：\">\n        <el-input v-model=\"ruleForm.introduction\" maxlength=\"100\"  placeholder=\"请输入提货点简介\" class=\"dialogWidth\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"提货点手机号：\" prop=\"phone\">\n        <el-input v-model=\"ruleForm.phone\" placeholder=\"请输入提货点手机号\" class=\"dialogWidth\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"提货点地址：\" prop=\"address\">\n        <el-cascader\n          class=\"dialogWidth\"\n          clearable\n          v-model=\"ruleForm.address\"\n          :options=\"addresData\"\n          :props=\"{ value: 'name', label: 'name',children:'child',expandTrigger: 'hover'}\"\n          @change=\"handleChange\"></el-cascader>\n      </el-form-item>\n      <el-form-item label=\"详细地址：\" prop=\"detailedAddress\">\n        <el-input v-model=\"ruleForm.detailedAddress\" placeholder=\"请输入详细地址\" class=\"dialogWidth\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"提货点营业：\">\n        <el-time-picker\n          is-range\n          v-model=\"dayTime\"\n          range-separator=\"至\"\n          start-placeholder=\"开始时间\"\n          end-placeholder=\"结束时间\"\n          placeholder=\"请选择时间营业时间\"\n          value-format=\"HH:mm:ss\"\n          @change=\"onchangeTime\">\n        </el-time-picker>\n      </el-form-item>\n      <!-- prop=\"image\"-->\n      <el-form-item label=\"提货点logo：\">\n        <div class=\"upLoadPicBox\" @click=\"modalPicTap('1')\">\n          <div class=\"pictrue\" v-if=\"ruleForm.image\"><img :src=\"ruleForm.image\"></div>\n          <div v-else class=\"upLoad\">\n            <i class=\"el-icon-camera cameraIconfont\" />\n          </div>\n        </div>\n      </el-form-item>\n      <el-form-item label=\"经纬度：\" prop=\"latitude\">\n        <el-tooltip content=\"请点击查找位置选择位置\">\n          <el-input v-model=\"ruleForm.latitude\" placeholder=\"请查找位置\" class=\"dialogWidth\" readOnly>\n            <el-button slot=\"append\" @click=\"onSearch\">查找位置</el-button>\n          </el-input>\n        </el-tooltip>\n      </el-form-item>\n    </el-form>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"cancel\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"editForm('ruleForm')\" v-if=\"id\"  v-hasPermi=\"['admin:system:store:update']\">修改</el-button>\n      <el-button type=\"primary\" @click=\"submitForm('ruleForm')\" v-else v-hasPermi=\"['admin:system:store:save']\">提交</el-button>\n    </div>\n    <el-dialog v-model=\"modalMap\" title='上传经纬度' :visible.sync=\"modalMap\" append-to-body class=\"mapBox\" width=\"500px\">\n      <iframe\n        id=\"mapPage\" width=\"100%\" height=\"100%\" frameborder=0\n        v-bind:src=\"keyUrl\"\n      ></iframe>\n    </el-dialog>\n  </template>\n</el-dialog>\n", null]}