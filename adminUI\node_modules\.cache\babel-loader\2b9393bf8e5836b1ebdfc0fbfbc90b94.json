{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\edit.vue", "mtime": 1754050582501}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar roleApi = _interopRequireWildcard(require(\"@/api/role.js\"));\nvar _validate = require(\"@/utils/validate\");\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"roleEdit\",\n  props: {\n    isCreate: {\n      type: Number,\n      required: true\n    },\n    editData: {\n      type: Object,\n      default: null\n    }\n  },\n  data: function data() {\n    return {\n      pram: {\n        roleName: null,\n        rules: \"\",\n        status: null,\n        id: null\n      },\n      menuExpand: false,\n      menuNodeAll: false,\n      menuOptions: [],\n      menuCheckStrictly: true,\n      currentNodeId: [],\n      defaultProps: {\n        children: \"childList\",\n        label: \"name\"\n      },\n      menuIds: []\n    };\n  },\n  mounted: function mounted() {\n    this.initEditData();\n    this.getCacheMenu();\n  },\n  methods: {\n    close: function close() {\n      this.$emit(\"hideEditDialog\");\n    },\n    initEditData: function initEditData() {\n      var _this = this;\n      if (this.isCreate !== 1) return;\n      var _this$editData = this.editData,\n        roleName = _this$editData.roleName,\n        status = _this$editData.status,\n        id = _this$editData.id;\n      this.pram.roleName = roleName;\n      this.pram.status = status;\n      this.pram.id = id;\n      var loading = this.$loading({\n        lock: true,\n        text: \"Loading\"\n      });\n      roleApi.getInfo(id).then(function (res) {\n        _this.menuOptions = res.menuList;\n        _this.checkDisabled(_this.menuOptions);\n        loading.close();\n        _this.getTreeId(res.menuList);\n        _this.$nextTick(function () {\n          _this.menuIds.forEach(function (i, n) {\n            var node = _this.$refs.menu.getNode(i);\n            if (node.isLeaf) {\n              _this.$refs.menu.setChecked(node, true);\n            }\n          });\n        });\n      });\n    },\n    handlerSubmit: (0, _validate.Debounce)(function (form) {\n      var _this2 = this;\n      this.$refs[form].validate(function (valid) {\n        if (!valid) return;\n        var roles = _this2.getMenuAllCheckedKeys().toString();\n        _this2.pram.rules = roles;\n        if (_this2.isCreate === 0) {\n          _this2.handlerSave();\n        } else {\n          _this2.handlerEdit();\n        }\n      });\n    }),\n    handlerSave: function handlerSave() {\n      var _this3 = this;\n      roleApi.addRole(this.pram).then(function (data) {\n        _this3.$message.success(_this3.$t(\"admin.system.role.createIdentity\") + _this3.$t(\"common.operationSuccess\"));\n        _this3.$emit(\"hideEditDialog\");\n      });\n    },\n    handlerEdit: function handlerEdit() {\n      var _this4 = this;\n      roleApi.updateRole(this.pram).then(function (data) {\n        _this4.$message.success(_this4.$t(\"admin.system.role.editIdentity\") + _this4.$t(\"common.operationSuccess\"));\n        _this4.$emit(\"hideEditDialog\");\n      });\n    },\n    rulesSelect: function rulesSelect(selectKeys) {\n      this.pram.rules = selectKeys;\n    },\n    // 树权限（展开/折叠）\n    handleCheckedTreeExpand: function handleCheckedTreeExpand(value, type) {\n      if (type == \"menu\") {\n        var treeList = this.menuOptions;\n        for (var i = 0; i < treeList.length; i++) {\n          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;\n        }\n      }\n    },\n    // 树权限（全选/全不选）\n    handleCheckedTreeNodeAll: function handleCheckedTreeNodeAll(value, type) {\n      if (type == \"menu\") {\n        this.$refs.menu.setCheckedNodes(value ? this.menuOptions : []);\n      }\n    },\n    // 树权限（父子联动）\n    handleCheckedTreeConnect: function handleCheckedTreeConnect(value, type) {\n      if (type == \"menu\") {\n        this.menuCheckStrictly = value ? true : false;\n      }\n    },\n    // 所有菜单节点数据\n    getMenuAllCheckedKeys: function getMenuAllCheckedKeys() {\n      // 目前被选中的菜单节点\n      var checkedKeys = this.$refs.menu.getCheckedKeys();\n      // 半选中的菜单节点\n      var halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);\n      return checkedKeys;\n    },\n    getCacheMenu: function getCacheMenu() {\n      var _this5 = this;\n      if (this.isCreate !== 0) return;\n      var loading = this.$loading({\n        lock: true,\n        text: \"Loading\"\n      });\n      roleApi.menuCacheList().then(function (res) {\n        _this5.menuOptions = res;\n        _this5.checkDisabled(_this5.menuOptions);\n        loading.close();\n      });\n    },\n    getTreeId: function getTreeId(datas) {\n      for (var i in datas) {\n        if (datas[i].checked) this.menuIds.push(datas[i].id);\n        if (datas[i].childList) {\n          this.getTreeId(datas[i].childList);\n        }\n      }\n    },\n    checkDisabled: function checkDisabled(data) {\n      var _this6 = this;\n      //设置公共权限默认勾选且不可操作\n      data.forEach(function (item) {\n        if (item.id === 280 || item.id === 294 || item.id === 344) {\n          item.disabled = true;\n          item.childList.forEach(function (item1) {\n            item1.disabled = true;\n            _this6.$nextTick(function () {\n              var node = _this6.$refs.menu.getNode(item1.id);\n              if (node.isLeaf) {\n                _this6.$refs.menu.setChecked(node, true);\n              }\n            });\n          });\n        }\n      });\n    }\n  }\n};", null]}