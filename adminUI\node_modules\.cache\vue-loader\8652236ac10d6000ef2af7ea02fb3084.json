{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\register.vue?vue&type=style&index=0&id=2937ad88&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\register.vue", "mtime": 1754050582487}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\css-loader\\index.js", "mtime": 1754554385075}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754554389499}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754554387093}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1754554384522}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.el-button+.el-button{\n  margin-left: 0px !important;\n}\n.title{\n  text-align: center;\n}\n.captcha{\n  display: flex;\n  align-items: flex-start;\n  ::v-deep.el-form-item__content{\n    width: 100%;\n  }\n}\n$bg: #2d3a4b;\n$dark_gray: #889aa4;\n$light_gray: #eee;\n.imgs{\n  img{\n    height: 36px;\n  }\n}\n.login-form {\n  flex: 1;\n  padding: 32px 0;\n  text-align: center;\n  width: 384px;\n  margin: 0 auto;\n  overflow: hidden;\n}\n.tips {\n  font-size: 14px;\n  color: #fff;\n  margin-bottom: 10px;\n\n  span {\n    &:first-of-type {\n      margin-right: 16px;\n    }\n  }\n}\n.svg-container {\n  padding: 6px 5px 6px 15px;\n  color: $dark_gray;\n  vertical-align: middle;\n  width: 30px;\n  display: inline-block;\n}\n.show-pwd {\n  position: absolute;\n  right: 10px;\n  top: 7px;\n  font-size: 16px;\n  color: $dark_gray;\n  cursor: pointer;\n  user-select: none;\n  ::v-deep.svg-icon {\n    vertical-align: 0.3em;\n  }\n}\n.thirdparty-button {\n  position: absolute;\n  right: 0;\n  bottom: 6px;\n}\n", null]}