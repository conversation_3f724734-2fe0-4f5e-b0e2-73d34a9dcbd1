{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillList\\creatSeckill.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillList\\creatSeckill.vue", "mtime": 1754050582454}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _index = _interopRequireDefault(require(\"@/components/Tinymce/index\"));\nvar _store = require(\"@/api/store\");\nvar _logistics = require(\"@/api/logistics\");\nvar _public = require(\"@/libs/public\");\nvar _marketing = require(\"@/api/marketing\");\nvar _creatTemplates = _interopRequireDefault(require(\"@/views/systemSetting/logistics/shippingTemplates/creatTemplates\"));\nvar _validate = require(\"@/utils/validate\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; } //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar defaultObj = {\n  image: '',\n  images: '',\n  imagess: [],\n  title: '',\n  info: '',\n  num: 1,\n  unitName: '',\n  sort: 0,\n  giveIntegral: 0,\n  ficti: 0,\n  isShow: false,\n  tempId: '',\n  attrValue: [{\n    image: '',\n    price: 0,\n    cost: 0,\n    otPrice: 0,\n    stock: 0,\n    quota: 1,\n    barCode: '',\n    weight: 0,\n    volume: 0\n  }],\n  attr: [],\n  selectRule: '',\n  content: '',\n  specType: false,\n  id: 0,\n  timeId: 1,\n  startTime: '',\n  stopTime: '',\n  timeVal: [],\n  status: 0\n};\nvar objTitle = {\n  price: {\n    title: '秒杀价'\n  },\n  cost: {\n    title: '成本价'\n  },\n  otPrice: {\n    title: '原价'\n  },\n  stock: {\n    title: '库存'\n  },\n  quota: {\n    title: \"限量\"\n  },\n  barCode: {\n    title: '商品编号'\n  },\n  weight: {\n    title: '重量（KG）'\n  },\n  volume: {\n    title: '体积(m³)'\n  }\n};\nvar _default = exports.default = {\n  name: \"creatSeckill\",\n  components: {\n    CreatTemplates: _creatTemplates.default,\n    Tinymce: _index.default\n  },\n  data: function data() {\n    return {\n      pickerOptions: {\n        disabledDate: function disabledDate(time) {\n          return time.getTime() < new Date().setTime(new Date().getTime() - 3600 * 1000 * 24);\n        }\n      },\n      props2: {\n        children: 'child',\n        label: 'name',\n        value: 'id',\n        multiple: true,\n        emitPath: false\n      },\n      grid2: {\n        xl: 8,\n        lg: 10,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      currentTab: 0,\n      formThead: Object.assign({}, objTitle),\n      formValidate: Object.assign({}, defaultObj),\n      loading: false,\n      fullscreenLoading: false,\n      merCateList: [],\n      // 商户分类筛选\n      shippingList: [],\n      // 运费模板\n      seckillTime: [],\n      ruleValidate: {\n        productId: [{\n          required: true,\n          message: '请选择商品',\n          trigger: 'change'\n        }],\n        title: [{\n          required: true,\n          message: '请输入商品标题',\n          trigger: 'blur'\n        }],\n        attrValue: [{\n          required: true,\n          message: '请选择商品属相',\n          trigger: 'change',\n          type: 'array',\n          min: '1'\n        }],\n        num: [{\n          required: true,\n          message: '请输入活动次数',\n          trigger: 'blur'\n        }],\n        unitName: [{\n          required: true,\n          message: '请输入单位',\n          trigger: 'blur'\n        }],\n        info: [{\n          required: true,\n          message: '请输入秒杀商品简介',\n          trigger: 'blur'\n        }],\n        tempId: [{\n          required: true,\n          message: '请选择运费模板',\n          trigger: 'change'\n        }],\n        timeId: [{\n          required: true,\n          message: '请选择活动时间',\n          trigger: 'change'\n        }],\n        image: [{\n          required: true,\n          message: '请上传商品图',\n          trigger: 'change'\n        }],\n        imagess: [{\n          required: true,\n          message: '请上传商品轮播图',\n          type: 'array',\n          trigger: 'change'\n        }],\n        specType: [{\n          required: true,\n          message: '请选择商品规格',\n          trigger: 'change'\n        }],\n        timeVal: [{\n          required: true,\n          message: '请选择活动日期',\n          trigger: 'change',\n          type: 'array'\n        }]\n      },\n      manyTabDate: {},\n      manyTabTit: {},\n      attrInfo: {},\n      tempRoute: {},\n      multipleSelection: [],\n      productId: 0,\n      OneattrValue: [Object.assign({}, defaultObj.attrValue[0])],\n      // 单规格\n      ManyAttrValue: [Object.assign({}, defaultObj.attrValue[0])] // 多规格\n    };\n  },\n  computed: {\n    attrValue: function attrValue() {\n      var obj = Object.assign({}, defaultObj.attrValue[0]);\n      delete obj.image;\n      return obj;\n    }\n  },\n  created: function created() {\n    this.$watch('formValidate.attr', this.watCh);\n    this.tempRoute = Object.assign({}, this.$route);\n  },\n  mounted: function mounted() {\n    var _this2 = this;\n    (0, _public.getSeckillList)(1).then(function (res) {\n      _this2.seckillTime = res.list;\n    });\n    this.formValidate.imagess = [];\n    if (this.$route.params.id) {\n      this.setTagsViewTitle();\n      this.getInfo();\n      this.currentTab = 1;\n    }\n    this.getShippingList();\n    this.getCategorySelect();\n  },\n  methods: {\n    watCh: function watCh(val) {\n      var tmp = {};\n      var tmpTab = {};\n      this.formValidate.attr.forEach(function (o, i) {\n        // tmp['value' + i] = { title: o.attrName }\n        // tmpTab['value' + i] = ''\n        tmp[o.attrName] = {\n          title: o.attrName\n        };\n        tmpTab[o.attrName] = '';\n      });\n      this.manyTabTit = tmp;\n      this.manyTabDate = tmpTab;\n      this.formThead = Object.assign({}, this.formThead, tmp);\n    },\n    handleRemove: function handleRemove(i) {\n      this.formValidate.imagess.splice(i, 1);\n    },\n    handleSelectionChange: function handleSelectionChange(val) {\n      this.multipleSelection = val;\n    },\n    // 点击商品图\n    modalPicTap: function modalPicTap(tit, num, i) {\n      var _this = this;\n      this.$modalUpload(function (img) {\n        if (tit === '1' && !num) {\n          _this.formValidate.image = img[0].sattDir;\n          _this.ManyAttrValue[0].image = img[0].sattDir;\n        }\n        if (tit === '2' && !num) {\n          if (img.length > 10) return this.$message.warning(\"最多选择10张图片！\");\n          if (img.length + _this.formValidate.imagess.length > 10) return this.$message.warning(\"最多选择10张图片！\");\n          img.map(function (item) {\n            _this.formValidate.imagess.push(item.sattDir);\n          });\n        }\n        if (tit === '1' && num === 'duo') {\n          _this.ManyAttrValue[i].image = img[0].sattDir;\n        }\n      }, tit, 'content');\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.formValidate.timeVal = e;\n      this.formValidate.startTime = e ? e[0] : \"\";\n      this.formValidate.stopTime = e ? e[1] : \"\";\n    },\n    changeGood: function changeGood() {\n      var _this = this;\n      this.$modalGoodList(function (row) {\n        _this.formValidate.image = row.image;\n        _this.productId = row.id;\n        // _this.formValidate.productId = row.id\n      });\n    },\n    handleSubmitNest1: function handleSubmitNest1() {\n      if (!this.formValidate.image) {\n        return this.$message.warning(\"请选择商品！\");\n      } else {\n        this.currentTab++;\n        if (!this.$route.params.id) this.getProdect(this.productId);\n      }\n    },\n    // 商品分类；\n    getCategorySelect: function getCategorySelect() {\n      var _this3 = this;\n      (0, _store.categoryApi)({\n        status: -1,\n        type: 1\n      }).then(function (res) {\n        _this3.merCateList = _this3.filerMerCateList(res);\n      });\n    },\n    filerMerCateList: function filerMerCateList(treeData) {\n      return treeData.map(function (item) {\n        if (!item.child) {\n          item.disabled = true;\n        }\n        item.label = item.name;\n        return item;\n      });\n    },\n    // 运费模板；\n    getShippingList: function getShippingList() {\n      var _this4 = this;\n      (0, _logistics.shippingTemplatesList)(this.tempData).then(function (res) {\n        _this4.shippingList = res.list;\n      });\n    },\n    // 运费模板\n    addTem: function addTem() {\n      this.$refs.addTemplates.dialogVisible = true;\n      this.$refs.addTemplates.getCityList();\n    },\n    // 商品详情\n    getInfo: function getInfo() {\n      if (!this.$route.params.id) {\n        this.getProdect(this.productId);\n      } else {\n        this.getSekllProdect(this.$route.params.id);\n      }\n    },\n    getProdect: function getProdect(id) {\n      var _this5 = this;\n      this.fullscreenLoading = true;\n      (0, _store.productDetailApi)(id).then(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(res) {\n          var info;\n          return _regenerator().w(function (_context) {\n            while (1) switch (_context.n) {\n              case 0:\n                info = res;\n                _this5.formValidate = {\n                  image: _this5.$selfUtil.setDomain(info.image),\n                  imagess: JSON.parse(info.sliderImage),\n                  title: info.storeName,\n                  info: info.storeInfo,\n                  quota: '',\n                  unitName: info.unitName,\n                  sort: info.sort,\n                  isShow: info.isShow,\n                  tempId: info.tempId,\n                  attr: info.attr,\n                  attrValue: info.attrValue,\n                  selectRule: info.selectRule,\n                  content: info.content,\n                  specType: info.specType,\n                  productId: info.id,\n                  giveIntegral: info.giveIntegral,\n                  ficti: info.ficti,\n                  timeId: _this5.$route.params.id ? Number(info.timeId) : _this5.$route.params.timeId ? Number(_this5.$route.params.timeId) : '',\n                  startTime: info.startTime || '',\n                  stopTime: info.stopTime || '',\n                  timeVal: [],\n                  status: 0,\n                  num: 1\n                };\n                if (info.specType) {\n                  _this5.$nextTick(function () {\n                    info.attrValue.forEach(function (row) {\n                      row.quota = row.stock;\n                      row.attrValue = JSON.parse(row.attrValue);\n                      for (var attrValueKey in row.attrValue) {\n                        row[attrValueKey] = row.attrValue[attrValueKey];\n                      }\n                      row.image = _this5.$selfUtil.setDomain(row.image);\n                      _this5.$refs.multipleTable.toggleRowSelection(row, true);\n                      // this.$set(row, 'checked', true)\n                    });\n                  });\n                  _this5.ManyAttrValue = info.attrValue;\n                  _this5.multipleSelection = info.attrValue;\n                } else {\n                  info.attrValue.forEach(function (row) {\n                    row.quota = row.stock;\n                  });\n                  _this5.ManyAttrValue = info.attrValue;\n                  // this.formValidate.attr = []\n                }\n                _this5.fullscreenLoading = false;\n              case 1:\n                return _context.a(2);\n            }\n          }, _callee);\n        }));\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this5.fullscreenLoading = false;\n      });\n    },\n    getSekllProdect: function getSekllProdect(id) {\n      var _this6 = this;\n      this.fullscreenLoading = true;\n      (0, _marketing.seckillStoreInfoApi)({\n        id: id\n      }).then(/*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(res) {\n          var info;\n          return _regenerator().w(function (_context2) {\n            while (1) switch (_context2.n) {\n              case 0:\n                info = res;\n                _this6.formValidate = {\n                  image: _this6.$selfUtil.setDomain(info.image),\n                  imagess: JSON.parse(info.sliderImage),\n                  // title: info.title,\n                  title: info.storeName,\n                  // info: info.info,\n                  info: info.storeInfo,\n                  quota: info.quota,\n                  unitName: info.unitName,\n                  sort: info.sort,\n                  isShow: info.isShow,\n                  tempId: info.tempId,\n                  attr: info.attr,\n                  attrValue: info.attrValue,\n                  selectRule: info.selectRule,\n                  content: info.content,\n                  specType: info.specType,\n                  productId: info.productId,\n                  giveIntegral: info.giveIntegral,\n                  ficti: info.ficti,\n                  timeId: Number(info.timeId),\n                  // startTime: info.startTime || '',\n                  startTime: info.startTimeStr || '',\n                  // stopTime: info.stopTime || '',\n                  stopTime: info.stopTimeStr || '',\n                  status: info.status,\n                  num: info.num,\n                  timeVal: info.startTimeStr && info.stopTimeStr ? [info.startTimeStr, info.stopTimeStr] : [],\n                  id: info.id\n                };\n                if (info.specType) {\n                  _this6.ManyAttrValue = info.attrValue;\n                  _this6.$nextTick(function () {\n                    _this6.ManyAttrValue.forEach(function (item, index) {\n                      item.attrValue = JSON.parse(item.attrValue);\n                      for (var attrValueKey in item.attrValue) {\n                        item[attrValueKey] = item.attrValue[attrValueKey];\n                      }\n                      item.image = _this6.$selfUtil.setDomain(item.image);\n                      if (item.id) {\n                        // 设置自动选中逻辑，只要id存在\n                        _this6.$set(item, 'price', item.price);\n                        _this6.$set(item, 'quota', item.quota);\n                        _this6.$nextTick(function () {\n                          _this6.$refs.multipleTable.toggleRowSelection(item, true);\n                        });\n                      }\n                    });\n                  });\n                } else {\n                  _this6.ManyAttrValue = info.attrValue;\n                  // this.formValidate.attr = []\n                }\n                _this6.fullscreenLoading = false;\n              case 1:\n                return _context2.a(2);\n            }\n          }, _callee2);\n        }));\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this6.fullscreenLoading = false;\n      });\n    },\n    handleSubmitNest2: function handleSubmitNest2(name) {\n      var _this7 = this;\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          if (_this7.formValidate.specType && _this7.multipleSelection.length === 0) return _this7.$message.warning(\"请填选择至少一个商品属性！\");\n          _this7.currentTab++;\n        } else {\n          return false;\n        }\n      });\n    },\n    // 提交\n    handleSubmit: (0, _validate.Debounce)(function (name) {\n      var _this8 = this;\n      if (!this.formValidate.specType) {\n        // this.formValidate.attr = []\n        this.formValidate.attrValue = this.ManyAttrValue;\n      } else {\n        // this.multipleSelection.forEach((row) => {\n        //   this.$set(row, 'checked', true)\n        // });\n        this.formValidate.attrValue = this.multipleSelection;\n      }\n      this.formValidate.images = JSON.stringify(this.formValidate.imagess);\n      this.formValidate.attrValue.forEach(function (item) {\n        item.attrValue = JSON.stringify(item.attrValue);\n      });\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          _this8.fullscreenLoading = true;\n          _this8.loading = true;\n          _this8.$route.params.id ? (0, _marketing.seckillStoreUpdateApi)({\n            id: _this8.$route.params.id\n          }, _this8.formValidate).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {\n            return _regenerator().w(function (_context3) {\n              while (1) switch (_context3.n) {\n                case 0:\n                  _this8.fullscreenLoading = false;\n                  _this8.$message.success('编辑成功');\n                  _this8.$router.push({\n                    path: \"/marketing/seckill/list\"\n                  });\n                  _this8.$refs[name].resetFields();\n                  _this8.formValidate.images = [];\n                  _this8.loading = false;\n                case 1:\n                  return _context3.a(2);\n              }\n            }, _callee3);\n          }))).catch(function () {\n            _this8.fullscreenLoading = false;\n            _this8.loading = false;\n          }) : (0, _marketing.seckillStoreSaveApi)(_this8.formValidate).then(/*#__PURE__*/function () {\n            var _ref4 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4(res) {\n              return _regenerator().w(function (_context4) {\n                while (1) switch (_context4.n) {\n                  case 0:\n                    _this8.fullscreenLoading = false;\n                    _this8.$message.success('新增成功');\n                    _this8.$router.push({\n                      path: \"/marketing/seckill/list\"\n                    });\n                    _this8.$refs[name].resetFields();\n                    _this8.formValidate.images = [];\n                    _this8.loading = false;\n                  case 1:\n                    return _context4.a(2);\n                }\n              }, _callee4);\n            }));\n            return function (_x3) {\n              return _ref4.apply(this, arguments);\n            };\n          }()).catch(function () {\n            _this8.fullscreenLoading = false;\n            _this8.loading = false;\n          });\n        } else {\n          if (!_this8.formValidate.storeName || !_this8.formValidate.unitName || !_this8.formValidate.store_info || !_this8.formValidate.image || !_this8.formValidate.images) {\n            _this8.$message.warning(\"请填写完整商品信息！\");\n          }\n        }\n      });\n    }),\n    handleSubmitUp: function handleSubmitUp() {\n      if (this.currentTab-- < 0) this.currentTab = 0;\n    },\n    setTagsViewTitle: function setTagsViewTitle() {\n      var title = '编辑秒杀商品';\n      var route = Object.assign({}, this.tempRoute, {\n        title: \"\".concat(title, \"-\").concat(this.$route.params.id)\n      });\n      this.$store.dispatch('tagsView/updateVisitedView', route);\n    },\n    // 移动\n    handleDragStart: function handleDragStart(e, item) {\n      this.dragging = item;\n    },\n    handleDragEnd: function handleDragEnd(e, item) {\n      this.dragging = null;\n    },\n    handleDragOver: function handleDragOver(e) {\n      e.dataTransfer.dropEffect = 'move';\n    },\n    handleDragEnter: function handleDragEnter(e, item) {\n      e.dataTransfer.effectAllowed = 'move';\n      if (item === this.dragging) {\n        return;\n      }\n      var newItems = _toConsumableArray(this.formValidate.imagess);\n      var src = newItems.indexOf(this.dragging);\n      var dst = newItems.indexOf(item);\n      newItems.splice.apply(newItems, [dst, 0].concat(_toConsumableArray(newItems.splice(src, 1))));\n      this.formValidate.imagess = newItems;\n    }\n  }\n};", null]}