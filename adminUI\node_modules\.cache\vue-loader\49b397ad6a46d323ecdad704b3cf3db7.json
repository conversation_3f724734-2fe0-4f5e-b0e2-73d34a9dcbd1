{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\forgetPassword.vue?vue&type=style&index=0&id=b2b22b66&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\forgetPassword.vue", "mtime": 1754050582485}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\css-loader\\index.js", "mtime": 1754554385075}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754554389499}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754554387093}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1754554384522}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.login-form {\n  flex: 1;\n  padding: 32px 0;\n  text-align: center;\n  width: 384px;\n  margin: 0 auto;\n  overflow: hidden;\n}\n.width100{\n  width: 100%;\n}\n\n  .maxInpt{\n      max-width:400px;\n      margin-left:auto;\n      margin-right:auto;\n  }\n  .code {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n  }\n  .ivu-steps-item:last-child{\n      width: unset!important;\n  }\n", null]}