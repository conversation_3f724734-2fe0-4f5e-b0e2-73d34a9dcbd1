{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\Logo.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\Logo.vue", "mtime": 1754273069867}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport * as systemConfigApi from '@/api/systemConfig.js'\nexport default {\n  name: '<PERSON>bar<PERSON><PERSON>',\n  props: {\n    collapse: {\n      type: Boolean,\n      required: true\n    }\n  },\n  data() {\n    return {\n      title: 'Vue Element Admin',\n      logo: '',\n      logobg: require('@/assets/imgs/minilogo.png'),\n      logoSmall: ''\n    }\n  },\n  mounted() {\n    this.getLogo()\n    this.getSquareLogo()\n  },\n  methods: {\n    getLogo() {\n      systemConfigApi.configGetUniq({key: \"site_logo_lefttop\"}).then(data => {\n        this.logo = data\n      })\n    },\n    getSquareLogo() {\n      systemConfigApi.configGetUniq({key: \"site_logo_square\"}).then(data => {\n        this.logoSmall = data\n      })\n    }\n  }\n}\n", null]}