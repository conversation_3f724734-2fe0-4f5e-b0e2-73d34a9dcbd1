{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\edit.vue", "mtime": 1754050582501}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport * as roleApi from \"@/api/role.js\";\r\nimport { Debounce } from \"@/utils/validate\";\r\nexport default {\r\n  name: \"roleEdit\",\r\n  props: {\r\n    isCreate: {\r\n      type: Number,\r\n      required: true\r\n    },\r\n    editData: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      pram: {\r\n        roleName: null,\r\n        rules: \"\",\r\n        status: null,\r\n        id: null\r\n      },\r\n      menuExpand: false,\r\n      menuNodeAll: false,\r\n      menuOptions: [],\r\n      menuCheckStrictly: true,\r\n      currentNodeId: [],\r\n      defaultProps: {\r\n        children: \"childList\",\r\n        label: \"name\"\r\n      },\r\n      menuIds: []\r\n    };\r\n  },\r\n  mounted() {\r\n    this.initEditData();\r\n    this.getCacheMenu();\r\n  },\r\n  methods: {\r\n    close() {\r\n      this.$emit(\"hideEditDialog\");\r\n    },\r\n    initEditData() {\r\n      if (this.isCreate !== 1) return;\r\n      const { roleName, status, id } = this.editData;\r\n      this.pram.roleName = roleName;\r\n      this.pram.status = status;\r\n      this.pram.id = id;\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\"\r\n      });\r\n      roleApi.getInfo(id).then(res => {\r\n        this.menuOptions = res.menuList;\r\n        this.checkDisabled(this.menuOptions);\r\n        loading.close();\r\n        this.getTreeId(res.menuList);\r\n        this.$nextTick(() => {\r\n          this.menuIds.forEach((i, n) => {\r\n            var node = this.$refs.menu.getNode(i);\r\n            if (node.isLeaf) {\r\n              this.$refs.menu.setChecked(node, true);\r\n            }\r\n          });\r\n        });\r\n      });\r\n    },\r\n    handlerSubmit: Debounce(function(form) {\r\n      this.$refs[form].validate(valid => {\r\n        if (!valid) return;\r\n        let roles = this.getMenuAllCheckedKeys().toString();\r\n        this.pram.rules = roles;\r\n        if (this.isCreate === 0) {\r\n          this.handlerSave();\r\n        } else {\r\n          this.handlerEdit();\r\n        }\r\n      });\r\n    }),\r\n    handlerSave() {\r\n      roleApi.addRole(this.pram).then(data => {\r\n        this.$message.success(\r\n          this.$t(\"admin.system.role.createIdentity\") +\r\n            this.$t(\"common.operationSuccess\")\r\n        );\r\n        this.$emit(\"hideEditDialog\");\r\n      });\r\n    },\r\n    handlerEdit() {\r\n      roleApi.updateRole(this.pram).then(data => {\r\n        this.$message.success(\r\n          this.$t(\"admin.system.role.editIdentity\") +\r\n            this.$t(\"common.operationSuccess\")\r\n        );\r\n        this.$emit(\"hideEditDialog\");\r\n      });\r\n    },\r\n    rulesSelect(selectKeys) {\r\n      this.pram.rules = selectKeys;\r\n    },\r\n    // 树权限（展开/折叠）\r\n    handleCheckedTreeExpand(value, type) {\r\n      if (type == \"menu\") {\r\n        let treeList = this.menuOptions;\r\n        for (let i = 0; i < treeList.length; i++) {\r\n          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;\r\n        }\r\n      }\r\n    },\r\n    // 树权限（全选/全不选）\r\n    handleCheckedTreeNodeAll(value, type) {\r\n      if (type == \"menu\") {\r\n        this.$refs.menu.setCheckedNodes(value ? this.menuOptions : []);\r\n      }\r\n    },\r\n    // 树权限（父子联动）\r\n    handleCheckedTreeConnect(value, type) {\r\n      if (type == \"menu\") {\r\n        this.menuCheckStrictly = value ? true : false;\r\n      }\r\n    },\r\n    // 所有菜单节点数据\r\n    getMenuAllCheckedKeys() {\r\n      // 目前被选中的菜单节点\r\n      let checkedKeys = this.$refs.menu.getCheckedKeys();\r\n      // 半选中的菜单节点\r\n      let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();\r\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);\r\n      return checkedKeys;\r\n    },\r\n    getCacheMenu() {\r\n      if (this.isCreate !== 0) return;\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\"\r\n      });\r\n      roleApi.menuCacheList().then(res => {\r\n        this.menuOptions = res;\r\n        this.checkDisabled(this.menuOptions);\r\n        loading.close();\r\n      });\r\n    },\r\n    getTreeId(datas) {\r\n      for (var i in datas) {\r\n        if (datas[i].checked) this.menuIds.push(datas[i].id);\r\n        if (datas[i].childList) {\r\n          this.getTreeId(datas[i].childList);\r\n        }\r\n      }\r\n    },\r\n    checkDisabled(data) {\r\n      //设置公共权限默认勾选且不可操作\r\n      data.forEach(item => {\r\n        if (item.id === 280 || item.id === 294 || item.id === 344) {\r\n          item.disabled = true;\r\n          item.childList.forEach(item1 => {\r\n            item1.disabled = true;\r\n            this.$nextTick(() => {\r\n              var node = this.$refs.menu.getNode(item1.id);\r\n              if (node.isLeaf) {\r\n                this.$refs.menu.setChecked(node, true);\r\n              }\r\n            });\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n", null]}