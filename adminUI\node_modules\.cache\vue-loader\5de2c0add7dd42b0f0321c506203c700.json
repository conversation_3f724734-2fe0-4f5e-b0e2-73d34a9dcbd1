{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\index.vue?vue&type=template&id=0a5e70de&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\index.vue", "mtime": 1754050582488}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card v-if=\"isShowList\" v-loading=\"fullscreenLoading\" class=\"box-card mb20\">\n    <div class=\"content acea-row row-middle\">\n      <div class=\"demo-basic--circle acea-row row-middle\">\n        <div class=\"circleUrl mr20\"><img :src=\"circleUrl\"></div>\n        <!--<el-avatar :size=\"50\" :src=\"circleUrl\" class=\"mr20\" />-->\n        <div class=\"dashboard-workplace-header-tip\">\n          <div class=\"dashboard-workplace-header-tip-title\">{{ smsAccount }}，祝您每一天开心！</div>\n          <div class=\"dashboard-workplace-header-tip-desc\">\n            <span class=\"mr10\" @click=\"onChangePassswordIndex\" v-if=\"checkPermi(['admin:pass:update:password'])\">修改密码</span>\n            <span class=\"mr10\" @click=\"onChangePhone\" v-if=\"checkPermi(['admin:pass:update:phone'])\">修改手机号</span>\n            <span @click=\"signOut\" class=\"mr10\" v-if=\"checkPermi(['admin:pass:logout'])\">退出登录</span>\n            <!-- <el-tooltip class=\"item\" effect=\"dark\" content=\"\n            一号通为我司一个第三方平台\n            专门提供短信 ， 物流查询，商品复制，电子面单等个性化服务\n            省去了自己单独接入功能的麻烦\n            初次运行代码默认是没有账号的，需要自行注册，\n            登录成功后根据提示购买自己需要用到的服务即可\" placement=\"right\">\n              <span class=\"mr10\">平台说明</span>\n            </el-tooltip> -->\n            <template>\n              <el-popover trigger=\"hover\" placement=\"right\" >\n                <span class=\"mr10\" slot=\"reference\">平台说明</span>\n                <div class=\"pup_card\">\n                  一号通为我司一个第三方平台专门提供短信 ， 物流查询，商品复制，电子面单等个性化服务省去了自己单独接入功能的麻烦初次运行代码默认是没有账号的，需要自行注册，\n                  登录成功后根据提示购买自己需要用到的服务即可\n                </div>\n              </el-popover>\n            </template>\n          </div>\n        </div>\n      </div>\n      <div class=\"dashboard\">\n        <div class=\"dashboard-workplace-header-extra\">\n          <div class=\"acea-row\">\n            <div class=\"header-extra\">\n              <p class=\"mb5\"><span>短信条数</span></p>\n              <p class=\"mb5\">{{sms.num || 0}}</p>\n              <el-button size=\"mini\" type=\"primary\" @click=\"sms.open ===0?onOpen('sms'):mealPay('sms')\" v-text=\"sms.open ===0?'开通服务':'套餐购买'\" v-hasPermi=\"['admin:pass:meal:code', 'admin:pass:service:open']\"></el-button>\n            </div>\n            <div class=\"header-extra\">\n              <p class=\"mb5\"><span>采集次数</span></p>\n              <p class=\"mb5\">{{copy.num || 0}}</p>\n              <el-button size=\"mini\" type=\"primary\" @click=\"copy.open ===0?onOpen('copy'):mealPay('copy')\" v-text=\"copy.open ===0?'开通服务':'套餐购买'\" v-hasPermi=\"['admin:pass:meal:code', 'admin:pass:service:open']\"></el-button>\n            </div>\n            <div class=\"header-extra\">\n              <p class=\"mb5\"><span>物流查询次数</span></p>\n              <p class=\"mb5\">{{query.num || 0}}</p>\n              <el-button size=\"mini\" type=\"primary\" @click=\"query.open ===0?onOpen('expr_query'):mealPay('expr_query')\" v-text=\"query.open ===0?'开通服务':'套餐购买'\" v-hasPermi=\"['admin:pass:meal:code', 'admin:pass:service:open']\"></el-button>\n            </div>\n            <div class=\"header-extra\" style=\"border: none;\">\n              <p class=\"mb5\"><span>面单打印次数</span> </p>\n              <p class=\"mb5\">{{dump.num || 0}}</p>\n              <el-button size=\"mini\" type=\"primary\" @click=\"dump.open ===0?onOpen('expr_dump'):mealPay('expr_dump')\" v-text=\"dump.open ===0?'开通服务':'套餐购买'\" v-hasPermi=\"['admin:pass:meal:code', 'admin:pass:service:open']\"></el-button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </el-card>\n  <el-card class=\"box-card\" v-loading=\"loading\">\n    <table-list v-if=\"isShowList\"  ref=\"tableLists\"  :sms=\"sms\" :copy=\"copy\" :dump=\"dump\" :query=\"query\" :accountInfo=\"accountInfo\" @openService=\"openService\"/>\n    <login-from v-if=\"isShowLogn\" @on-change=\"onChangePasssword\" @on-changes=\"onChangeReg\" @on-Login=\"onLogin\" />\n    <forget-password :infoData=\"infoData\" v-if=\"isShow\" @goback=\"goback\" @on-Login=\"onLogin\" :isIndex=\"isIndex\"></forget-password>\n    <forget-phone v-if=\"isForgetPhone\" @gobackPhone=\"gobackPhone\" @on-Login=\"onLogin\"></forget-phone>\n    <register-from v-if=\"isShowReg\" @on-change=\"logoup\" />\n  </el-card>\n\n</div>\n", null]}