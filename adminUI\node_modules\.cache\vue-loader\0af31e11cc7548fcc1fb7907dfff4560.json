{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\setting\\index.vue?vue&type=template&id=26c5d13b&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\setting\\index.vue", "mtime": 1754050582515}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <el-tabs v-model=\"activeNamel1\" @tab-click=\"handleTabClick\"  v-loading=\"loading\" v-if=\"checkPermi(['admin:system:config:info'])\">\n      <el-tab-pane\n        v-for=\"tab,index in treeList\"\n        :key=\"index\"\n        :label=\"tab.name\"\n        :name=\"tab.id.toString()\">\n        <template>\n          <el-tabs v-if=\"tab.child.length > 0\" v-model=\"activeNamel2\"\n                   type=\"border-card\" @tab-click=\"handleItemTabClick\">\n            <el-tab-pane\n              v-for=\"tabItem,itemIndex in tab.child\"\n              :key=\"itemIndex\"\n              :label=\"tabItem.name\"\n              :name=\"tabItem.extra\"\n            >\n              <parser\n                v-if=\"formConfChild.render\"\n                :is-edit=\"formConfChild.isEdit\"\n                :form-conf=\"formConfChild.content\"\n                :form-edit-data=\"currentEditData\"\n                @submit=\"handlerSubmit\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n          <span v-else>\n        <parser\n          v-if=\"formConf.render\"\n          :is-edit=\"formConf.isEdit\"\n          :form-conf=\"formConf.content\"\n          :form-edit-data=\"currentEditData\"\n          @submit=\"handlerSubmit\"\n        />\n      </span>\n        </template>\n      </el-tab-pane>\n    </el-tabs>\n  </el-card>\n</div>\n", null]}