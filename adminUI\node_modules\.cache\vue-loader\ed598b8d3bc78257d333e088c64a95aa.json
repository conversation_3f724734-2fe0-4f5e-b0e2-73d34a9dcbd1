{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\FormGenerator\\index\\ResourceDialog.vue?vue&type=template&id=651d5643&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\FormGenerator\\index\\ResourceDialog.vue", "mtime": 1754050582253}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    [\n      _c(\n        \"el-dialog\",\n        _vm._g(\n          _vm._b(\n            {\n              attrs: {\n                title: \"外部资源引用\",\n                width: \"600px\",\n                \"close-on-click-modal\": false,\n              },\n              on: { open: _vm.onOpen, close: _vm.onClose },\n            },\n            \"el-dialog\",\n            _vm.$attrs,\n            false\n          ),\n          _vm.$listeners\n        ),\n        [\n          _vm._l(_vm.resources, function (item, index) {\n            return _c(\n              \"el-input\",\n              {\n                key: index,\n                staticClass: \"url-item\",\n                attrs: {\n                  placeholder: \"请输入 css 或 js 资源路径\",\n                  \"prefix-icon\": \"el-icon-link\",\n                  clearable: \"\",\n                },\n                model: {\n                  value: _vm.resources[index],\n                  callback: function ($$v) {\n                    _vm.$set(_vm.resources, index, $$v)\n                  },\n                  expression: \"resources[index]\",\n                },\n              },\n              [\n                _c(\"el-button\", {\n                  attrs: { slot: \"append\", icon: \"el-icon-delete\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.deleteOne(index)\n                    },\n                  },\n                  slot: \"append\",\n                }),\n              ],\n              1\n            )\n          }),\n          _vm._v(\" \"),\n          _c(\n            \"el-button-group\",\n            { staticClass: \"add-item\" },\n            [\n              _c(\n                \"el-button\",\n                {\n                  attrs: { plain: \"\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.addOne(\n                        \"https://cdn.bootcss.com/jquery/1.8.3/jquery.min.js\"\n                      )\n                    },\n                  },\n                },\n                [_vm._v(\"\\n        jQuery1.8.3\\n      \")]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { plain: \"\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.addOne(\"https://unpkg.com/http-vue-loader\")\n                    },\n                  },\n                },\n                [_vm._v(\"\\n        http-vue-loader\\n      \")]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { icon: \"el-icon-circle-plus-outline\", plain: \"\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.addOne(\"\")\n                    },\n                  },\n                },\n                [_vm._v(\"\\n        添加其他\\n      \")]\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { attrs: { slot: \"footer\" }, slot: \"footer\" },\n            [\n              _c(\"el-button\", { on: { click: _vm.close } }, [\n                _vm._v(\"\\n        取消\\n      \"),\n              ]),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\" },\n                  on: { click: _vm.handelConfirm },\n                },\n                [_vm._v(\"\\n        确定\\n      \")]\n              ),\n            ],\n            1\n          ),\n        ],\n        2\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}