{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\Logo.vue", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\Logo.vue", "mtime": 1754273069867}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754554385233}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./Logo.vue?vue&type=template&id=6494804b&scoped=true\"\nimport script from \"./Logo.vue?vue&type=script&lang=js\"\nexport * from \"./Logo.vue?vue&type=script&lang=js\"\nimport style0 from \"./Logo.vue?vue&type=style&index=0&id=6494804b&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6494804b\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\shop\\\\adminUI\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('6494804b')) {\n      api.createRecord('6494804b', component.options)\n    } else {\n      api.reload('6494804b', component.options)\n    }\n    module.hot.accept(\"./Logo.vue?vue&type=template&id=6494804b&scoped=true\", function () {\n      api.rerender('6494804b', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/layout/components/Sidebar/Logo.vue\"\nexport default component.exports"]}