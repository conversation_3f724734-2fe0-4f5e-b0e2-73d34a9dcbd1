{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\permissionRules\\index.vue?vue&type=template&id=bac4044c", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\permissionRules\\index.vue", "mtime": 1754373878576}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <el-form\n      :model=\"queryParams\"\n      ref=\"queryForm\"\n      :inline=\"true\"\n      v-show=\"showSearch\"\n    >\n      <el-form-item :label=\"$t('permissionRules.menuName')\" prop=\"menuName\">\n        <el-input\n          v-model=\"queryParams.name\"\n          :placeholder=\"$t('permissionRules.form.enterMenuName')\"\n          clearable\n          size=\"small\"\n        />\n      </el-form-item>\n      <el-form-item :label=\"$t('permissionRules.status')\" prop=\"menuType\">\n        <el-select\n          v-model=\"queryParams.menuType\"\n          :placeholder=\"$t('permissionRules.select')\"\n          clearable\n          size=\"small\"\n        >\n          <el-option\n            v-for=\"item in statusOptions\"\n            :key=\"item.value\"\n            :label=\"item.label\"\n            :value=\"item.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item>\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          size=\"mini\"\n          @click=\"handleQuery\"\n          >{{ $t(\"common.query\") }}</el-button\n        >\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">{{\n          $t(\"common.reset\")\n        }}</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          >{{ $t(\"permissionRules.actions.add\") }}</el-button\n        >\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"info\"\n          plain\n          icon=\"el-icon-sort\"\n          size=\"mini\"\n          @click=\"toggleExpandAll\"\n          >{{ $t(\"permissionRules.expandCollapse\") }}</el-button\n        >\n      </el-col>\n    </el-row>\n\n    <el-table\n      v-if=\"refreshTable\"\n      v-loading=\"listLoading\"\n      :data=\"menuList\"\n      row-key=\"id\"\n      :default-expand-all=\"isExpandAll\"\n      :tree-props=\"{ children: 'children', hasChildren: 'hasChildren' }\"\n      :header-cell-style=\"{ fontWeight: 'bold' }\"\n    >\n      <el-table-column\n        :label=\"$t('permissionRules.table.menuName')\"\n        :show-overflow-tooltip=\"true\"\n        width=\"160\"\n      >\n        <template slot-scope=\"scope\">\n          {{\n            $t(\"dashboard.\" + scope.row.name)\n              ? $t(\"dashboard.\" + scope.row.name)\n              : scope.row.name\n          }}\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"icon\"\n        :label=\"$t('permissionRules.table.icon')\"\n        align=\"center\"\n        width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <i :class=\"'el-icon-' + scope.row.icon\" style=\"font-size: 20px;\" />\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"sort\"\n        :label=\"$t('permissionRules.table.sort')\"\n        width=\"60\"\n      ></el-table-column>\n      <el-table-column\n        prop=\"perms\"\n        :label=\"$t('permissionRules.table.perm')\"\n        :show-overflow-tooltip=\"true\"\n      ></el-table-column>\n      <el-table-column\n        prop=\"component\"\n        :label=\"$t('permissionRules.table.component')\"\n        :show-overflow-tooltip=\"true\"\n      ></el-table-column>\n      <el-table-column\n        prop=\"isShow\"\n        :label=\"$t('permissionRules.table.status')\"\n        width=\"80\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"scope.row.isShow ? '' : 'danger'\">{{\n            scope.row.isShow ? $t(\"common.show\") : $t(\"common.hide\")\n          }}</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column :label=\"$t('permissionRules.table.createTime')\" align=\"center\" prop=\"createTime\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column :label=\"$t('permissionRules.table.type')\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <span class=\"type_tag one\" v-if=\"scope.row.menuType == 'M'\">{{\n            $t(\"permissionRules.menuType.directory\")\n          }}</span>\n          <span class=\"type_tag two\" v-else-if=\"scope.row.menuType == 'C'\">{{\n            $t(\"permissionRules.menuType.menu\")\n          }}</span>\n          <span class=\"type_tag three\" v-else type=\"info\">{{\n            $t(\"permissionRules.menuType.button\")\n          }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('common.actions')\"\n        align=\"center\"\n        class-name=\"small-padding fixed-width\"\n      >\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['admin:system:menu:info']\"\n            >{{ $t(\"permissionRules.actions.edit\") }}</el-button\n          >\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-plus\"\n            @click=\"handleAdd(scope.row)\"\n            v-hasPermi=\"['admin:system:menu:add']\"\n            >{{ $t(\"permissionRules.actions.add\") }}</el-button\n          >\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['admin:system:menu:delete']\"\n            >{{ $t(\"permissionRules.actions.delete\") }}</el-button\n          >\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 添加或修改菜单对话框 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"open\"\n      width=\"680px\"\n      append-to-body\n    >\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item :label=\"$t('permissionRules.form.parentMenu')\">\n              <treeselect\n                v-model=\"form.pid\"\n                :options=\"menuOptions\"\n                :normalizer=\"normalizer\"\n                :show-count=\"true\"\n                :placeholder=\"$t('permissionRules.form.selectParentMenu')\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item\n              :label=\"$t('permissionRules.form.menuType')\"\n              prop=\"menuType\"\n            >\n              <el-radio-group v-model=\"form.menuType\">\n                <el-radio label=\"M\">{{\n                  $t(\"permissionRules.menuType.directory\")\n                }}</el-radio>\n                <el-radio label=\"C\">{{\n                  $t(\"permissionRules.menuType.menu\")\n                }}</el-radio>\n                <el-radio label=\"A\">{{\n                  $t(\"permissionRules.menuType.button\")\n                }}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item\n              v-if=\"form.menuType != 'A'\"\n              :label=\"$t('permissionRules.form.menuIcon')\"\n            >\n              <el-form-item>\n                <el-input\n                  :placeholder=\"$t('permissionRules.form.selectIcon')\"\n                  v-model=\"form.icon\"\n                >\n                  <el-button\n                    slot=\"append\"\n                    icon=\"el-icon-circle-plus-outline\"\n                    @click=\"addIcon\"\n                  ></el-button>\n                </el-input>\n              </el-form-item>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item\n              :label=\"$t('permissionRules.form.menuName')\"\n              prop=\"menuName\"\n            >\n              <el-input\n                v-model=\"form.name\"\n                :placeholder=\"$t('permissionRules.form.enterMenuName')\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item\n              :label=\"$t('permissionRules.form.sort')\"\n              prop=\"sort\"\n            >\n              <el-input-number\n                v-model=\"form.sort\"\n                controls-position=\"right\"\n                :min=\"0\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\" v-if=\"form.menuType !== 'A'\">\n            <el-form-item prop=\"component\">\n              <span slot=\"label\">\n                <el-tooltip\n                  :content=\"$t('permissionRules.form.componentTip')\"\n                  placement=\"top\"\n                >\n                  <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                {{ $t(\"permissionRules.form.component\") }}\n              </span>\n              <el-input\n                v-model=\"form.component\"\n                :placeholder=\"$t('permissionRules.form.enterComponent')\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item v-if=\"form.menuType != 'M'\">\n              <el-input\n                v-model=\"form.perms\"\n                :placeholder=\"$t('permissionRules.form.enterPerm')\"\n                maxlength=\"100\"\n              />\n              <span slot=\"label\">\n                <el-tooltip\n                  :content=\"$t('permissionRules.form.permTip')\"\n                  placement=\"top\"\n                >\n                  <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                {{ $t(\"permissionRules.form.perm\") }}\n              </span>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item>\n              <span slot=\"label\">\n                <el-tooltip\n                  :content=\"$t('permissionRules.form.showStatusTip')\"\n                  placement=\"top\"\n                >\n                  <i class=\"el-icon-question\"></i>\n                </el-tooltip>\n                {{ $t(\"permissionRules.form.showStatus\") }}\n              </span>\n              <el-radio-group v-model=\"form.isShow\">\n                <el-radio\n                  v-for=\"item in showStatus\"\n                  :key=\"item.value\"\n                  :label=\"item.value\"\n                  >{{ item.label }}</el-radio\n                >\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button\n          type=\"primary\"\n          @click=\"submitForm\"\n          v-hasPermi=\"['admin:system:menu:update']\"\n          >{{ $t(\"common.confirm\") }}</el-button\n        >\n        <el-button @click=\"cancel\">{{ $t(\"common.cancel\") }}</el-button>\n      </div>\n    </el-dialog>\n  </el-card>\n</div>\n", null]}