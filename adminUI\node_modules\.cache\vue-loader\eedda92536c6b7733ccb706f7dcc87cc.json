{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\forgetPassword.vue", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\forgetPassword.vue", "mtime": 1754050582485}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754554385233}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./forgetPassword.vue?vue&type=template&id=b2b22b66&scoped=true\"\nimport script from \"./forgetPassword.vue?vue&type=script&lang=js\"\nexport * from \"./forgetPassword.vue?vue&type=script&lang=js\"\nimport style0 from \"./forgetPassword.vue?vue&type=style&index=0&id=b2b22b66&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b2b22b66\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\shop\\\\adminUI\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('b2b22b66')) {\n      api.createRecord('b2b22b66', component.options)\n    } else {\n      api.reload('b2b22b66', component.options)\n    }\n    module.hot.accept(\"./forgetPassword.vue?vue&type=template&id=b2b22b66&scoped=true\", function () {\n      api.rerender('b2b22b66', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/sms/smsConfig/components/forgetPassword.vue\"\nexport default component.exports"]}