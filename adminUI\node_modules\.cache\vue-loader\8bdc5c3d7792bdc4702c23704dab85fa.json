{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\FormGenerator\\index\\ResourceDialog.vue?vue&type=template&id=651d5643&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\FormGenerator\\index\\ResourceDialog.vue", "mtime": 1754050582253}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div>\n  <el-dialog\n    v-bind=\"$attrs\"\n    title=\"外部资源引用\"\n    width=\"600px\"\n    :close-on-click-modal=\"false\"\n    v-on=\"$listeners\"\n    @open=\"onOpen\"\n    @close=\"onClose\"\n  >\n    <el-input\n      v-for=\"(item, index) in resources\"\n      :key=\"index\"\n      v-model=\"resources[index]\"\n      class=\"url-item\"\n      placeholder=\"请输入 css 或 js 资源路径\"\n      prefix-icon=\"el-icon-link\"\n      clearable\n    >\n      <el-button\n        slot=\"append\"\n        icon=\"el-icon-delete\"\n        @click=\"deleteOne(index)\"\n      />\n    </el-input>\n    <el-button-group class=\"add-item\">\n      <el-button\n        plain\n        @click=\"addOne('https://cdn.bootcss.com/jquery/1.8.3/jquery.min.js')\"\n      >\n        jQuery1.8.3\n      </el-button>\n      <el-button\n        plain\n        @click=\"addOne('https://unpkg.com/http-vue-loader')\"\n      >\n        http-vue-loader\n      </el-button>\n      <el-button\n        icon=\"el-icon-circle-plus-outline\"\n        plain\n        @click=\"addOne('')\"\n      >\n        添加其他\n      </el-button>\n    </el-button-group>\n    <div slot=\"footer\">\n      <el-button @click=\"close\">\n        取消\n      </el-button>\n      <el-button\n        type=\"primary\"\n        @click=\"handelConfirm\"\n      >\n        确定\n      </el-button>\n    </div>\n  </el-dialog>\n</div>\n", null]}