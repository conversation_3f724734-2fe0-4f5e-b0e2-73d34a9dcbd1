{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\adminList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\adminList\\index.vue", "mtime": 1754445731572}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport * as systemAdminApi from \"@/api/systemadmin.js\";\r\nimport * as roleApi from \"@/api/role.js\";\r\nimport edit from \"./edit\";\r\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\r\nexport default {\r\n  // name: \"index\"\r\n  components: { edit },\r\n  data() {\r\n    return {\r\n      constants: this.$constants,\r\n      listData: { list: [] },\r\n      listPram: {\r\n        account: null,\r\n        addTime: null,\r\n        lastIp: null,\r\n        lastTime: null,\r\n        level: null,\r\n        loginCount: null,\r\n        realName: null,\r\n        roles: null,\r\n        status: null,\r\n        page: 1,\r\n        limit: this.$constants.page.limit[0]\r\n      },\r\n      roleList: [],\r\n      menuList: [],\r\n      editDialogConfig: {\r\n        visible: false,\r\n        isCreate: 0, // 0=创建，1=编辑\r\n        editData: {}\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.handleGetAdminList();\r\n    this.handleGetRoleList();\r\n  },\r\n  methods: {\r\n    checkPermi,\r\n    onchangeIsShow(row) {\r\n      systemAdminApi\r\n        .updateStatusApi({ id: row.id, status: row.status })\r\n        .then(async () => {\r\n          this.$message.success(this.$t(\"common.operationSuccess\"));\r\n          this.handleGetAdminList();\r\n        })\r\n        .catch(() => {\r\n          row.status = !row.status;\r\n        });\r\n    },\r\n    onchangeIsSms(row) {\r\n      // this.$confirm(`此操作将${!row.isSms ? '开启' : '关闭'}验证, 是否继续？`, \"提示\", {\r\n      //   confirmButtonText: \"确定\",\r\n      //   cancelButtonText: \"取消\",\r\n      //   type: \"warning\"\r\n      // }).then(async () => {\r\n      //   row.isSms = !row.isSms\r\n      // }).catch(() => {\r\n      //   this.$message.error('取消操作')\r\n      // })\r\n\r\n      if (!row.phone)\r\n        return this.$message({\r\n          message: this.$t(\"admin.system.admin.pleaseAddPhone\"),\r\n          type: \"warning\"\r\n        });\r\n      systemAdminApi\r\n        .updateIsSmsApi({ id: row.id })\r\n        .then(async () => {\r\n          this.$message.success(this.$t(\"common.operationSuccess\"));\r\n          this.handleGetAdminList();\r\n        })\r\n        .catch(() => {\r\n          row.isSms = !row.isSms;\r\n        });\r\n    },\r\n    handleSearch() {\r\n      this.listPram.page = 1;\r\n      this.handleGetAdminList();\r\n    },\r\n    handleSizeChange(val) {\r\n      this.listPram.limit = val;\r\n      this.handleGetAdminList();\r\n      this.handleGetRoleList(this.listPram);\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.listPram.page = val;\r\n      this.handleGetAdminList();\r\n      this.handleGetRoleList(this.listPram);\r\n    },\r\n    handleGetRoleList() {\r\n      const _pram = {\r\n        page: 1,\r\n        limit: this.constants.page.limit[4]\r\n      };\r\n      roleApi.getRoleList(_pram).then(data => {\r\n        this.roleList = data;\r\n      });\r\n    },\r\n    handlerOpenDel(rowData) {\r\n      this.$confirm(this.$t(\"admin.system.admin.confirmDelete\")).then(() => {\r\n        const _pram = { id: rowData.id };\r\n        systemAdminApi.adminDel(_pram).then(data => {\r\n          this.$message.success(this.$t(\"common.prompt\"));\r\n          this.handleGetAdminList();\r\n        });\r\n      });\r\n    },\r\n    handleGetAdminList() {\r\n      systemAdminApi.adminList(this.listPram).then(data => {\r\n        this.listData = data;\r\n        // this.handlerGetMenuList()\r\n      });\r\n    },\r\n    handlerOpenEdit(isCreate, editDate) {\r\n      this.editDialogConfig.editData = editDate;\r\n      this.editDialogConfig.isCreate = isCreate;\r\n      this.editDialogConfig.visible = true;\r\n    },\r\n    handlerGetMenuList() {\r\n      // 获取菜单全部数据后做menu翻译使用\r\n      systemAdminApi\r\n        .listCategroy({ page: 1, limit: 999, type: 5 })\r\n        .then(data => {\r\n          this.menuList = data.list;\r\n          this.listData.list.forEach(item => {\r\n            const _muneText = [];\r\n            const menuids = item.rules.split(\",\");\r\n            menuids.map(muid => {\r\n              this.menuList.filter(menu => {\r\n                if (menu.id == muid) {\r\n                  _muneText.push(menu.name);\r\n                }\r\n              });\r\n            });\r\n            item.rulesView = _muneText.join(\",\");\r\n            this.$set(item, \"rulesViews\", item.rulesView);\r\n          });\r\n        });\r\n    },\r\n    hideEditDialog() {\r\n      this.editDialogConfig.visible = false;\r\n      this.handleGetAdminList();\r\n    }\r\n  }\r\n};\r\n", null]}