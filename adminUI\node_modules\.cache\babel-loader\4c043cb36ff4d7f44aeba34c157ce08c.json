{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\tableList.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\tableList.vue", "mtime": 1754050582488}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _sms = require(\"@/api/sms\");\nvar commFilter = _interopRequireWildcard(require(\"@/filters/commFilter\"));\nvar _index = _interopRequireDefault(require(\"../../../appSetting/wxAccount/wxTemplate/index\"));\nvar _permission = require(\"@/utils/permission\");\nvar _validate = require(\"@/utils/validate\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; } //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// 权限判断函数\nvar _default = exports.default = {\n  name: 'TableList',\n  props: {\n    copy: {\n      type: Object,\n      default: null\n    },\n    dump: {\n      type: Object,\n      default: null\n    },\n    query: {\n      type: Object,\n      default: null\n    },\n    sms: {\n      type: Object,\n      default: null\n    },\n    accountInfo: {\n      type: Object,\n      default: null\n    }\n  },\n  components: {\n    Template: _index.default\n  },\n  data: function data() {\n    var validatePhone = function validatePhone(rule, value, callback) {\n      if (!value) {\n        return callback(new Error('请填写手机号'));\n      } else if (!/^1[3456789]\\d{9}$/.test(value)) {\n        callback(new Error('手机号格式不正确!'));\n      } else {\n        callback();\n      }\n    };\n    return {\n      dialogVisible: false,\n      listLoading: false,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        status: '3',\n        type: 'sms'\n      },\n      columns2: [],\n      isSms: false,\n      // 是否开通短信\n      isDump: false,\n      // 是否开通电子面单,是否开通物流查询\n      isCopy: false,\n      // 是否开通商品采集\n      modals: false,\n      loading: false,\n      formInlineDump: {\n        tempId: '',\n        sign: '',\n        com: '',\n        toName: '',\n        toTel: '',\n        siid: '',\n        toAddress: '',\n        type: ''\n      },\n      ruleInline: {\n        sign: [{\n          required: true,\n          message: '请输入短信签名',\n          trigger: 'blur'\n        }],\n        phone: [{\n          required: true,\n          validator: validatePhone,\n          trigger: 'blur'\n        }],\n        code: [{\n          required: true,\n          message: '请输入验证码',\n          trigger: 'blur'\n        }],\n        com: [{\n          required: true,\n          message: '请选择快递公司',\n          trigger: 'change'\n        }],\n        tempId: [{\n          required: true,\n          message: '请选择打印模板',\n          trigger: 'change'\n        }],\n        toName: [{\n          required: true,\n          message: '请输寄件人姓名',\n          trigger: 'blur'\n        }],\n        toTel: [{\n          required: true,\n          validator: validatePhone,\n          trigger: 'blur'\n        }],\n        siid: [{\n          required: true,\n          message: '请输入云打印机编号',\n          trigger: 'blur'\n        }],\n        toAddress: [{\n          required: true,\n          message: '请输寄件人地址',\n          trigger: 'blur'\n        }]\n      },\n      tempImg: '',\n      // 图片\n      exportTempList: [],\n      // 电子面单模板\n      exportList: [],\n      // 快递公司列表\n      formInline: {\n        phone: '',\n        code: '',\n        sign: ''\n      },\n      ruleInlineSign: {\n        sign: [{\n          required: true,\n          message: '请输入短信签名',\n          trigger: 'blur'\n        }],\n        phone: [{\n          required: true,\n          validator: validatePhone,\n          trigger: 'blur'\n        }],\n        code: [{\n          required: true,\n          message: '请输入验证码',\n          trigger: 'blur'\n        }]\n      },\n      cutNUm: '获取验证码',\n      canClick: true\n    };\n  },\n  watch: {\n    sms: function sms(n) {\n      if (n.open === 1) this.getList();\n    }\n  },\n  mounted: function mounted() {\n    if (this.sms.open === 1) this.getList();\n    // if (this.isChecked === '1' && this.sms.open === 1) this.getList();\n  },\n  methods: {\n    editSign: function editSign() {\n      this.formInline.account = this.accountInfo.account;\n      this.formInline.sign = this.accountInfo.sms.sign;\n      this.formInline.phone = this.accountInfo.phone;\n      this.dialogVisible = true;\n    },\n    //修改签名\n    handleSubmit: (0, _validate.Debounce)(function (name) {\n      var _this = this;\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          (0, _sms.smsSignApi)(_this.formInline).then(/*#__PURE__*/function () {\n            var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(res) {\n              return _regenerator().w(function (_context) {\n                while (1) switch (_context.n) {\n                  case 0:\n                    _this.$message.success('修改签名之后一号通需要审核过后通过!');\n                    _this.dialogVisible = false;\n                    _this.$refs[formName].resetFields();\n                  case 1:\n                    return _context.a(2);\n                }\n              }, _callee);\n            }));\n            return function (_x) {\n              return _ref.apply(this, arguments);\n            };\n          }());\n        } else {\n          return false;\n        }\n      });\n    }),\n    // 短信验证码\n    cutDown: function cutDown() {\n      var _this2 = this;\n      if (this.formInline.phone) {\n        if (!this.canClick) return;\n        this.canClick = false;\n        this.cutNUm = 60;\n        var data = {\n          phone: this.formInline.phone,\n          types: 1\n        };\n        (0, _sms.captchaApi)(data).then(/*#__PURE__*/function () {\n          var _ref2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(res) {\n            return _regenerator().w(function (_context2) {\n              while (1) switch (_context2.n) {\n                case 0:\n                  _this2.$message.success(res.msg);\n                case 1:\n                  return _context2.a(2);\n              }\n            }, _callee2);\n          }));\n          return function (_x2) {\n            return _ref2.apply(this, arguments);\n          };\n        }());\n        var time = setInterval(function () {\n          _this2.cutNUm--;\n          if (_this2.cutNUm === 0) {\n            _this2.cutNUm = '获取验证码';\n            _this2.canClick = true;\n            clearInterval(time);\n          }\n        }, 1000);\n      } else {\n        this.$message.warning('请填写手机号!');\n      }\n    },\n    handleClose: function handleClose() {\n      this.dialogVisible = false;\n      this.$refs['formInline'].resetFields();\n    },\n    // 首页去开通\n    onOpenIndex: function onOpenIndex(val) {\n      this.tableFrom.type = val;\n      switch (val) {\n        case 'sms':\n          this.isSms = true;\n          break;\n        case 'expr_dump':\n          this.openDump();\n          break;\n        default:\n          this.openOther();\n          break;\n      }\n    },\n    // 开通其他\n    openOther: function openOther() {\n      var _this3 = this;\n      this.$confirm(\"\\u786E\\u5B9A\\u5F00\\u901A\".concat(commFilter.onePassTypeFilter(this.tableFrom.type), \"\\u5417?\"), '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        // this.handleSubmitDump('formInlineDump');\n        (0, _sms.serviceOpenApi)({\n          type: _this3.tableFrom.type\n        }).then(/*#__PURE__*/function () {\n          var _ref3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(res) {\n            return _regenerator().w(function (_context3) {\n              while (1) switch (_context3.n) {\n                case 0:\n                  _this3.$message.success('开通成功!');\n                  _this3.getList();\n                  _this3.$emit('openService');\n                case 1:\n                  return _context3.a(2);\n              }\n            }, _callee3);\n          }));\n          return function (_x3) {\n            return _ref3.apply(this, arguments);\n          };\n        }());\n      }).catch(function () {\n        _this3.$message({\n          type: 'info',\n          message: '已取消'\n        });\n      });\n    },\n    // 开通电子面单\n    openDump: function openDump() {\n      this.exportTempAllList();\n      this.isDump = true;\n    },\n    // 物流公司\n    exportTempAllList: function exportTempAllList() {\n      var _this4 = this;\n      (0, _sms.expressAllApi)({\n        type: 'elec'\n      }).then(/*#__PURE__*/function () {\n        var _ref4 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4(res) {\n          return _regenerator().w(function (_context4) {\n            while (1) switch (_context4.n) {\n              case 0:\n                _this4.exportList = res;\n              case 1:\n                return _context4.a(2);\n            }\n          }, _callee4);\n        }));\n        return function (_x4) {\n          return _ref4.apply(this, arguments);\n        };\n      }());\n    },\n    // 快递公司选择\n    onChangeExport: function onChangeExport(val) {\n      this.formInlineDump.tempId = '';\n      this.exportTemp(val);\n    },\n    // 电子面单模板\n    exportTemp: function exportTemp(val) {\n      var _this5 = this;\n      (0, _sms.exportTempApi)({\n        com: val\n      }).then(/*#__PURE__*/function () {\n        var _ref5 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5(res) {\n          return _regenerator().w(function (_context5) {\n            while (1) switch (_context5.n) {\n              case 0:\n                _this5.exportTempList = res.data.data || [];\n              case 1:\n                return _context5.a(2);\n            }\n          }, _callee5);\n        }));\n        return function (_x5) {\n          return _ref5.apply(this, arguments);\n        };\n      }());\n    },\n    onChangeImg: function onChangeImg(item) {\n      var _this6 = this;\n      this.exportTempList.map(function (i) {\n        if (i.temp_id === item) _this6.tempImg = i.pic;\n      });\n    },\n    handleSubmitDump: function handleSubmitDump(name) {\n      var _this7 = this;\n      this.formInlineDump.type = this.tableFrom.type;\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          _this7.loading = true;\n          (0, _sms.serviceOpenApi)(_this7.formInlineDump).then(/*#__PURE__*/function () {\n            var _ref6 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6(res) {\n              return _regenerator().w(function (_context6) {\n                while (1) switch (_context6.n) {\n                  case 0:\n                    _this7.$emit('openService');\n                    _this7.$message.success('开通成功!');\n                    _this7.getList();\n                    _this7.loading = false;\n                  case 1:\n                    return _context6.a(2);\n                }\n              }, _callee6);\n            }));\n            return function (_x6) {\n              return _ref6.apply(this, arguments);\n            };\n          }()).catch(function () {\n            _this7.loading = false;\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n    onChangeType: function onChangeType() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 列表\n    getList: function getList() {\n      var _this8 = this;\n      this.listLoading = true;\n      (0, _sms.smsLstApi)(this.tableFrom).then(function (res) {\n        _this8.tableData.data = res.data;\n        if (_this8.tableFrom.type == 'sms') {\n          var obj = new Object();\n          var newArr = new Array();\n          res.data.forEach(function (item) {\n            obj = item;\n            switch (item.status) {\n              case 0:\n                obj.status = '发送中';\n                break;\n              case 1:\n                obj.status = '成功';\n                break;\n              case 2:\n                obj.status = '失败';\n                break;\n              case 3:\n                obj.status = '全部';\n                break;\n            }\n            newArr.push(obj);\n            _this8.tableData.data = newArr;\n          });\n        }\n        _this8.tableData.total = res.count;\n        switch (_this8.tableFrom.type) {\n          case 'sms':\n            _this8.columns2 = [{\n              title: '手机号',\n              key: 'phone',\n              minWidth: 100\n            }, {\n              title: '模板内容',\n              key: 'content',\n              minWidth: 590\n            }, {\n              title: '发送时间',\n              key: 'add_time',\n              minWidth: 150\n            }\n            // {\n            //   title: '状态',\n            //   key: 'status',\n            //   minWidth: 100\n            // }\n            ];\n            break;\n          case 'expr_dump':\n            _this8.columns2 = [\n            // {\n            //   title: '订单号',\n            //   key: 'order_id',\n            //   minWidth: 150\n            // },\n            {\n              title: '发货人',\n              key: 'from_name',\n              minWidth: 120\n            }, {\n              title: '收货人',\n              key: 'to_name',\n              minWidth: 120\n            }, {\n              title: '快递单号',\n              key: 'num',\n              minWidth: 120\n            }, {\n              title: '快递公司编码',\n              key: 'code',\n              minWidth: 120\n            }, {\n              title: '状态',\n              key: '_resultcode',\n              minWidth: 100\n            }, {\n              title: '打印时间',\n              key: 'add_time',\n              minWidth: 150\n            }];\n            break;\n          case 'expr_query':\n            _this8.columns2 = [{\n              title: '快递单号',\n              key: 'content',\n              minWidth: 120\n            }, {\n              title: '快递公司编码',\n              key: 'code',\n              minWidth: 120\n            }, {\n              title: '状态',\n              key: '_resultcode',\n              minWidth: 120\n            }, {\n              title: '添加时间',\n              key: 'add_time',\n              minWidth: 150\n            }];\n            break;\n          default:\n            _this8.columns2 = [{\n              title: '复制URL',\n              key: 'url',\n              minWidth: 400\n            }, {\n              title: '请求状态',\n              key: '_resultcode',\n              minWidth: 120\n            }, {\n              title: '添加时间',\n              key: 'add_time',\n              minWidth: 150\n            }];\n            break;\n        }\n        _this8.listLoading = false;\n      }).catch(function (res) {\n        _this8.listLoading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    }\n  }\n};", null]}