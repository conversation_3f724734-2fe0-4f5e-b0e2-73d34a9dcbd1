{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\index.vue", "mtime": 1754050582488}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport tableList from './components/tableList'\r\nimport loginFrom from './components/loginFrom'\r\nimport registerFrom from './components/register'\r\nimport forgetPassword from './components/forgetPassword';\r\nimport forgetPhone from './components/forgetPhone';\r\nimport { logoutApi, smsNumberApi, smsInfoApi } from '@/api/sms'\r\nimport { mapGetters } from 'vuex'\r\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\r\nexport default {\r\n  name: 'SmsConfig',\r\n  components: { tableList, loginFrom, registerFrom, forgetPassword, forgetPhone },\r\n  data() {\r\n    return {\r\n      fullscreenLoading: false,\r\n      loading: false,\r\n      smsAccount: '',\r\n      circleUrl: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',\r\n      accountInfo:{},\r\n      spinShow: false,\r\n      isForgetPhone: false, // 修改手机号\r\n      isIndex: false, // 判断忘记密码返回的路径\r\n      isShowLogn: false, // 登录\r\n      isShow: false, // 修改密码\r\n      isShowReg: false, // 注册\r\n      isShowList: false, // 登录之后列表\r\n      sms: { open: 0 }, // 短信信息\r\n      query: { open: 0 }, // 物流查询\r\n      dump: { open: 0 }, // 电子面单打印\r\n      copy: { open: 0 }, // 商品采集,\r\n      infoData: {},\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'isLogin'\r\n    ])\r\n  },\r\n  mounted() {\r\n    this.onIsLogin()\r\n    // if (!this.isLogin) {\r\n    //   this.onIsLogin()\r\n    // } else {\r\n    //   this.isShowList = true\r\n    // }\r\n  },\r\n  methods: {\r\n    checkPermi,\r\n    // 开通服务\r\n    openService (val) {\r\n      this.getNumber();\r\n    },\r\n    onOpen (val) {\r\n      this.$refs.tableLists.onOpenIndex(val);\r\n    },\r\n    // 手机号返回\r\n    gobackPhone () {\r\n      this.isShowList = true;\r\n      this.isForgetPhone = false;\r\n    },\r\n    onChangePhone () {\r\n      this.isForgetPhone = true\r\n      this.isShowLogn = false;\r\n      this.isShowList = false;\r\n    },\r\n    // 密码返回\r\n    goback () {\r\n      if (this.isIndex) {\r\n        this.isShowList = true;\r\n        this.isShow = false;\r\n      } else {\r\n        this.isShowLogn = true;\r\n        this.isShow = false;\r\n      }\r\n    },\r\n    // 修改密码\r\n    onChangePassswordIndex () {\r\n      this.isIndex = true;\r\n      this.passsword();\r\n    },\r\n    // 忘记密码\r\n    onChangePasssword () {\r\n      this.isIndex = false;\r\n      this.passsword();\r\n      // this.isShowLogn = false;\r\n      // this.isShow = true;\r\n      // this.isShowList = false;\r\n    },\r\n    passsword () {\r\n      this.isShowLogn = false;\r\n      this.isShow = true;\r\n      this.isShowList = false;\r\n    },\r\n    mealPay (val) {\r\n      this.$router.push({ path:'/operation/systemSms/pay',query:{type:val}});\r\n    },\r\n    // 剩余条数\r\n    getNumber() {\r\n      this.loading = true;\r\n      smsInfoApi().then(async res => {\r\n        let data = res;\r\n        this.infoData = res;\r\n        this.sms = {\r\n          num: data.sms.num,\r\n          open: data.sms.open,\r\n          surp: data.sms.open\r\n        };\r\n        this.query = {\r\n          num: data.query.num,\r\n          open: data.query.open,\r\n          surp: data.query.open\r\n        };\r\n        this.dump = {\r\n          num: data.dump.num,\r\n          open: data.dump.open,\r\n          surp: data.dump.open\r\n        };\r\n        this.copy = {\r\n          num: data.copy.num,\r\n          open: data.copy.open,\r\n          surp: data.copy.open\r\n        };\r\n        this.loading = false;\r\n        this.smsAccount = data.account;\r\n        this.accountInfo = data;\r\n      }).catch(res => {\r\n        this.isShowLogn = true;\r\n        this.isShowList = false;\r\n        this.loading = false;\r\n      })\r\n    },\r\n    // 登录跳转\r\n    onLogin() {\r\n      const url = this.$route.query.url\r\n      if (url) {\r\n        this.$router.replace(url)\r\n      } else {\r\n        this.getNumber()\r\n        this.isShowLogn = false\r\n        this.isShow = false\r\n        this.isShowReg = false\r\n        this.isShowList = true\r\n      }\r\n    },\r\n    // 查看是否登录\r\n    onIsLogin() {\r\n      this.fullscreenLoading = true\r\n      this.$store.dispatch('user/isLogin').then(async res => {\r\n        const data = res\r\n        this.isShowLogn = !data.status\r\n        this.isShowList = data.status\r\n        if (data.status) {\r\n          this.smsAccount = data.info\r\n          this.getNumber()\r\n        }\r\n        this.fullscreenLoading = false\r\n      }).catch(res => {\r\n        this.fullscreenLoading = false\r\n        this.isShowLogn = true\r\n      })\r\n    },\r\n    // 退出登录\r\n    signOut() {\r\n      logoutApi().then(async res => {\r\n        this.isShowLogn = true\r\n        this.isShowList = false\r\n        this.infoData.phone = '';\r\n        this.$store.dispatch('user/isLogin')\r\n      })\r\n    },\r\n    // 立即注册\r\n    onChangeReg() {\r\n      this.isShowLogn = false\r\n      this.isShow = false\r\n      this.isShowReg = true\r\n    },\r\n    // 立即登录\r\n    logoup() {\r\n      this.isShowLogn = true\r\n      this.isShow = false\r\n      this.isShowReg = false\r\n    }\r\n  }\r\n}\r\n", null]}