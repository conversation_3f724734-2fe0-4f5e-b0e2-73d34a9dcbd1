{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\@babel\\parser\\lib\\index.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\@babel\\parser\\lib\\index.js", "mtime": 1754554388863}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}], "contextDependencies": [], "result": ["'use strict';function _typeof(o){\"@babel/helpers - typeof\";return _typeof=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(o){return typeof o;}:function(o){return o&&\"function\"==typeof Symbol&&o.constructor===Symbol&&o!==Symbol.prototype?\"symbol\":typeof o;},_typeof(o);}var _templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5;function _createForOfIteratorHelper(r,e){var t=\"undefined\"!=typeof Symbol&&r[Symbol.iterator]||r[\"@@iterator\"];if(!t){if(Array.isArray(r)||(t=_unsupportedIterableToArray(r))||e&&r&&\"number\"==typeof r.length){t&&(r=t);var _n=0,F=function F(){};return{s:F,n:function n(){return _n>=r.length?{done:!0}:{done:!1,value:r[_n++]};},e:function e(r){throw r;},f:F};}throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");}var o,a=!0,u=!1;return{s:function s(){t=t.call(r);},n:function n(){var r=t.next();return a=r.done,r;},e:function e(r){u=!0,o=r;},f:function f(){try{a||null==t.return||t.return();}finally{if(u)throw o;}}};}function _toConsumableArray(r){return _arrayWithoutHoles(r)||_iterableToArray(r)||_unsupportedIterableToArray(r)||_nonIterableSpread();}function _nonIterableSpread(){throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");}function _iterableToArray(r){if(\"undefined\"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r[\"@@iterator\"])return Array.from(r);}function _arrayWithoutHoles(r){if(Array.isArray(r))return _arrayLikeToArray(r);}function _slicedToArray(r,e){return _arrayWithHoles(r)||_iterableToArrayLimit(r,e)||_unsupportedIterableToArray(r,e)||_nonIterableRest();}function _nonIterableRest(){throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");}function _unsupportedIterableToArray(r,a){if(r){if(\"string\"==typeof r)return _arrayLikeToArray(r,a);var t={}.toString.call(r).slice(8,-1);return\"Object\"===t&&r.constructor&&(t=r.constructor.name),\"Map\"===t||\"Set\"===t?Array.from(r):\"Arguments\"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?_arrayLikeToArray(r,a):void 0;}}function _arrayLikeToArray(r,a){(null==a||a>r.length)&&(a=r.length);for(var e=0,n=Array(a);e<a;e++)n[e]=r[e];return n;}function _iterableToArrayLimit(r,l){var t=null==r?null:\"undefined\"!=typeof Symbol&&r[Symbol.iterator]||r[\"@@iterator\"];if(null!=t){var e,n,i,u,a=[],f=!0,o=!1;try{if(i=(t=t.call(r)).next,0===l){if(Object(t)!==t)return;f=!1;}else for(;!(f=(e=i.call(t)).done)&&(a.push(e.value),a.length!==l);f=!0);}catch(r){o=!0,n=r;}finally{try{if(!f&&null!=t.return&&(u=t.return(),Object(u)!==u))return;}finally{if(o)throw n;}}return a;}}function _arrayWithHoles(r){if(Array.isArray(r))return r;}function _callSuper(t,o,e){return o=_getPrototypeOf(o),_possibleConstructorReturn(t,_isNativeReflectConstruct()?Reflect.construct(o,e||[],_getPrototypeOf(t).constructor):o.apply(t,e));}function _possibleConstructorReturn(t,e){if(e&&(\"object\"==_typeof(e)||\"function\"==typeof e))return e;if(void 0!==e)throw new TypeError(\"Derived constructors may only return object or undefined\");return _assertThisInitialized(t);}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");return e;}function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}));}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t;})();}function _superPropGet(t,o,e,r){var p=_get(_getPrototypeOf(1&r?t.prototype:t),o,e);return 2&r&&\"function\"==typeof p?function(t){return p.apply(e,t);}:p;}function _get(){return _get=\"undefined\"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,r){var p=_superPropBase(e,t);if(p){var n=Object.getOwnPropertyDescriptor(p,t);return n.get?n.get.call(arguments.length<3?e:r):n.value;}},_get.apply(null,arguments);}function _superPropBase(t,o){for(;!{}.hasOwnProperty.call(t,o)&&null!==(t=_getPrototypeOf(t)););return t;}function _getPrototypeOf(t){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t);},_getPrototypeOf(t);}function _inherits(t,e){if(\"function\"!=typeof e&&null!==e)throw new TypeError(\"Super expression must either be null or a function\");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,\"prototype\",{writable:!1}),e&&_setPrototypeOf(t,e);}function _setPrototypeOf(t,e){return _setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t;},_setPrototypeOf(t,e);}function _taggedTemplateLiteral(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}));}function _defineProperties(e,r){for(var t=0;t<r.length;t++){var o=r[t];o.enumerable=o.enumerable||!1,o.configurable=!0,\"value\"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o);}}function _createClass(e,r,t){return r&&_defineProperties(e.prototype,r),t&&_defineProperties(e,t),Object.defineProperty(e,\"prototype\",{writable:!1}),e;}function _toPropertyKey(t){var i=_toPrimitive(t,\"string\");return\"symbol\"==_typeof(i)?i:i+\"\";}function _toPrimitive(t,r){if(\"object\"!=_typeof(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var i=e.call(t,r||\"default\");if(\"object\"!=_typeof(i))return i;throw new TypeError(\"@@toPrimitive must return a primitive value.\");}return(\"string\"===r?String:Number)(t);}function _classCallCheck(a,n){if(!(a instanceof n))throw new TypeError(\"Cannot call a class as a function\");}Object.defineProperty(exports,'__esModule',{value:true});function _objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(-1!==e.indexOf(n))continue;t[n]=r[n];}return t;}var Position=/*#__PURE__*/_createClass(function Position(line,col,index){_classCallCheck(this,Position);this.line=void 0;this.column=void 0;this.index=void 0;this.line=line;this.column=col;this.index=index;});var SourceLocation=/*#__PURE__*/_createClass(function SourceLocation(start,end){_classCallCheck(this,SourceLocation);this.start=void 0;this.end=void 0;this.filename=void 0;this.identifierName=void 0;this.start=start;this.end=end;});function createPositionWithColumnOffset(position,columnOffset){var line=position.line,column=position.column,index=position.index;return new Position(line,column+columnOffset,index+columnOffset);}var code=\"BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED\";var ModuleErrors={ImportMetaOutsideModule:{message:\"import.meta may appear only with 'sourceType: \\\"module\\\"'\",code:code},ImportOutsideModule:{message:\"'import' and 'export' may appear only with 'sourceType: \\\"module\\\"'\",code:code}};var NodeDescriptions={ArrayPattern:\"array destructuring pattern\",AssignmentExpression:\"assignment expression\",AssignmentPattern:\"assignment expression\",ArrowFunctionExpression:\"arrow function expression\",ConditionalExpression:\"conditional expression\",CatchClause:\"catch clause\",ForOfStatement:\"for-of statement\",ForInStatement:\"for-in statement\",ForStatement:\"for-loop\",FormalParameters:\"function parameter list\",Identifier:\"identifier\",ImportSpecifier:\"import specifier\",ImportDefaultSpecifier:\"import default specifier\",ImportNamespaceSpecifier:\"import namespace specifier\",ObjectPattern:\"object destructuring pattern\",ParenthesizedExpression:\"parenthesized expression\",RestElement:\"rest element\",UpdateExpression:{true:\"prefix operation\",false:\"postfix operation\"},VariableDeclarator:\"variable declaration\",YieldExpression:\"yield expression\"};var toNodeDescription=function toNodeDescription(node){return node.type===\"UpdateExpression\"?NodeDescriptions.UpdateExpression[\"\".concat(node.prefix)]:NodeDescriptions[node.type];};var StandardErrors={AccessorIsGenerator:function AccessorIsGenerator(_ref3){var kind=_ref3.kind;return\"A \".concat(kind,\"ter cannot be a generator.\");},ArgumentsInClass:\"'arguments' is only allowed in functions and class methods.\",AsyncFunctionInSingleStatementContext:\"Async functions can only be declared at the top level or inside a block.\",AwaitBindingIdentifier:\"Can not use 'await' as identifier inside an async function.\",AwaitBindingIdentifierInStaticBlock:\"Can not use 'await' as identifier inside a static block.\",AwaitExpressionFormalParameter:\"'await' is not allowed in async function parameters.\",AwaitUsingNotInAsyncContext:\"'await using' is only allowed within async functions and at the top levels of modules.\",AwaitNotInAsyncContext:\"'await' is only allowed within async functions and at the top levels of modules.\",BadGetterArity:\"A 'get' accessor must not have any formal parameters.\",BadSetterArity:\"A 'set' accessor must have exactly one formal parameter.\",BadSetterRestParameter:\"A 'set' accessor function argument must not be a rest parameter.\",ConstructorClassField:\"Classes may not have a field named 'constructor'.\",ConstructorClassPrivateField:\"Classes may not have a private field named '#constructor'.\",ConstructorIsAccessor:\"Class constructor may not be an accessor.\",ConstructorIsAsync:\"Constructor can't be an async function.\",ConstructorIsGenerator:\"Constructor can't be a generator.\",DeclarationMissingInitializer:function DeclarationMissingInitializer(_ref4){var kind=_ref4.kind;return\"Missing initializer in \".concat(kind,\" declaration.\");},DecoratorArgumentsOutsideParentheses:\"Decorator arguments must be moved inside parentheses: use '@(decorator(args))' instead of '@(decorator)(args)'.\",DecoratorBeforeExport:\"Decorators must be placed *before* the 'export' keyword. Remove the 'decoratorsBeforeExport: true' option to use the 'export @decorator class {}' syntax.\",DecoratorsBeforeAfterExport:\"Decorators can be placed *either* before or after the 'export' keyword, but not in both locations at the same time.\",DecoratorConstructor:\"Decorators can't be used with a constructor. Did you mean '@dec class { ... }'?\",DecoratorExportClass:\"Decorators must be placed *after* the 'export' keyword. Remove the 'decoratorsBeforeExport: false' option to use the '@decorator export class {}' syntax.\",DecoratorSemicolon:\"Decorators must not be followed by a semicolon.\",DecoratorStaticBlock:\"Decorators can't be used with a static block.\",DeferImportRequiresNamespace:'Only `import defer * as x from \"./module\"` is valid.',DeletePrivateField:\"Deleting a private field is not allowed.\",DestructureNamedImport:\"ES2015 named imports do not destructure. Use another statement for destructuring after the import.\",DuplicateConstructor:\"Duplicate constructor in the same class.\",DuplicateDefaultExport:\"Only one default export allowed per module.\",DuplicateExport:function DuplicateExport(_ref5){var exportName=_ref5.exportName;return\"`\".concat(exportName,\"` has already been exported. Exported identifiers must be unique.\");},DuplicateProto:\"Redefinition of __proto__ property.\",DuplicateRegExpFlags:\"Duplicate regular expression flag.\",ElementAfterRest:\"Rest element must be last element.\",EscapedCharNotAnIdentifier:\"Invalid Unicode escape.\",ExportBindingIsString:function ExportBindingIsString(_ref6){var localName=_ref6.localName,exportName=_ref6.exportName;return\"A string literal cannot be used as an exported binding without `from`.\\n- Did you mean `export { '\".concat(localName,\"' as '\").concat(exportName,\"' } from 'some-module'`?\");},ExportDefaultFromAsIdentifier:\"'from' is not allowed as an identifier after 'export default'.\",ForInOfLoopInitializer:function ForInOfLoopInitializer(_ref7){var type=_ref7.type;return\"'\".concat(type===\"ForInStatement\"?\"for-in\":\"for-of\",\"' loop variable declaration may not have an initializer.\");},ForInUsing:\"For-in loop may not start with 'using' declaration.\",ForOfAsync:\"The left-hand side of a for-of loop may not be 'async'.\",ForOfLet:\"The left-hand side of a for-of loop may not start with 'let'.\",GeneratorInSingleStatementContext:\"Generators can only be declared at the top level or inside a block.\",IllegalBreakContinue:function IllegalBreakContinue(_ref8){var type=_ref8.type;return\"Unsyntactic \".concat(type===\"BreakStatement\"?\"break\":\"continue\",\".\");},IllegalLanguageModeDirective:\"Illegal 'use strict' directive in function with non-simple parameter list.\",IllegalReturn:\"'return' outside of function.\",ImportAttributesUseAssert:\"The `assert` keyword in import attributes is deprecated and it has been replaced by the `with` keyword. You can enable the `deprecatedImportAssert` parser plugin to suppress this error.\",ImportBindingIsString:function ImportBindingIsString(_ref9){var importName=_ref9.importName;return\"A string literal cannot be used as an imported binding.\\n- Did you mean `import { \\\"\".concat(importName,\"\\\" as foo }`?\");},ImportCallArity:\"`import()` requires exactly one or two arguments.\",ImportCallNotNewExpression:\"Cannot use new with import(...).\",ImportCallSpreadArgument:\"`...` is not allowed in `import()`.\",ImportJSONBindingNotDefault:\"A JSON module can only be imported with `default`.\",ImportReflectionHasAssertion:\"`import module x` cannot have assertions.\",ImportReflectionNotBinding:'Only `import module x from \"./module\"` is valid.',IncompatibleRegExpUVFlags:\"The 'u' and 'v' regular expression flags cannot be enabled at the same time.\",InvalidBigIntLiteral:\"Invalid BigIntLiteral.\",InvalidCodePoint:\"Code point out of bounds.\",InvalidCoverDiscardElement:\"'void' must be followed by an expression when not used in a binding position.\",InvalidCoverInitializedName:\"Invalid shorthand property initializer.\",InvalidDecimal:\"Invalid decimal.\",InvalidDigit:function InvalidDigit(_ref0){var radix=_ref0.radix;return\"Expected number in radix \".concat(radix,\".\");},InvalidEscapeSequence:\"Bad character escape sequence.\",InvalidEscapeSequenceTemplate:\"Invalid escape sequence in template.\",InvalidEscapedReservedWord:function InvalidEscapedReservedWord(_ref1){var reservedWord=_ref1.reservedWord;return\"Escape sequence in keyword \".concat(reservedWord,\".\");},InvalidIdentifier:function InvalidIdentifier(_ref10){var identifierName=_ref10.identifierName;return\"Invalid identifier \".concat(identifierName,\".\");},InvalidLhs:function InvalidLhs(_ref11){var ancestor=_ref11.ancestor;return\"Invalid left-hand side in \".concat(toNodeDescription(ancestor),\".\");},InvalidLhsBinding:function InvalidLhsBinding(_ref12){var ancestor=_ref12.ancestor;return\"Binding invalid left-hand side in \".concat(toNodeDescription(ancestor),\".\");},InvalidLhsOptionalChaining:function InvalidLhsOptionalChaining(_ref13){var ancestor=_ref13.ancestor;return\"Invalid optional chaining in the left-hand side of \".concat(toNodeDescription(ancestor),\".\");},InvalidNumber:\"Invalid number.\",InvalidOrMissingExponent:\"Floating-point numbers require a valid exponent after the 'e'.\",InvalidOrUnexpectedToken:function InvalidOrUnexpectedToken(_ref14){var unexpected=_ref14.unexpected;return\"Unexpected character '\".concat(unexpected,\"'.\");},InvalidParenthesizedAssignment:\"Invalid parenthesized assignment pattern.\",InvalidPrivateFieldResolution:function InvalidPrivateFieldResolution(_ref15){var identifierName=_ref15.identifierName;return\"Private name #\".concat(identifierName,\" is not defined.\");},InvalidPropertyBindingPattern:\"Binding member expression.\",InvalidRecordProperty:\"Only properties and spread elements are allowed in record definitions.\",InvalidRestAssignmentPattern:\"Invalid rest operator's argument.\",LabelRedeclaration:function LabelRedeclaration(_ref16){var labelName=_ref16.labelName;return\"Label '\".concat(labelName,\"' is already declared.\");},LetInLexicalBinding:\"'let' is disallowed as a lexically bound name.\",LineTerminatorBeforeArrow:\"No line break is allowed before '=>'.\",MalformedRegExpFlags:\"Invalid regular expression flag.\",MissingClassName:\"A class name is required.\",MissingEqInAssignment:\"Only '=' operator can be used for specifying default value.\",MissingSemicolon:\"Missing semicolon.\",MissingPlugin:function MissingPlugin(_ref17){var missingPlugin=_ref17.missingPlugin;return\"This experimental syntax requires enabling the parser plugin: \".concat(missingPlugin.map(function(name){return JSON.stringify(name);}).join(\", \"),\".\");},MissingOneOfPlugins:function MissingOneOfPlugins(_ref18){var missingPlugin=_ref18.missingPlugin;return\"This experimental syntax requires enabling one of the following parser plugin(s): \".concat(missingPlugin.map(function(name){return JSON.stringify(name);}).join(\", \"),\".\");},MissingUnicodeEscape:\"Expecting Unicode escape sequence \\\\uXXXX.\",MixingCoalesceWithLogical:\"Nullish coalescing operator(??) requires parens when mixing with logical operators.\",ModuleAttributeDifferentFromType:\"The only accepted module attribute is `type`.\",ModuleAttributeInvalidValue:\"Only string literals are allowed as module attribute values.\",ModuleAttributesWithDuplicateKeys:function ModuleAttributesWithDuplicateKeys(_ref19){var key=_ref19.key;return\"Duplicate key \\\"\".concat(key,\"\\\" is not allowed in module attributes.\");},ModuleExportNameHasLoneSurrogate:function ModuleExportNameHasLoneSurrogate(_ref20){var surrogateCharCode=_ref20.surrogateCharCode;return\"An export name cannot include a lone surrogate, found '\\\\u\".concat(surrogateCharCode.toString(16),\"'.\");},ModuleExportUndefined:function ModuleExportUndefined(_ref21){var localName=_ref21.localName;return\"Export '\".concat(localName,\"' is not defined.\");},MultipleDefaultsInSwitch:\"Multiple default clauses.\",NewlineAfterThrow:\"Illegal newline after throw.\",NoCatchOrFinally:\"Missing catch or finally clause.\",NumberIdentifier:\"Identifier directly after number.\",NumericSeparatorInEscapeSequence:\"Numeric separators are not allowed inside unicode escape sequences or hex escape sequences.\",ObsoleteAwaitStar:\"'await*' has been removed from the async functions proposal. Use Promise.all() instead.\",OptionalChainingNoNew:\"Constructors in/after an Optional Chain are not allowed.\",OptionalChainingNoTemplate:\"Tagged Template Literals are not allowed in optionalChain.\",OverrideOnConstructor:\"'override' modifier cannot appear on a constructor declaration.\",ParamDupe:\"Argument name clash.\",PatternHasAccessor:\"Object pattern can't contain getter or setter.\",PatternHasMethod:\"Object pattern can't contain methods.\",PrivateInExpectedIn:function PrivateInExpectedIn(_ref22){var identifierName=_ref22.identifierName;return\"Private names are only allowed in property accesses (`obj.#\".concat(identifierName,\"`) or in `in` expressions (`#\").concat(identifierName,\" in obj`).\");},PrivateNameRedeclaration:function PrivateNameRedeclaration(_ref23){var identifierName=_ref23.identifierName;return\"Duplicate private name #\".concat(identifierName,\".\");},RecordExpressionBarIncorrectEndSyntaxType:\"Record expressions ending with '|}' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.\",RecordExpressionBarIncorrectStartSyntaxType:\"Record expressions starting with '{|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.\",RecordExpressionHashIncorrectStartSyntaxType:\"Record expressions starting with '#{' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.\",RecordNoProto:\"'__proto__' is not allowed in Record expressions.\",RestTrailingComma:\"Unexpected trailing comma after rest element.\",SloppyFunction:\"In non-strict mode code, functions can only be declared at top level or inside a block.\",SloppyFunctionAnnexB:\"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement.\",SourcePhaseImportRequiresDefault:'Only `import source x from \"./module\"` is valid.',StaticPrototype:\"Classes may not have static property named prototype.\",SuperNotAllowed:\"`super()` is only valid inside a class constructor of a subclass. Maybe a typo in the method name ('constructor') or not extending another class?\",SuperPrivateField:\"Private fields can't be accessed on super.\",TrailingDecorator:\"Decorators must be attached to a class element.\",TupleExpressionBarIncorrectEndSyntaxType:\"Tuple expressions ending with '|]' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.\",TupleExpressionBarIncorrectStartSyntaxType:\"Tuple expressions starting with '[|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.\",TupleExpressionHashIncorrectStartSyntaxType:\"Tuple expressions starting with '#[' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.\",UnexpectedArgumentPlaceholder:\"Unexpected argument placeholder.\",UnexpectedAwaitAfterPipelineBody:'Unexpected \"await\" after pipeline body; await must have parentheses in minimal proposal.',UnexpectedDigitAfterHash:\"Unexpected digit after hash token.\",UnexpectedImportExport:\"'import' and 'export' may only appear at the top level.\",UnexpectedKeyword:function UnexpectedKeyword(_ref24){var keyword=_ref24.keyword;return\"Unexpected keyword '\".concat(keyword,\"'.\");},UnexpectedLeadingDecorator:\"Leading decorators must be attached to a class declaration.\",UnexpectedLexicalDeclaration:\"Lexical declaration cannot appear in a single-statement context.\",UnexpectedNewTarget:\"`new.target` can only be used in functions or class properties.\",UnexpectedNumericSeparator:\"A numeric separator is only allowed between two digits.\",UnexpectedPrivateField:\"Unexpected private name.\",UnexpectedReservedWord:function UnexpectedReservedWord(_ref25){var reservedWord=_ref25.reservedWord;return\"Unexpected reserved word '\".concat(reservedWord,\"'.\");},UnexpectedSuper:\"'super' is only allowed in object methods and classes.\",UnexpectedToken:function UnexpectedToken(_ref26){var expected=_ref26.expected,unexpected=_ref26.unexpected;return\"Unexpected token\".concat(unexpected?\" '\".concat(unexpected,\"'.\"):\"\").concat(expected?\", expected \\\"\".concat(expected,\"\\\"\"):\"\");},UnexpectedTokenUnaryExponentiation:\"Illegal expression. Wrap left hand side or entire exponentiation in parentheses.\",UnexpectedUsingDeclaration:\"Using declaration cannot appear in the top level when source type is `script` or in the bare case statement.\",UnexpectedVoidPattern:\"Unexpected void binding.\",UnsupportedBind:\"Binding should be performed on object property.\",UnsupportedDecoratorExport:\"A decorated export must export a class declaration.\",UnsupportedDefaultExport:\"Only expressions, functions or classes are allowed as the `default` export.\",UnsupportedImport:\"`import` can only be used in `import()` or `import.meta`.\",UnsupportedMetaProperty:function UnsupportedMetaProperty(_ref27){var target=_ref27.target,onlyValidPropertyName=_ref27.onlyValidPropertyName;return\"The only valid meta property for \".concat(target,\" is \").concat(target,\".\").concat(onlyValidPropertyName,\".\");},UnsupportedParameterDecorator:\"Decorators cannot be used to decorate parameters.\",UnsupportedPropertyDecorator:\"Decorators cannot be used to decorate object literal properties.\",UnsupportedSuper:\"'super' can only be used with function calls (i.e. super()) or in property accesses (i.e. super.prop or super[prop]).\",UnterminatedComment:\"Unterminated comment.\",UnterminatedRegExp:\"Unterminated regular expression.\",UnterminatedString:\"Unterminated string constant.\",UnterminatedTemplate:\"Unterminated template.\",UsingDeclarationExport:\"Using declaration cannot be exported.\",UsingDeclarationHasBindingPattern:\"Using declaration cannot have destructuring patterns.\",VarRedeclaration:function VarRedeclaration(_ref28){var identifierName=_ref28.identifierName;return\"Identifier '\".concat(identifierName,\"' has already been declared.\");},VoidPatternCatchClauseParam:\"A void binding can not be the catch clause parameter. Use `try { ... } catch { ... }` if you want to discard the caught error.\",VoidPatternInitializer:\"A void binding may not have an initializer.\",YieldBindingIdentifier:\"Can not use 'yield' as identifier inside a generator.\",YieldInParameter:\"Yield expression is not allowed in formal parameters.\",YieldNotInGeneratorFunction:\"'yield' is only allowed within generator functions.\",ZeroDigitNumericSeparator:\"Numeric separator can not be used after leading 0.\"};var StrictModeErrors={StrictDelete:\"Deleting local variable in strict mode.\",StrictEvalArguments:function StrictEvalArguments(_ref29){var referenceName=_ref29.referenceName;return\"Assigning to '\".concat(referenceName,\"' in strict mode.\");},StrictEvalArgumentsBinding:function StrictEvalArgumentsBinding(_ref30){var bindingName=_ref30.bindingName;return\"Binding '\".concat(bindingName,\"' in strict mode.\");},StrictFunction:\"In strict mode code, functions can only be declared at top level or inside a block.\",StrictNumericEscape:\"The only valid numeric escape in strict mode is '\\\\0'.\",StrictOctalLiteral:\"Legacy octal literals are not allowed in strict mode.\",StrictWith:\"'with' in strict mode.\"};var ParseExpressionErrors={ParseExpressionEmptyInput:\"Unexpected parseExpression() input: The input is empty or contains only comments.\",ParseExpressionExpectsEOF:function ParseExpressionExpectsEOF(_ref31){var unexpected=_ref31.unexpected;return\"Unexpected parseExpression() input: The input should contain exactly one expression, but the first expression is followed by the unexpected character `\".concat(String.fromCodePoint(unexpected),\"`.\");}};var UnparenthesizedPipeBodyDescriptions=new Set([\"ArrowFunctionExpression\",\"AssignmentExpression\",\"ConditionalExpression\",\"YieldExpression\"]);var PipelineOperatorErrors=Object.assign({PipeBodyIsTighter:\"Unexpected yield after pipeline body; any yield expression acting as Hack-style pipe body must be parenthesized due to its loose operator precedence.\",PipeTopicRequiresHackPipes:'Topic reference is used, but the pipelineOperator plugin was not passed a \"proposal\": \"hack\" or \"smart\" option.',PipeTopicUnbound:\"Topic reference is unbound; it must be inside a pipe body.\",PipeTopicUnconfiguredToken:function PipeTopicUnconfiguredToken(_ref32){var token=_ref32.token;return\"Invalid topic token \".concat(token,\". In order to use \").concat(token,\" as a topic reference, the pipelineOperator plugin must be configured with { \\\"proposal\\\": \\\"hack\\\", \\\"topicToken\\\": \\\"\").concat(token,\"\\\" }.\");},PipeTopicUnused:\"Hack-style pipe body does not contain a topic reference; Hack-style pipes must use topic at least once.\",PipeUnparenthesizedBody:function PipeUnparenthesizedBody(_ref33){var type=_ref33.type;return\"Hack-style pipe body cannot be an unparenthesized \".concat(toNodeDescription({type:type}),\"; please wrap it in parentheses.\");}},{PipelineBodyNoArrow:'Unexpected arrow \"=>\" after pipeline body; arrow function in pipeline body must be parenthesized.',PipelineBodySequenceExpression:\"Pipeline body may not be a comma-separated sequence expression.\",PipelineHeadSequenceExpression:\"Pipeline head should not be a comma-separated sequence expression.\",PipelineTopicUnused:\"Pipeline is in topic style but does not use topic reference.\",PrimaryTopicNotAllowed:\"Topic reference was used in a lexical context without topic binding.\",PrimaryTopicRequiresSmartPipeline:'Topic reference is used, but the pipelineOperator plugin was not passed a \"proposal\": \"hack\" or \"smart\" option.'});var _excluded=[\"message\"];function defineHidden(obj,key,value){Object.defineProperty(obj,key,{enumerable:false,configurable:true,value:value});}function toParseErrorConstructor(_ref34){var toMessage=_ref34.toMessage,code=_ref34.code,reasonCode=_ref34.reasonCode,syntaxPlugin=_ref34.syntaxPlugin;var hasMissingPlugin=reasonCode===\"MissingPlugin\"||reasonCode===\"MissingOneOfPlugins\";{var oldReasonCodes={AccessorCannotDeclareThisParameter:\"AccesorCannotDeclareThisParameter\",AccessorCannotHaveTypeParameters:\"AccesorCannotHaveTypeParameters\",ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference:\"ConstInitiailizerMustBeStringOrNumericLiteralOrLiteralEnumReference\",SetAccessorCannotHaveOptionalParameter:\"SetAccesorCannotHaveOptionalParameter\",SetAccessorCannotHaveRestParameter:\"SetAccesorCannotHaveRestParameter\",SetAccessorCannotHaveReturnType:\"SetAccesorCannotHaveReturnType\"};if(oldReasonCodes[reasonCode]){reasonCode=oldReasonCodes[reasonCode];}}return function constructor(loc,details){var error=new SyntaxError();error.code=code;error.reasonCode=reasonCode;error.loc=loc;error.pos=loc.index;error.syntaxPlugin=syntaxPlugin;if(hasMissingPlugin){error.missingPlugin=details.missingPlugin;}defineHidden(error,\"clone\",function clone(){var overrides=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};var _overrides$loc;var _ref35=(_overrides$loc=overrides.loc)!=null?_overrides$loc:loc,line=_ref35.line,column=_ref35.column,index=_ref35.index;return constructor(new Position(line,column,index),Object.assign({},details,overrides.details));});defineHidden(error,\"details\",details);Object.defineProperty(error,\"message\",{configurable:true,get:function get(){var message=\"\".concat(toMessage(details),\" (\").concat(loc.line,\":\").concat(loc.column,\")\");this.message=message;return message;},set:function set(value){Object.defineProperty(this,\"message\",{value:value,writable:true});}});return error;};}function ParseErrorEnum(argument,syntaxPlugin){if(Array.isArray(argument)){return function(parseErrorTemplates){return ParseErrorEnum(parseErrorTemplates,argument[0]);};}var ParseErrorConstructors={};var _loop=function _loop(){var reasonCode=_Object$keys[_i];var template=argument[reasonCode];var _ref=typeof template===\"string\"?{message:function message(){return template;}}:typeof template===\"function\"?{message:template}:template,message=_ref.message,rest=_objectWithoutPropertiesLoose(_ref,_excluded);var toMessage=typeof message===\"string\"?function(){return message;}:message;ParseErrorConstructors[reasonCode]=toParseErrorConstructor(Object.assign({code:\"BABEL_PARSER_SYNTAX_ERROR\",reasonCode:reasonCode,toMessage:toMessage},syntaxPlugin?{syntaxPlugin:syntaxPlugin}:{},rest));};for(var _i=0,_Object$keys=Object.keys(argument);_i<_Object$keys.length;_i++){_loop();}return ParseErrorConstructors;}var Errors=Object.assign({},ParseErrorEnum(ModuleErrors),ParseErrorEnum(StandardErrors),ParseErrorEnum(StrictModeErrors),ParseErrorEnum(ParseExpressionErrors),ParseErrorEnum(_templateObject||(_templateObject=_taggedTemplateLiteral([\"pipelineOperator\"])))(PipelineOperatorErrors));function createDefaultOptions(){return{sourceType:\"script\",sourceFilename:undefined,startIndex:0,startColumn:0,startLine:1,allowAwaitOutsideFunction:false,allowReturnOutsideFunction:false,allowNewTargetOutsideFunction:false,allowImportExportEverywhere:false,allowSuperOutsideMethod:false,allowUndeclaredExports:false,allowYieldOutsideFunction:false,plugins:[],strictMode:null,ranges:false,tokens:false,createImportExpressions:false,createParenthesizedExpressions:false,errorRecovery:false,attachComment:true,annexB:true};}function getOptions(opts){var options=createDefaultOptions();if(opts==null){return options;}if(opts.annexB!=null&&opts.annexB!==false){throw new Error(\"The `annexB` option can only be set to `false`.\");}for(var _i2=0,_Object$keys2=Object.keys(options);_i2<_Object$keys2.length;_i2++){var key=_Object$keys2[_i2];if(opts[key]!=null)options[key]=opts[key];}if(options.startLine===1){if(opts.startIndex==null&&options.startColumn>0){options.startIndex=options.startColumn;}else if(opts.startColumn==null&&options.startIndex>0){options.startColumn=options.startIndex;}}else if(opts.startColumn==null||opts.startIndex==null){if(opts.startIndex!=null){throw new Error(\"With a `startLine > 1` you must also specify `startIndex` and `startColumn`.\");}}if(options.sourceType===\"commonjs\"){if(opts.allowAwaitOutsideFunction!=null){throw new Error(\"The `allowAwaitOutsideFunction` option cannot be used with `sourceType: 'commonjs'`.\");}if(opts.allowReturnOutsideFunction!=null){throw new Error(\"`sourceType: 'commonjs'` implies `allowReturnOutsideFunction: true`, please remove the `allowReturnOutsideFunction` option or use `sourceType: 'script'`.\");}if(opts.allowNewTargetOutsideFunction!=null){throw new Error(\"`sourceType: 'commonjs'` implies `allowNewTargetOutsideFunction: true`, please remove the `allowNewTargetOutsideFunction` option or use `sourceType: 'script'`.\");}}return options;}var defineProperty=Object.defineProperty;var toUnenumerable=function toUnenumerable(object,key){if(object){defineProperty(object,key,{enumerable:false,value:object[key]});}};function toESTreeLocation(node){toUnenumerable(node.loc.start,\"index\");toUnenumerable(node.loc.end,\"index\");return node;}var estree=function estree(superClass){return/*#__PURE__*/function(_superClass){function ESTreeParserMixin(){_classCallCheck(this,ESTreeParserMixin);return _callSuper(this,ESTreeParserMixin,arguments);}_inherits(ESTreeParserMixin,_superClass);return _createClass(ESTreeParserMixin,[{key:\"parse\",value:function parse(){var file=toESTreeLocation(_superPropGet(ESTreeParserMixin,\"parse\",this,3)([]));if(this.optionFlags&256){file.tokens=file.tokens.map(toESTreeLocation);}return file;}},{key:\"parseRegExpLiteral\",value:function parseRegExpLiteral(_ref36){var pattern=_ref36.pattern,flags=_ref36.flags;var regex=null;try{regex=new RegExp(pattern,flags);}catch(_){}var node=this.estreeParseLiteral(regex);node.regex={pattern:pattern,flags:flags};return node;}},{key:\"parseBigIntLiteral\",value:function parseBigIntLiteral(value){var bigInt;try{bigInt=BigInt(value);}catch(_unused){bigInt=null;}var node=this.estreeParseLiteral(bigInt);node.bigint=String(node.value||value);return node;}},{key:\"parseDecimalLiteral\",value:function parseDecimalLiteral(value){var decimal=null;var node=this.estreeParseLiteral(decimal);node.decimal=String(node.value||value);return node;}},{key:\"estreeParseLiteral\",value:function estreeParseLiteral(value){return this.parseLiteral(value,\"Literal\");}},{key:\"parseStringLiteral\",value:function parseStringLiteral(value){return this.estreeParseLiteral(value);}},{key:\"parseNumericLiteral\",value:function parseNumericLiteral(value){return this.estreeParseLiteral(value);}},{key:\"parseNullLiteral\",value:function parseNullLiteral(){return this.estreeParseLiteral(null);}},{key:\"parseBooleanLiteral\",value:function parseBooleanLiteral(value){return this.estreeParseLiteral(value);}},{key:\"estreeParseChainExpression\",value:function estreeParseChainExpression(node,endLoc){var chain=this.startNodeAtNode(node);chain.expression=node;return this.finishNodeAt(chain,\"ChainExpression\",endLoc);}},{key:\"directiveToStmt\",value:function directiveToStmt(directive){var expression=directive.value;delete directive.value;this.castNodeTo(expression,\"Literal\");expression.raw=expression.extra.raw;expression.value=expression.extra.expressionValue;var stmt=this.castNodeTo(directive,\"ExpressionStatement\");stmt.expression=expression;stmt.directive=expression.extra.rawValue;delete expression.extra;return stmt;}},{key:\"fillOptionalPropertiesForTSESLint\",value:function fillOptionalPropertiesForTSESLint(node){}},{key:\"cloneEstreeStringLiteral\",value:function cloneEstreeStringLiteral(node){var start=node.start,end=node.end,loc=node.loc,range=node.range,raw=node.raw,value=node.value;var cloned=Object.create(node.constructor.prototype);cloned.type=\"Literal\";cloned.start=start;cloned.end=end;cloned.loc=loc;cloned.range=range;cloned.raw=raw;cloned.value=value;return cloned;}},{key:\"initFunction\",value:function initFunction(node,isAsync){_superPropGet(ESTreeParserMixin,\"initFunction\",this,3)([node,isAsync]);node.expression=false;}},{key:\"checkDeclaration\",value:function checkDeclaration(node){if(node!=null&&this.isObjectProperty(node)){this.checkDeclaration(node.value);}else{_superPropGet(ESTreeParserMixin,\"checkDeclaration\",this,3)([node]);}}},{key:\"getObjectOrClassMethodParams\",value:function getObjectOrClassMethodParams(method){return method.value.params;}},{key:\"isValidDirective\",value:function isValidDirective(stmt){var _stmt$expression$extr;return stmt.type===\"ExpressionStatement\"&&stmt.expression.type===\"Literal\"&&typeof stmt.expression.value===\"string\"&&!((_stmt$expression$extr=stmt.expression.extra)!=null&&_stmt$expression$extr.parenthesized);}},{key:\"parseBlockBody\",value:function parseBlockBody(node,allowDirectives,topLevel,end,afterBlockParse){var _this2=this;_superPropGet(ESTreeParserMixin,\"parseBlockBody\",this,3)([node,allowDirectives,topLevel,end,afterBlockParse]);var directiveStatements=node.directives.map(function(d){return _this2.directiveToStmt(d);});node.body=directiveStatements.concat(node.body);delete node.directives;}},{key:\"parsePrivateName\",value:function parsePrivateName(){var node=_superPropGet(ESTreeParserMixin,\"parsePrivateName\",this,3)([]);{if(!this.getPluginOption(\"estree\",\"classFeatures\")){return node;}}return this.convertPrivateNameToPrivateIdentifier(node);}},{key:\"convertPrivateNameToPrivateIdentifier\",value:function convertPrivateNameToPrivateIdentifier(node){var name=_superPropGet(ESTreeParserMixin,\"getPrivateNameSV\",this,3)([node]);node=node;delete node.id;node.name=name;return this.castNodeTo(node,\"PrivateIdentifier\");}},{key:\"isPrivateName\",value:function isPrivateName(node){{if(!this.getPluginOption(\"estree\",\"classFeatures\")){return _superPropGet(ESTreeParserMixin,\"isPrivateName\",this,3)([node]);}}return node.type===\"PrivateIdentifier\";}},{key:\"getPrivateNameSV\",value:function getPrivateNameSV(node){{if(!this.getPluginOption(\"estree\",\"classFeatures\")){return _superPropGet(ESTreeParserMixin,\"getPrivateNameSV\",this,3)([node]);}}return node.name;}},{key:\"parseLiteral\",value:function parseLiteral(value,type){var node=_superPropGet(ESTreeParserMixin,\"parseLiteral\",this,3)([value,type]);node.raw=node.extra.raw;delete node.extra;return node;}},{key:\"parseFunctionBody\",value:function parseFunctionBody(node,allowExpression){var isMethod=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;_superPropGet(ESTreeParserMixin,\"parseFunctionBody\",this,3)([node,allowExpression,isMethod]);node.expression=node.body.type!==\"BlockStatement\";}},{key:\"parseMethod\",value:function parseMethod(node,isGenerator,isAsync,isConstructor,allowDirectSuper,type){var inClassScope=arguments.length>6&&arguments[6]!==undefined?arguments[6]:false;var funcNode=this.startNode();funcNode.kind=node.kind;funcNode=_superPropGet(ESTreeParserMixin,\"parseMethod\",this,3)([funcNode,isGenerator,isAsync,isConstructor,allowDirectSuper,type,inClassScope]);delete funcNode.kind;var typeParameters=node.typeParameters;if(typeParameters){delete node.typeParameters;funcNode.typeParameters=typeParameters;this.resetStartLocationFromNode(funcNode,typeParameters);}var valueNode=this.castNodeTo(funcNode,\"FunctionExpression\");node.value=valueNode;if(type===\"ClassPrivateMethod\"){node.computed=false;}if(type===\"ObjectMethod\"){if(node.kind===\"method\"){node.kind=\"init\";}node.shorthand=false;return this.finishNode(node,\"Property\");}else{return this.finishNode(node,\"MethodDefinition\");}}},{key:\"nameIsConstructor\",value:function nameIsConstructor(key){if(key.type===\"Literal\")return key.value===\"constructor\";return _superPropGet(ESTreeParserMixin,\"nameIsConstructor\",this,3)([key]);}},{key:\"parseClassProperty\",value:function parseClassProperty(){for(var _len=arguments.length,args=new Array(_len),_key=0;_key<_len;_key++){args[_key]=arguments[_key];}var propertyNode=_superPropGet(ESTreeParserMixin,\"parseClassProperty\",this,3)(args);{if(!this.getPluginOption(\"estree\",\"classFeatures\")){return propertyNode;}}{this.castNodeTo(propertyNode,\"PropertyDefinition\");}return propertyNode;}},{key:\"parseClassPrivateProperty\",value:function parseClassPrivateProperty(){for(var _len2=arguments.length,args=new Array(_len2),_key2=0;_key2<_len2;_key2++){args[_key2]=arguments[_key2];}var propertyNode=_superPropGet(ESTreeParserMixin,\"parseClassPrivateProperty\",this,3)(args);{if(!this.getPluginOption(\"estree\",\"classFeatures\")){return propertyNode;}}{this.castNodeTo(propertyNode,\"PropertyDefinition\");}propertyNode.computed=false;return propertyNode;}},{key:\"parseClassAccessorProperty\",value:function parseClassAccessorProperty(node){var accessorPropertyNode=_superPropGet(ESTreeParserMixin,\"parseClassAccessorProperty\",this,3)([node]);{if(!this.getPluginOption(\"estree\",\"classFeatures\")){return accessorPropertyNode;}}if(accessorPropertyNode.abstract&&this.hasPlugin(\"typescript\")){delete accessorPropertyNode.abstract;this.castNodeTo(accessorPropertyNode,\"TSAbstractAccessorProperty\");}else{this.castNodeTo(accessorPropertyNode,\"AccessorProperty\");}return accessorPropertyNode;}},{key:\"parseObjectProperty\",value:function parseObjectProperty(prop,startLoc,isPattern,refExpressionErrors){var node=_superPropGet(ESTreeParserMixin,\"parseObjectProperty\",this,3)([prop,startLoc,isPattern,refExpressionErrors]);if(node){node.kind=\"init\";this.castNodeTo(node,\"Property\");}return node;}},{key:\"finishObjectProperty\",value:function finishObjectProperty(node){node.kind=\"init\";return this.finishNode(node,\"Property\");}},{key:\"isValidLVal\",value:function isValidLVal(type,isUnparenthesizedInAssign,binding){return type===\"Property\"?\"value\":_superPropGet(ESTreeParserMixin,\"isValidLVal\",this,3)([type,isUnparenthesizedInAssign,binding]);}},{key:\"isAssignable\",value:function isAssignable(node,isBinding){if(node!=null&&this.isObjectProperty(node)){return this.isAssignable(node.value,isBinding);}return _superPropGet(ESTreeParserMixin,\"isAssignable\",this,3)([node,isBinding]);}},{key:\"toAssignable\",value:function toAssignable(node){var isLHS=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;if(node!=null&&this.isObjectProperty(node)){var key=node.key,value=node.value;if(this.isPrivateName(key)){this.classScope.usePrivateName(this.getPrivateNameSV(key),key.loc.start);}this.toAssignable(value,isLHS);}else{_superPropGet(ESTreeParserMixin,\"toAssignable\",this,3)([node,isLHS]);}}},{key:\"toAssignableObjectExpressionProp\",value:function toAssignableObjectExpressionProp(prop,isLast,isLHS){if(prop.type===\"Property\"&&(prop.kind===\"get\"||prop.kind===\"set\")){this.raise(Errors.PatternHasAccessor,prop.key);}else if(prop.type===\"Property\"&&prop.method){this.raise(Errors.PatternHasMethod,prop.key);}else{_superPropGet(ESTreeParserMixin,\"toAssignableObjectExpressionProp\",this,3)([prop,isLast,isLHS]);}}},{key:\"finishCallExpression\",value:function finishCallExpression(unfinished,optional){var node=_superPropGet(ESTreeParserMixin,\"finishCallExpression\",this,3)([unfinished,optional]);if(node.callee.type===\"Import\"){var _ref,_ref2;this.castNodeTo(node,\"ImportExpression\");node.source=node.arguments[0];node.options=(_ref=node.arguments[1])!=null?_ref:null;node.attributes=(_ref2=node.arguments[1])!=null?_ref2:null;delete node.arguments;delete node.callee;}else if(node.type===\"OptionalCallExpression\"){this.castNodeTo(node,\"CallExpression\");}else{node.optional=false;}return node;}},{key:\"toReferencedArguments\",value:function toReferencedArguments(node){if(node.type===\"ImportExpression\"){return;}_superPropGet(ESTreeParserMixin,\"toReferencedArguments\",this,3)([node]);}},{key:\"parseExport\",value:function parseExport(unfinished,decorators){var exportStartLoc=this.state.lastTokStartLoc;var node=_superPropGet(ESTreeParserMixin,\"parseExport\",this,3)([unfinished,decorators]);switch(node.type){case\"ExportAllDeclaration\":node.exported=null;break;case\"ExportNamedDeclaration\":if(node.specifiers.length===1&&node.specifiers[0].type===\"ExportNamespaceSpecifier\"){this.castNodeTo(node,\"ExportAllDeclaration\");node.exported=node.specifiers[0].exported;delete node.specifiers;}case\"ExportDefaultDeclaration\":{var _declaration$decorato;var declaration=node.declaration;if((declaration==null?void 0:declaration.type)===\"ClassDeclaration\"&&((_declaration$decorato=declaration.decorators)==null?void 0:_declaration$decorato.length)>0&&declaration.start===node.start){this.resetStartLocation(node,exportStartLoc);}}break;}return node;}},{key:\"stopParseSubscript\",value:function stopParseSubscript(base,state){var node=_superPropGet(ESTreeParserMixin,\"stopParseSubscript\",this,3)([base,state]);if(state.optionalChainMember){return this.estreeParseChainExpression(node,base.loc.end);}return node;}},{key:\"parseMember\",value:function parseMember(base,startLoc,state,computed,optional){var node=_superPropGet(ESTreeParserMixin,\"parseMember\",this,3)([base,startLoc,state,computed,optional]);if(node.type===\"OptionalMemberExpression\"){this.castNodeTo(node,\"MemberExpression\");}else{node.optional=false;}return node;}},{key:\"isOptionalMemberExpression\",value:function isOptionalMemberExpression(node){if(node.type===\"ChainExpression\"){return node.expression.type===\"MemberExpression\";}return _superPropGet(ESTreeParserMixin,\"isOptionalMemberExpression\",this,3)([node]);}},{key:\"hasPropertyAsPrivateName\",value:function hasPropertyAsPrivateName(node){if(node.type===\"ChainExpression\"){node=node.expression;}return _superPropGet(ESTreeParserMixin,\"hasPropertyAsPrivateName\",this,3)([node]);}},{key:\"isObjectProperty\",value:function isObjectProperty(node){return node.type===\"Property\"&&node.kind===\"init\"&&!node.method;}},{key:\"isObjectMethod\",value:function isObjectMethod(node){return node.type===\"Property\"&&(node.method||node.kind===\"get\"||node.kind===\"set\");}},{key:\"castNodeTo\",value:function castNodeTo(node,type){var result=_superPropGet(ESTreeParserMixin,\"castNodeTo\",this,3)([node,type]);this.fillOptionalPropertiesForTSESLint(result);return result;}},{key:\"cloneIdentifier\",value:function cloneIdentifier(node){var cloned=_superPropGet(ESTreeParserMixin,\"cloneIdentifier\",this,3)([node]);this.fillOptionalPropertiesForTSESLint(cloned);return cloned;}},{key:\"cloneStringLiteral\",value:function cloneStringLiteral(node){if(node.type===\"Literal\"){return this.cloneEstreeStringLiteral(node);}return _superPropGet(ESTreeParserMixin,\"cloneStringLiteral\",this,3)([node]);}},{key:\"finishNodeAt\",value:function finishNodeAt(node,type,endLoc){return toESTreeLocation(_superPropGet(ESTreeParserMixin,\"finishNodeAt\",this,3)([node,type,endLoc]));}},{key:\"finishNode\",value:function finishNode(node,type){var result=_superPropGet(ESTreeParserMixin,\"finishNode\",this,3)([node,type]);this.fillOptionalPropertiesForTSESLint(result);return result;}},{key:\"resetStartLocation\",value:function resetStartLocation(node,startLoc){_superPropGet(ESTreeParserMixin,\"resetStartLocation\",this,3)([node,startLoc]);toESTreeLocation(node);}},{key:\"resetEndLocation\",value:function resetEndLocation(node){var endLoc=arguments.length>1&&arguments[1]!==undefined?arguments[1]:this.state.lastTokEndLoc;_superPropGet(ESTreeParserMixin,\"resetEndLocation\",this,3)([node,endLoc]);toESTreeLocation(node);}}]);}(superClass);};var TokContext=/*#__PURE__*/_createClass(function TokContext(token,preserveSpace){_classCallCheck(this,TokContext);this.token=void 0;this.preserveSpace=void 0;this.token=token;this.preserveSpace=!!preserveSpace;});var types={brace:new TokContext(\"{\"),j_oTag:new TokContext(\"<tag\"),j_cTag:new TokContext(\"</tag\"),j_expr:new TokContext(\"<tag>...</tag>\",true)};{types.template=new TokContext(\"`\",true);}var beforeExpr=true;var startsExpr=true;var isLoop=true;var isAssign=true;var prefix=true;var postfix=true;var ExportedTokenType=/*#__PURE__*/_createClass(function ExportedTokenType(label){var conf=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};_classCallCheck(this,ExportedTokenType);this.label=void 0;this.keyword=void 0;this.beforeExpr=void 0;this.startsExpr=void 0;this.rightAssociative=void 0;this.isLoop=void 0;this.isAssign=void 0;this.prefix=void 0;this.postfix=void 0;this.binop=void 0;this.label=label;this.keyword=conf.keyword;this.beforeExpr=!!conf.beforeExpr;this.startsExpr=!!conf.startsExpr;this.rightAssociative=!!conf.rightAssociative;this.isLoop=!!conf.isLoop;this.isAssign=!!conf.isAssign;this.prefix=!!conf.prefix;this.postfix=!!conf.postfix;this.binop=conf.binop!=null?conf.binop:null;{this.updateContext=null;}});var keywords$1=new Map();function createKeyword(name){var options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};options.keyword=name;var token=createToken(name,options);keywords$1.set(name,token);return token;}function createBinop(name,binop){return createToken(name,{beforeExpr:beforeExpr,binop:binop});}var tokenTypeCounter=-1;var tokenTypes=[];var tokenLabels=[];var tokenBinops=[];var tokenBeforeExprs=[];var tokenStartsExprs=[];var tokenPrefixes=[];function createToken(name){var options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};var _options$binop,_options$beforeExpr,_options$startsExpr,_options$prefix;++tokenTypeCounter;tokenLabels.push(name);tokenBinops.push((_options$binop=options.binop)!=null?_options$binop:-1);tokenBeforeExprs.push((_options$beforeExpr=options.beforeExpr)!=null?_options$beforeExpr:false);tokenStartsExprs.push((_options$startsExpr=options.startsExpr)!=null?_options$startsExpr:false);tokenPrefixes.push((_options$prefix=options.prefix)!=null?_options$prefix:false);tokenTypes.push(new ExportedTokenType(name,options));return tokenTypeCounter;}function createKeywordLike(name){var options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};var _options$binop2,_options$beforeExpr2,_options$startsExpr2,_options$prefix2;++tokenTypeCounter;keywords$1.set(name,tokenTypeCounter);tokenLabels.push(name);tokenBinops.push((_options$binop2=options.binop)!=null?_options$binop2:-1);tokenBeforeExprs.push((_options$beforeExpr2=options.beforeExpr)!=null?_options$beforeExpr2:false);tokenStartsExprs.push((_options$startsExpr2=options.startsExpr)!=null?_options$startsExpr2:false);tokenPrefixes.push((_options$prefix2=options.prefix)!=null?_options$prefix2:false);tokenTypes.push(new ExportedTokenType(\"name\",options));return tokenTypeCounter;}var tt={bracketL:createToken(\"[\",{beforeExpr:beforeExpr,startsExpr:startsExpr}),bracketHashL:createToken(\"#[\",{beforeExpr:beforeExpr,startsExpr:startsExpr}),bracketBarL:createToken(\"[|\",{beforeExpr:beforeExpr,startsExpr:startsExpr}),bracketR:createToken(\"]\"),bracketBarR:createToken(\"|]\"),braceL:createToken(\"{\",{beforeExpr:beforeExpr,startsExpr:startsExpr}),braceBarL:createToken(\"{|\",{beforeExpr:beforeExpr,startsExpr:startsExpr}),braceHashL:createToken(\"#{\",{beforeExpr:beforeExpr,startsExpr:startsExpr}),braceR:createToken(\"}\"),braceBarR:createToken(\"|}\"),parenL:createToken(\"(\",{beforeExpr:beforeExpr,startsExpr:startsExpr}),parenR:createToken(\")\"),comma:createToken(\",\",{beforeExpr:beforeExpr}),semi:createToken(\";\",{beforeExpr:beforeExpr}),colon:createToken(\":\",{beforeExpr:beforeExpr}),doubleColon:createToken(\"::\",{beforeExpr:beforeExpr}),dot:createToken(\".\"),question:createToken(\"?\",{beforeExpr:beforeExpr}),questionDot:createToken(\"?.\"),arrow:createToken(\"=>\",{beforeExpr:beforeExpr}),template:createToken(\"template\"),ellipsis:createToken(\"...\",{beforeExpr:beforeExpr}),backQuote:createToken(\"`\",{startsExpr:startsExpr}),dollarBraceL:createToken(\"${\",{beforeExpr:beforeExpr,startsExpr:startsExpr}),templateTail:createToken(\"...`\",{startsExpr:startsExpr}),templateNonTail:createToken(\"...${\",{beforeExpr:beforeExpr,startsExpr:startsExpr}),at:createToken(\"@\"),hash:createToken(\"#\",{startsExpr:startsExpr}),interpreterDirective:createToken(\"#!...\"),eq:createToken(\"=\",{beforeExpr:beforeExpr,isAssign:isAssign}),assign:createToken(\"_=\",{beforeExpr:beforeExpr,isAssign:isAssign}),slashAssign:createToken(\"_=\",{beforeExpr:beforeExpr,isAssign:isAssign}),xorAssign:createToken(\"_=\",{beforeExpr:beforeExpr,isAssign:isAssign}),moduloAssign:createToken(\"_=\",{beforeExpr:beforeExpr,isAssign:isAssign}),incDec:createToken(\"++/--\",{prefix:prefix,postfix:postfix,startsExpr:startsExpr}),bang:createToken(\"!\",{beforeExpr:beforeExpr,prefix:prefix,startsExpr:startsExpr}),tilde:createToken(\"~\",{beforeExpr:beforeExpr,prefix:prefix,startsExpr:startsExpr}),doubleCaret:createToken(\"^^\",{startsExpr:startsExpr}),doubleAt:createToken(\"@@\",{startsExpr:startsExpr}),pipeline:createBinop(\"|>\",0),nullishCoalescing:createBinop(\"??\",1),logicalOR:createBinop(\"||\",1),logicalAND:createBinop(\"&&\",2),bitwiseOR:createBinop(\"|\",3),bitwiseXOR:createBinop(\"^\",4),bitwiseAND:createBinop(\"&\",5),equality:createBinop(\"==/!=/===/!==\",6),lt:createBinop(\"</>/<=/>=\",7),gt:createBinop(\"</>/<=/>=\",7),relational:createBinop(\"</>/<=/>=\",7),bitShift:createBinop(\"<</>>/>>>\",8),bitShiftL:createBinop(\"<</>>/>>>\",8),bitShiftR:createBinop(\"<</>>/>>>\",8),plusMin:createToken(\"+/-\",{beforeExpr:beforeExpr,binop:9,prefix:prefix,startsExpr:startsExpr}),modulo:createToken(\"%\",{binop:10,startsExpr:startsExpr}),star:createToken(\"*\",{binop:10}),slash:createBinop(\"/\",10),exponent:createToken(\"**\",{beforeExpr:beforeExpr,binop:11,rightAssociative:true}),_in:createKeyword(\"in\",{beforeExpr:beforeExpr,binop:7}),_instanceof:createKeyword(\"instanceof\",{beforeExpr:beforeExpr,binop:7}),_break:createKeyword(\"break\"),_case:createKeyword(\"case\",{beforeExpr:beforeExpr}),_catch:createKeyword(\"catch\"),_continue:createKeyword(\"continue\"),_debugger:createKeyword(\"debugger\"),_default:createKeyword(\"default\",{beforeExpr:beforeExpr}),_else:createKeyword(\"else\",{beforeExpr:beforeExpr}),_finally:createKeyword(\"finally\"),_function:createKeyword(\"function\",{startsExpr:startsExpr}),_if:createKeyword(\"if\"),_return:createKeyword(\"return\",{beforeExpr:beforeExpr}),_switch:createKeyword(\"switch\"),_throw:createKeyword(\"throw\",{beforeExpr:beforeExpr,prefix:prefix,startsExpr:startsExpr}),_try:createKeyword(\"try\"),_var:createKeyword(\"var\"),_const:createKeyword(\"const\"),_with:createKeyword(\"with\"),_new:createKeyword(\"new\",{beforeExpr:beforeExpr,startsExpr:startsExpr}),_this:createKeyword(\"this\",{startsExpr:startsExpr}),_super:createKeyword(\"super\",{startsExpr:startsExpr}),_class:createKeyword(\"class\",{startsExpr:startsExpr}),_extends:createKeyword(\"extends\",{beforeExpr:beforeExpr}),_export:createKeyword(\"export\"),_import:createKeyword(\"import\",{startsExpr:startsExpr}),_null:createKeyword(\"null\",{startsExpr:startsExpr}),_true:createKeyword(\"true\",{startsExpr:startsExpr}),_false:createKeyword(\"false\",{startsExpr:startsExpr}),_typeof:createKeyword(\"typeof\",{beforeExpr:beforeExpr,prefix:prefix,startsExpr:startsExpr}),_void:createKeyword(\"void\",{beforeExpr:beforeExpr,prefix:prefix,startsExpr:startsExpr}),_delete:createKeyword(\"delete\",{beforeExpr:beforeExpr,prefix:prefix,startsExpr:startsExpr}),_do:createKeyword(\"do\",{isLoop:isLoop,beforeExpr:beforeExpr}),_for:createKeyword(\"for\",{isLoop:isLoop}),_while:createKeyword(\"while\",{isLoop:isLoop}),_as:createKeywordLike(\"as\",{startsExpr:startsExpr}),_assert:createKeywordLike(\"assert\",{startsExpr:startsExpr}),_async:createKeywordLike(\"async\",{startsExpr:startsExpr}),_await:createKeywordLike(\"await\",{startsExpr:startsExpr}),_defer:createKeywordLike(\"defer\",{startsExpr:startsExpr}),_from:createKeywordLike(\"from\",{startsExpr:startsExpr}),_get:createKeywordLike(\"get\",{startsExpr:startsExpr}),_let:createKeywordLike(\"let\",{startsExpr:startsExpr}),_meta:createKeywordLike(\"meta\",{startsExpr:startsExpr}),_of:createKeywordLike(\"of\",{startsExpr:startsExpr}),_sent:createKeywordLike(\"sent\",{startsExpr:startsExpr}),_set:createKeywordLike(\"set\",{startsExpr:startsExpr}),_source:createKeywordLike(\"source\",{startsExpr:startsExpr}),_static:createKeywordLike(\"static\",{startsExpr:startsExpr}),_using:createKeywordLike(\"using\",{startsExpr:startsExpr}),_yield:createKeywordLike(\"yield\",{startsExpr:startsExpr}),_asserts:createKeywordLike(\"asserts\",{startsExpr:startsExpr}),_checks:createKeywordLike(\"checks\",{startsExpr:startsExpr}),_exports:createKeywordLike(\"exports\",{startsExpr:startsExpr}),_global:createKeywordLike(\"global\",{startsExpr:startsExpr}),_implements:createKeywordLike(\"implements\",{startsExpr:startsExpr}),_intrinsic:createKeywordLike(\"intrinsic\",{startsExpr:startsExpr}),_infer:createKeywordLike(\"infer\",{startsExpr:startsExpr}),_is:createKeywordLike(\"is\",{startsExpr:startsExpr}),_mixins:createKeywordLike(\"mixins\",{startsExpr:startsExpr}),_proto:createKeywordLike(\"proto\",{startsExpr:startsExpr}),_require:createKeywordLike(\"require\",{startsExpr:startsExpr}),_satisfies:createKeywordLike(\"satisfies\",{startsExpr:startsExpr}),_keyof:createKeywordLike(\"keyof\",{startsExpr:startsExpr}),_readonly:createKeywordLike(\"readonly\",{startsExpr:startsExpr}),_unique:createKeywordLike(\"unique\",{startsExpr:startsExpr}),_abstract:createKeywordLike(\"abstract\",{startsExpr:startsExpr}),_declare:createKeywordLike(\"declare\",{startsExpr:startsExpr}),_enum:createKeywordLike(\"enum\",{startsExpr:startsExpr}),_module:createKeywordLike(\"module\",{startsExpr:startsExpr}),_namespace:createKeywordLike(\"namespace\",{startsExpr:startsExpr}),_interface:createKeywordLike(\"interface\",{startsExpr:startsExpr}),_type:createKeywordLike(\"type\",{startsExpr:startsExpr}),_opaque:createKeywordLike(\"opaque\",{startsExpr:startsExpr}),name:createToken(\"name\",{startsExpr:startsExpr}),placeholder:createToken(\"%%\",{startsExpr:startsExpr}),string:createToken(\"string\",{startsExpr:startsExpr}),num:createToken(\"num\",{startsExpr:startsExpr}),bigint:createToken(\"bigint\",{startsExpr:startsExpr}),decimal:createToken(\"decimal\",{startsExpr:startsExpr}),regexp:createToken(\"regexp\",{startsExpr:startsExpr}),privateName:createToken(\"#name\",{startsExpr:startsExpr}),eof:createToken(\"eof\"),jsxName:createToken(\"jsxName\"),jsxText:createToken(\"jsxText\",{beforeExpr:beforeExpr}),jsxTagStart:createToken(\"jsxTagStart\",{startsExpr:startsExpr}),jsxTagEnd:createToken(\"jsxTagEnd\")};function tokenIsIdentifier(token){return token>=93&&token<=133;}function tokenKeywordOrIdentifierIsKeyword(token){return token<=92;}function tokenIsKeywordOrIdentifier(token){return token>=58&&token<=133;}function tokenIsLiteralPropertyName(token){return token>=58&&token<=137;}function tokenComesBeforeExpression(token){return tokenBeforeExprs[token];}function tokenCanStartExpression(token){return tokenStartsExprs[token];}function tokenIsAssignment(token){return token>=29&&token<=33;}function tokenIsFlowInterfaceOrTypeOrOpaque(token){return token>=129&&token<=131;}function tokenIsLoop(token){return token>=90&&token<=92;}function tokenIsKeyword(token){return token>=58&&token<=92;}function tokenIsOperator(token){return token>=39&&token<=59;}function tokenIsPostfix(token){return token===34;}function tokenIsPrefix(token){return tokenPrefixes[token];}function tokenIsTSTypeOperator(token){return token>=121&&token<=123;}function tokenIsTSDeclarationStart(token){return token>=124&&token<=130;}function tokenLabelName(token){return tokenLabels[token];}function tokenOperatorPrecedence(token){return tokenBinops[token];}function tokenIsRightAssociative(token){return token===57;}function tokenIsTemplate(token){return token>=24&&token<=25;}function getExportedToken(token){return tokenTypes[token];}{tokenTypes[8].updateContext=function(context){context.pop();};tokenTypes[5].updateContext=tokenTypes[7].updateContext=tokenTypes[23].updateContext=function(context){context.push(types.brace);};tokenTypes[22].updateContext=function(context){if(context[context.length-1]===types.template){context.pop();}else{context.push(types.template);}};tokenTypes[143].updateContext=function(context){context.push(types.j_expr,types.j_oTag);};}var nonASCIIidentifierStartChars=\"\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u0870-\\u0887\\u0889-\\u088E\\u08A0-\\u08C9\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C5D\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D04-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E86-\\u0E8A\\u0E8C-\\u0EA3\\u0EA5\\u0EA7-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u1711\\u171F-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1878\\u1880-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4C\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C8A\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CE9-\\u1CEC\\u1CEE-\\u1CF3\\u1CF5\\u1CF6\\u1CFA\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2118-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309B-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BF\\u31F0-\\u31FF\\u3400-\\u4DBF\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7CD\\uA7D0\\uA7D1\\uA7D3\\uA7D5-\\uA7DC\\uA7F2-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA8FE\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB69\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC\";var nonASCIIidentifierChars=\"\\xB7\\u0300-\\u036F\\u0387\\u0483-\\u0487\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u0610-\\u061A\\u064B-\\u0669\\u0670\\u06D6-\\u06DC\\u06DF-\\u06E4\\u06E7\\u06E8\\u06EA-\\u06ED\\u06F0-\\u06F9\\u0711\\u0730-\\u074A\\u07A6-\\u07B0\\u07C0-\\u07C9\\u07EB-\\u07F3\\u07FD\\u0816-\\u0819\\u081B-\\u0823\\u0825-\\u0827\\u0829-\\u082D\\u0859-\\u085B\\u0897-\\u089F\\u08CA-\\u08E1\\u08E3-\\u0903\\u093A-\\u093C\\u093E-\\u094F\\u0951-\\u0957\\u0962\\u0963\\u0966-\\u096F\\u0981-\\u0983\\u09BC\\u09BE-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CD\\u09D7\\u09E2\\u09E3\\u09E6-\\u09EF\\u09FE\\u0A01-\\u0A03\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A66-\\u0A71\\u0A75\\u0A81-\\u0A83\\u0ABC\\u0ABE-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AE2\\u0AE3\\u0AE6-\\u0AEF\\u0AFA-\\u0AFF\\u0B01-\\u0B03\\u0B3C\\u0B3E-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B55-\\u0B57\\u0B62\\u0B63\\u0B66-\\u0B6F\\u0B82\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD7\\u0BE6-\\u0BEF\\u0C00-\\u0C04\\u0C3C\\u0C3E-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C62\\u0C63\\u0C66-\\u0C6F\\u0C81-\\u0C83\\u0CBC\\u0CBE-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CE2\\u0CE3\\u0CE6-\\u0CEF\\u0CF3\\u0D00-\\u0D03\\u0D3B\\u0D3C\\u0D3E-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4D\\u0D57\\u0D62\\u0D63\\u0D66-\\u0D6F\\u0D81-\\u0D83\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DE6-\\u0DEF\\u0DF2\\u0DF3\\u0E31\\u0E34-\\u0E3A\\u0E47-\\u0E4E\\u0E50-\\u0E59\\u0EB1\\u0EB4-\\u0EBC\\u0EC8-\\u0ECE\\u0ED0-\\u0ED9\\u0F18\\u0F19\\u0F20-\\u0F29\\u0F35\\u0F37\\u0F39\\u0F3E\\u0F3F\\u0F71-\\u0F84\\u0F86\\u0F87\\u0F8D-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u102B-\\u103E\\u1040-\\u1049\\u1056-\\u1059\\u105E-\\u1060\\u1062-\\u1064\\u1067-\\u106D\\u1071-\\u1074\\u1082-\\u108D\\u108F-\\u109D\\u135D-\\u135F\\u1369-\\u1371\\u1712-\\u1715\\u1732-\\u1734\\u1752\\u1753\\u1772\\u1773\\u17B4-\\u17D3\\u17DD\\u17E0-\\u17E9\\u180B-\\u180D\\u180F-\\u1819\\u18A9\\u1920-\\u192B\\u1930-\\u193B\\u1946-\\u194F\\u19D0-\\u19DA\\u1A17-\\u1A1B\\u1A55-\\u1A5E\\u1A60-\\u1A7C\\u1A7F-\\u1A89\\u1A90-\\u1A99\\u1AB0-\\u1ABD\\u1ABF-\\u1ACE\\u1B00-\\u1B04\\u1B34-\\u1B44\\u1B50-\\u1B59\\u1B6B-\\u1B73\\u1B80-\\u1B82\\u1BA1-\\u1BAD\\u1BB0-\\u1BB9\\u1BE6-\\u1BF3\\u1C24-\\u1C37\\u1C40-\\u1C49\\u1C50-\\u1C59\\u1CD0-\\u1CD2\\u1CD4-\\u1CE8\\u1CED\\u1CF4\\u1CF7-\\u1CF9\\u1DC0-\\u1DFF\\u200C\\u200D\\u203F\\u2040\\u2054\\u20D0-\\u20DC\\u20E1\\u20E5-\\u20F0\\u2CEF-\\u2CF1\\u2D7F\\u2DE0-\\u2DFF\\u302A-\\u302F\\u3099\\u309A\\u30FB\\uA620-\\uA629\\uA66F\\uA674-\\uA67D\\uA69E\\uA69F\\uA6F0\\uA6F1\\uA802\\uA806\\uA80B\\uA823-\\uA827\\uA82C\\uA880\\uA881\\uA8B4-\\uA8C5\\uA8D0-\\uA8D9\\uA8E0-\\uA8F1\\uA8FF-\\uA909\\uA926-\\uA92D\\uA947-\\uA953\\uA980-\\uA983\\uA9B3-\\uA9C0\\uA9D0-\\uA9D9\\uA9E5\\uA9F0-\\uA9F9\\uAA29-\\uAA36\\uAA43\\uAA4C\\uAA4D\\uAA50-\\uAA59\\uAA7B-\\uAA7D\\uAAB0\\uAAB2-\\uAAB4\\uAAB7\\uAAB8\\uAABE\\uAABF\\uAAC1\\uAAEB-\\uAAEF\\uAAF5\\uAAF6\\uABE3-\\uABEA\\uABEC\\uABED\\uABF0-\\uABF9\\uFB1E\\uFE00-\\uFE0F\\uFE20-\\uFE2F\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFF10-\\uFF19\\uFF3F\\uFF65\";var nonASCIIidentifierStart=new RegExp(\"[\"+nonASCIIidentifierStartChars+\"]\");var nonASCIIidentifier=new RegExp(\"[\"+nonASCIIidentifierStartChars+nonASCIIidentifierChars+\"]\");nonASCIIidentifierStartChars=nonASCIIidentifierChars=null;var astralIdentifierStartCodes=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,4,51,13,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,39,27,10,22,251,41,7,1,17,2,60,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,31,9,2,0,3,0,2,37,2,0,26,0,2,0,45,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,200,32,32,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,26,3994,6,582,6842,29,1763,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,433,44,212,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,42,9,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,229,29,3,0,496,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191];var astralIdentifierCodes=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,7,9,32,4,318,1,80,3,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,68,8,2,0,3,0,2,3,2,4,2,0,15,1,83,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,7,19,58,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,343,9,54,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,10,5350,0,7,14,11465,27,2343,9,87,9,39,4,60,6,26,9,535,9,470,0,2,54,8,3,82,0,12,1,19628,1,4178,9,519,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,245,1,2,9,726,6,110,6,6,9,4759,9,787719,239];function isInAstralSet(code,set){var pos=0x10000;for(var i=0,length=set.length;i<length;i+=2){pos+=set[i];if(pos>code)return false;pos+=set[i+1];if(pos>=code)return true;}return false;}function isIdentifierStart(code){if(code<65)return code===36;if(code<=90)return true;if(code<97)return code===95;if(code<=122)return true;if(code<=0xffff){return code>=0xaa&&nonASCIIidentifierStart.test(String.fromCharCode(code));}return isInAstralSet(code,astralIdentifierStartCodes);}function isIdentifierChar(code){if(code<48)return code===36;if(code<58)return true;if(code<65)return false;if(code<=90)return true;if(code<97)return code===95;if(code<=122)return true;if(code<=0xffff){return code>=0xaa&&nonASCIIidentifier.test(String.fromCharCode(code));}return isInAstralSet(code,astralIdentifierStartCodes)||isInAstralSet(code,astralIdentifierCodes);}var reservedWords={keyword:[\"break\",\"case\",\"catch\",\"continue\",\"debugger\",\"default\",\"do\",\"else\",\"finally\",\"for\",\"function\",\"if\",\"return\",\"switch\",\"throw\",\"try\",\"var\",\"const\",\"while\",\"with\",\"new\",\"this\",\"super\",\"class\",\"extends\",\"export\",\"import\",\"null\",\"true\",\"false\",\"in\",\"instanceof\",\"typeof\",\"void\",\"delete\"],strict:[\"implements\",\"interface\",\"let\",\"package\",\"private\",\"protected\",\"public\",\"static\",\"yield\"],strictBind:[\"eval\",\"arguments\"]};var keywords=new Set(reservedWords.keyword);var reservedWordsStrictSet=new Set(reservedWords.strict);var reservedWordsStrictBindSet=new Set(reservedWords.strictBind);function isReservedWord(word,inModule){return inModule&&word===\"await\"||word===\"enum\";}function isStrictReservedWord(word,inModule){return isReservedWord(word,inModule)||reservedWordsStrictSet.has(word);}function isStrictBindOnlyReservedWord(word){return reservedWordsStrictBindSet.has(word);}function isStrictBindReservedWord(word,inModule){return isStrictReservedWord(word,inModule)||isStrictBindOnlyReservedWord(word);}function isKeyword(word){return keywords.has(word);}function isIteratorStart(current,next,next2){return current===64&&next===64&&isIdentifierStart(next2);}var reservedWordLikeSet=new Set([\"break\",\"case\",\"catch\",\"continue\",\"debugger\",\"default\",\"do\",\"else\",\"finally\",\"for\",\"function\",\"if\",\"return\",\"switch\",\"throw\",\"try\",\"var\",\"const\",\"while\",\"with\",\"new\",\"this\",\"super\",\"class\",\"extends\",\"export\",\"import\",\"null\",\"true\",\"false\",\"in\",\"instanceof\",\"typeof\",\"void\",\"delete\",\"implements\",\"interface\",\"let\",\"package\",\"private\",\"protected\",\"public\",\"static\",\"yield\",\"eval\",\"arguments\",\"enum\",\"await\"]);function canBeReservedWord(word){return reservedWordLikeSet.has(word);}var Scope=/*#__PURE__*/_createClass(function Scope(flags){_classCallCheck(this,Scope);this.flags=0;this.names=new Map();this.firstLexicalName=\"\";this.flags=flags;});var ScopeHandler=/*#__PURE__*/function(){function ScopeHandler(parser,inModule){_classCallCheck(this,ScopeHandler);this.parser=void 0;this.scopeStack=[];this.inModule=void 0;this.undefinedExports=new Map();this.parser=parser;this.inModule=inModule;}return _createClass(ScopeHandler,[{key:\"inTopLevel\",get:function get(){return(this.currentScope().flags&1)>0;}},{key:\"inFunction\",get:function get(){return(this.currentVarScopeFlags()&2)>0;}},{key:\"allowSuper\",get:function get(){return(this.currentThisScopeFlags()&16)>0;}},{key:\"allowDirectSuper\",get:function get(){return(this.currentThisScopeFlags()&32)>0;}},{key:\"allowNewTarget\",get:function get(){return(this.currentThisScopeFlags()&512)>0;}},{key:\"inClass\",get:function get(){return(this.currentThisScopeFlags()&64)>0;}},{key:\"inClassAndNotInNonArrowFunction\",get:function get(){var flags=this.currentThisScopeFlags();return(flags&64)>0&&(flags&2)===0;}},{key:\"inStaticBlock\",get:function get(){for(var i=this.scopeStack.length-1;;i--){var flags=this.scopeStack[i].flags;if(flags&128){return true;}if(flags&(1667|64)){return false;}}}},{key:\"inNonArrowFunction\",get:function get(){return(this.currentThisScopeFlags()&2)>0;}},{key:\"inBareCaseStatement\",get:function get(){return(this.currentScope().flags&256)>0;}},{key:\"treatFunctionsAsVar\",get:function get(){return this.treatFunctionsAsVarInScope(this.currentScope());}},{key:\"createScope\",value:function createScope(flags){return new Scope(flags);}},{key:\"enter\",value:function enter(flags){this.scopeStack.push(this.createScope(flags));}},{key:\"exit\",value:function exit(){var scope=this.scopeStack.pop();return scope.flags;}},{key:\"treatFunctionsAsVarInScope\",value:function treatFunctionsAsVarInScope(scope){return!!(scope.flags&(2|128)||!this.parser.inModule&&scope.flags&1);}},{key:\"declareName\",value:function declareName(name,bindingType,loc){var scope=this.currentScope();if(bindingType&8||bindingType&16){this.checkRedeclarationInScope(scope,name,bindingType,loc);var type=scope.names.get(name)||0;if(bindingType&16){type=type|4;}else{if(!scope.firstLexicalName){scope.firstLexicalName=name;}type=type|2;}scope.names.set(name,type);if(bindingType&8){this.maybeExportDefined(scope,name);}}else if(bindingType&4){for(var i=this.scopeStack.length-1;i>=0;--i){scope=this.scopeStack[i];this.checkRedeclarationInScope(scope,name,bindingType,loc);scope.names.set(name,(scope.names.get(name)||0)|1);this.maybeExportDefined(scope,name);if(scope.flags&1667)break;}}if(this.parser.inModule&&scope.flags&1){this.undefinedExports.delete(name);}}},{key:\"maybeExportDefined\",value:function maybeExportDefined(scope,name){if(this.parser.inModule&&scope.flags&1){this.undefinedExports.delete(name);}}},{key:\"checkRedeclarationInScope\",value:function checkRedeclarationInScope(scope,name,bindingType,loc){if(this.isRedeclaredInScope(scope,name,bindingType)){this.parser.raise(Errors.VarRedeclaration,loc,{identifierName:name});}}},{key:\"isRedeclaredInScope\",value:function isRedeclaredInScope(scope,name,bindingType){if(!(bindingType&1))return false;if(bindingType&8){return scope.names.has(name);}var type=scope.names.get(name);if(bindingType&16){return(type&2)>0||!this.treatFunctionsAsVarInScope(scope)&&(type&1)>0;}return(type&2)>0&&!(scope.flags&8&&scope.firstLexicalName===name)||!this.treatFunctionsAsVarInScope(scope)&&(type&4)>0;}},{key:\"checkLocalExport\",value:function checkLocalExport(id){var name=id.name;var topLevelScope=this.scopeStack[0];if(!topLevelScope.names.has(name)){this.undefinedExports.set(name,id.loc.start);}}},{key:\"currentScope\",value:function currentScope(){return this.scopeStack[this.scopeStack.length-1];}},{key:\"currentVarScopeFlags\",value:function currentVarScopeFlags(){for(var i=this.scopeStack.length-1;;i--){var flags=this.scopeStack[i].flags;if(flags&1667){return flags;}}}},{key:\"currentThisScopeFlags\",value:function currentThisScopeFlags(){for(var i=this.scopeStack.length-1;;i--){var flags=this.scopeStack[i].flags;if(flags&(1667|64)&&!(flags&4)){return flags;}}}}]);}();var FlowScope=/*#__PURE__*/function(_Scope){function FlowScope(){var _this3;_classCallCheck(this,FlowScope);for(var _len3=arguments.length,args=new Array(_len3),_key3=0;_key3<_len3;_key3++){args[_key3]=arguments[_key3];}_this3=_callSuper(this,FlowScope,[].concat(args));_this3.declareFunctions=new Set();return _this3;}_inherits(FlowScope,_Scope);return _createClass(FlowScope);}(Scope);var FlowScopeHandler=/*#__PURE__*/function(_ScopeHandler){function FlowScopeHandler(){_classCallCheck(this,FlowScopeHandler);return _callSuper(this,FlowScopeHandler,arguments);}_inherits(FlowScopeHandler,_ScopeHandler);return _createClass(FlowScopeHandler,[{key:\"createScope\",value:function createScope(flags){return new FlowScope(flags);}},{key:\"declareName\",value:function declareName(name,bindingType,loc){var scope=this.currentScope();if(bindingType&2048){this.checkRedeclarationInScope(scope,name,bindingType,loc);this.maybeExportDefined(scope,name);scope.declareFunctions.add(name);return;}_superPropGet(FlowScopeHandler,\"declareName\",this,3)([name,bindingType,loc]);}},{key:\"isRedeclaredInScope\",value:function isRedeclaredInScope(scope,name,bindingType){if(_superPropGet(FlowScopeHandler,\"isRedeclaredInScope\",this,3)([scope,name,bindingType]))return true;if(bindingType&2048&&!scope.declareFunctions.has(name)){var type=scope.names.get(name);return(type&4)>0||(type&2)>0;}return false;}},{key:\"checkLocalExport\",value:function checkLocalExport(id){if(!this.scopeStack[0].declareFunctions.has(id.name)){_superPropGet(FlowScopeHandler,\"checkLocalExport\",this,3)([id]);}}}]);}(ScopeHandler);var reservedTypes=new Set([\"_\",\"any\",\"bool\",\"boolean\",\"empty\",\"extends\",\"false\",\"interface\",\"mixed\",\"null\",\"number\",\"static\",\"string\",\"true\",\"typeof\",\"void\"]);var FlowErrors=ParseErrorEnum(_templateObject2||(_templateObject2=_taggedTemplateLiteral([\"flow\"])))({AmbiguousConditionalArrow:\"Ambiguous expression: wrap the arrow functions in parentheses to disambiguate.\",AmbiguousDeclareModuleKind:\"Found both `declare module.exports` and `declare export` in the same module. Modules can only have 1 since they are either an ES module or they are a CommonJS module.\",AssignReservedType:function AssignReservedType(_ref37){var reservedType=_ref37.reservedType;return\"Cannot overwrite reserved type \".concat(reservedType,\".\");},DeclareClassElement:\"The `declare` modifier can only appear on class fields.\",DeclareClassFieldInitializer:\"Initializers are not allowed in fields with the `declare` modifier.\",DuplicateDeclareModuleExports:\"Duplicate `declare module.exports` statement.\",EnumBooleanMemberNotInitialized:function EnumBooleanMemberNotInitialized(_ref38){var memberName=_ref38.memberName,enumName=_ref38.enumName;return\"Boolean enum members need to be initialized. Use either `\".concat(memberName,\" = true,` or `\").concat(memberName,\" = false,` in enum `\").concat(enumName,\"`.\");},EnumDuplicateMemberName:function EnumDuplicateMemberName(_ref39){var memberName=_ref39.memberName,enumName=_ref39.enumName;return\"Enum member names need to be unique, but the name `\".concat(memberName,\"` has already been used before in enum `\").concat(enumName,\"`.\");},EnumInconsistentMemberValues:function EnumInconsistentMemberValues(_ref40){var enumName=_ref40.enumName;return\"Enum `\".concat(enumName,\"` has inconsistent member initializers. Either use no initializers, or consistently use literals (either booleans, numbers, or strings) for all member initializers.\");},EnumInvalidExplicitType:function EnumInvalidExplicitType(_ref41){var invalidEnumType=_ref41.invalidEnumType,enumName=_ref41.enumName;return\"Enum type `\".concat(invalidEnumType,\"` is not valid. Use one of `boolean`, `number`, `string`, or `symbol` in enum `\").concat(enumName,\"`.\");},EnumInvalidExplicitTypeUnknownSupplied:function EnumInvalidExplicitTypeUnknownSupplied(_ref42){var enumName=_ref42.enumName;return\"Supplied enum type is not valid. Use one of `boolean`, `number`, `string`, or `symbol` in enum `\".concat(enumName,\"`.\");},EnumInvalidMemberInitializerPrimaryType:function EnumInvalidMemberInitializerPrimaryType(_ref43){var enumName=_ref43.enumName,memberName=_ref43.memberName,explicitType=_ref43.explicitType;return\"Enum `\".concat(enumName,\"` has type `\").concat(explicitType,\"`, so the initializer of `\").concat(memberName,\"` needs to be a \").concat(explicitType,\" literal.\");},EnumInvalidMemberInitializerSymbolType:function EnumInvalidMemberInitializerSymbolType(_ref44){var enumName=_ref44.enumName,memberName=_ref44.memberName;return\"Symbol enum members cannot be initialized. Use `\".concat(memberName,\",` in enum `\").concat(enumName,\"`.\");},EnumInvalidMemberInitializerUnknownType:function EnumInvalidMemberInitializerUnknownType(_ref45){var enumName=_ref45.enumName,memberName=_ref45.memberName;return\"The enum member initializer for `\".concat(memberName,\"` needs to be a literal (either a boolean, number, or string) in enum `\").concat(enumName,\"`.\");},EnumInvalidMemberName:function EnumInvalidMemberName(_ref46){var enumName=_ref46.enumName,memberName=_ref46.memberName,suggestion=_ref46.suggestion;return\"Enum member names cannot start with lowercase 'a' through 'z'. Instead of using `\".concat(memberName,\"`, consider using `\").concat(suggestion,\"`, in enum `\").concat(enumName,\"`.\");},EnumNumberMemberNotInitialized:function EnumNumberMemberNotInitialized(_ref47){var enumName=_ref47.enumName,memberName=_ref47.memberName;return\"Number enum members need to be initialized, e.g. `\".concat(memberName,\" = 1` in enum `\").concat(enumName,\"`.\");},EnumStringMemberInconsistentlyInitialized:function EnumStringMemberInconsistentlyInitialized(_ref48){var enumName=_ref48.enumName;return\"String enum members need to consistently either all use initializers, or use no initializers, in enum `\".concat(enumName,\"`.\");},GetterMayNotHaveThisParam:\"A getter cannot have a `this` parameter.\",ImportReflectionHasImportType:\"An `import module` declaration can not use `type` or `typeof` keyword.\",ImportTypeShorthandOnlyInPureImport:\"The `type` and `typeof` keywords on named imports can only be used on regular `import` statements. It cannot be used with `import type` or `import typeof` statements.\",InexactInsideExact:\"Explicit inexact syntax cannot appear inside an explicit exact object type.\",InexactInsideNonObject:\"Explicit inexact syntax cannot appear in class or interface definitions.\",InexactVariance:\"Explicit inexact syntax cannot have variance.\",InvalidNonTypeImportInDeclareModule:\"Imports within a `declare module` body must always be `import type` or `import typeof`.\",MissingTypeParamDefault:\"Type parameter declaration needs a default, since a preceding type parameter declaration has a default.\",NestedDeclareModule:\"`declare module` cannot be used inside another `declare module`.\",NestedFlowComment:\"Cannot have a flow comment inside another flow comment.\",PatternIsOptional:Object.assign({message:\"A binding pattern parameter cannot be optional in an implementation signature.\"},{reasonCode:\"OptionalBindingPattern\"}),SetterMayNotHaveThisParam:\"A setter cannot have a `this` parameter.\",SpreadVariance:\"Spread properties cannot have variance.\",ThisParamAnnotationRequired:\"A type annotation is required for the `this` parameter.\",ThisParamBannedInConstructor:\"Constructors cannot have a `this` parameter; constructors don't bind `this` like other functions.\",ThisParamMayNotBeOptional:\"The `this` parameter cannot be optional.\",ThisParamMustBeFirst:\"The `this` parameter must be the first function parameter.\",ThisParamNoDefault:\"The `this` parameter may not have a default value.\",TypeBeforeInitializer:\"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.\",TypeCastInPattern:\"The type cast expression is expected to be wrapped with parenthesis.\",UnexpectedExplicitInexactInObject:\"Explicit inexact syntax must appear at the end of an inexact object.\",UnexpectedReservedType:function UnexpectedReservedType(_ref49){var reservedType=_ref49.reservedType;return\"Unexpected reserved type \".concat(reservedType,\".\");},UnexpectedReservedUnderscore:\"`_` is only allowed as a type argument to call or new.\",UnexpectedSpaceBetweenModuloChecks:\"Spaces between `%` and `checks` are not allowed here.\",UnexpectedSpreadType:\"Spread operator cannot appear in class or interface definitions.\",UnexpectedSubtractionOperand:'Unexpected token, expected \"number\" or \"bigint\".',UnexpectedTokenAfterTypeParameter:\"Expected an arrow function after this type parameter declaration.\",UnexpectedTypeParameterBeforeAsyncArrowFunction:\"Type parameters must come after the async keyword, e.g. instead of `<T> async () => {}`, use `async <T>() => {}`.\",UnsupportedDeclareExportKind:function UnsupportedDeclareExportKind(_ref50){var unsupportedExportKind=_ref50.unsupportedExportKind,suggestion=_ref50.suggestion;return\"`declare export \".concat(unsupportedExportKind,\"` is not supported. Use `\").concat(suggestion,\"` instead.\");},UnsupportedStatementInDeclareModule:\"Only declares and type imports are allowed inside declare module.\",UnterminatedFlowComment:\"Unterminated flow-comment.\"});function isEsModuleType(bodyElement){return bodyElement.type===\"DeclareExportAllDeclaration\"||bodyElement.type===\"DeclareExportDeclaration\"&&(!bodyElement.declaration||bodyElement.declaration.type!==\"TypeAlias\"&&bodyElement.declaration.type!==\"InterfaceDeclaration\");}function hasTypeImportKind(node){return node.importKind===\"type\"||node.importKind===\"typeof\";}var exportSuggestions={const:\"declare export var\",let:\"declare export var\",type:\"export type\",interface:\"export interface\"};function partition(list,test){var list1=[];var list2=[];for(var i=0;i<list.length;i++){(test(list[i],i,list)?list1:list2).push(list[i]);}return[list1,list2];}var FLOW_PRAGMA_REGEX=/\\*?\\s*@((?:no)?flow)\\b/;var flow=function flow(superClass){return/*#__PURE__*/function(_superClass2){function FlowParserMixin(){var _this4;_classCallCheck(this,FlowParserMixin);for(var _len4=arguments.length,args=new Array(_len4),_key4=0;_key4<_len4;_key4++){args[_key4]=arguments[_key4];}_this4=_callSuper(this,FlowParserMixin,[].concat(args));_this4.flowPragma=undefined;return _this4;}_inherits(FlowParserMixin,_superClass2);return _createClass(FlowParserMixin,[{key:\"getScopeHandler\",value:function getScopeHandler(){return FlowScopeHandler;}},{key:\"shouldParseTypes\",value:function shouldParseTypes(){return this.getPluginOption(\"flow\",\"all\")||this.flowPragma===\"flow\";}},{key:\"finishToken\",value:function finishToken(type,val){if(type!==134&&type!==13&&type!==28){if(this.flowPragma===undefined){this.flowPragma=null;}}_superPropGet(FlowParserMixin,\"finishToken\",this,3)([type,val]);}},{key:\"addComment\",value:function addComment(comment){if(this.flowPragma===undefined){var matches=FLOW_PRAGMA_REGEX.exec(comment.value);if(!matches);else if(matches[1]===\"flow\"){this.flowPragma=\"flow\";}else if(matches[1]===\"noflow\"){this.flowPragma=\"noflow\";}else{throw new Error(\"Unexpected flow pragma\");}}_superPropGet(FlowParserMixin,\"addComment\",this,3)([comment]);}},{key:\"flowParseTypeInitialiser\",value:function flowParseTypeInitialiser(tok){var oldInType=this.state.inType;this.state.inType=true;this.expect(tok||14);var type=this.flowParseType();this.state.inType=oldInType;return type;}},{key:\"flowParsePredicate\",value:function flowParsePredicate(){var node=this.startNode();var moduloLoc=this.state.startLoc;this.next();this.expectContextual(110);if(this.state.lastTokStartLoc.index>moduloLoc.index+1){this.raise(FlowErrors.UnexpectedSpaceBetweenModuloChecks,moduloLoc);}if(this.eat(10)){node.value=_superPropGet(FlowParserMixin,\"parseExpression\",this,3)([]);this.expect(11);return this.finishNode(node,\"DeclaredPredicate\");}else{return this.finishNode(node,\"InferredPredicate\");}}},{key:\"flowParseTypeAndPredicateInitialiser\",value:function flowParseTypeAndPredicateInitialiser(){var oldInType=this.state.inType;this.state.inType=true;this.expect(14);var type=null;var predicate=null;if(this.match(54)){this.state.inType=oldInType;predicate=this.flowParsePredicate();}else{type=this.flowParseType();this.state.inType=oldInType;if(this.match(54)){predicate=this.flowParsePredicate();}}return[type,predicate];}},{key:\"flowParseDeclareClass\",value:function flowParseDeclareClass(node){this.next();this.flowParseInterfaceish(node,true);return this.finishNode(node,\"DeclareClass\");}},{key:\"flowParseDeclareFunction\",value:function flowParseDeclareFunction(node){this.next();var id=node.id=this.parseIdentifier();var typeNode=this.startNode();var typeContainer=this.startNode();if(this.match(47)){typeNode.typeParameters=this.flowParseTypeParameterDeclaration();}else{typeNode.typeParameters=null;}this.expect(10);var tmp=this.flowParseFunctionTypeParams();typeNode.params=tmp.params;typeNode.rest=tmp.rest;typeNode.this=tmp._this;this.expect(11);var _this$flowParseTypeAn=this.flowParseTypeAndPredicateInitialiser();var _this$flowParseTypeAn2=_slicedToArray(_this$flowParseTypeAn,2);typeNode.returnType=_this$flowParseTypeAn2[0];node.predicate=_this$flowParseTypeAn2[1];typeContainer.typeAnnotation=this.finishNode(typeNode,\"FunctionTypeAnnotation\");id.typeAnnotation=this.finishNode(typeContainer,\"TypeAnnotation\");this.resetEndLocation(id);this.semicolon();this.scope.declareName(node.id.name,2048,node.id.loc.start);return this.finishNode(node,\"DeclareFunction\");}},{key:\"flowParseDeclare\",value:function flowParseDeclare(node,insideModule){if(this.match(80)){return this.flowParseDeclareClass(node);}else if(this.match(68)){return this.flowParseDeclareFunction(node);}else if(this.match(74)){return this.flowParseDeclareVariable(node);}else if(this.eatContextual(127)){if(this.match(16)){return this.flowParseDeclareModuleExports(node);}else{if(insideModule){this.raise(FlowErrors.NestedDeclareModule,this.state.lastTokStartLoc);}return this.flowParseDeclareModule(node);}}else if(this.isContextual(130)){return this.flowParseDeclareTypeAlias(node);}else if(this.isContextual(131)){return this.flowParseDeclareOpaqueType(node);}else if(this.isContextual(129)){return this.flowParseDeclareInterface(node);}else if(this.match(82)){return this.flowParseDeclareExportDeclaration(node,insideModule);}else{this.unexpected();}}},{key:\"flowParseDeclareVariable\",value:function flowParseDeclareVariable(node){this.next();node.id=this.flowParseTypeAnnotatableIdentifier(true);this.scope.declareName(node.id.name,5,node.id.loc.start);this.semicolon();return this.finishNode(node,\"DeclareVariable\");}},{key:\"flowParseDeclareModule\",value:function flowParseDeclareModule(node){var _this5=this;this.scope.enter(0);if(this.match(134)){node.id=_superPropGet(FlowParserMixin,\"parseExprAtom\",this,3)([]);}else{node.id=this.parseIdentifier();}var bodyNode=node.body=this.startNode();var body=bodyNode.body=[];this.expect(5);while(!this.match(8)){var _bodyNode=this.startNode();if(this.match(83)){this.next();if(!this.isContextual(130)&&!this.match(87)){this.raise(FlowErrors.InvalidNonTypeImportInDeclareModule,this.state.lastTokStartLoc);}_superPropGet(FlowParserMixin,\"parseImport\",this,3)([_bodyNode]);}else{this.expectContextual(125,FlowErrors.UnsupportedStatementInDeclareModule);_bodyNode=this.flowParseDeclare(_bodyNode,true);}body.push(_bodyNode);}this.scope.exit();this.expect(8);this.finishNode(bodyNode,\"BlockStatement\");var kind=null;var hasModuleExport=false;body.forEach(function(bodyElement){if(isEsModuleType(bodyElement)){if(kind===\"CommonJS\"){_this5.raise(FlowErrors.AmbiguousDeclareModuleKind,bodyElement);}kind=\"ES\";}else if(bodyElement.type===\"DeclareModuleExports\"){if(hasModuleExport){_this5.raise(FlowErrors.DuplicateDeclareModuleExports,bodyElement);}if(kind===\"ES\"){_this5.raise(FlowErrors.AmbiguousDeclareModuleKind,bodyElement);}kind=\"CommonJS\";hasModuleExport=true;}});node.kind=kind||\"CommonJS\";return this.finishNode(node,\"DeclareModule\");}},{key:\"flowParseDeclareExportDeclaration\",value:function flowParseDeclareExportDeclaration(node,insideModule){this.expect(82);if(this.eat(65)){if(this.match(68)||this.match(80)){node.declaration=this.flowParseDeclare(this.startNode());}else{node.declaration=this.flowParseType();this.semicolon();}node.default=true;return this.finishNode(node,\"DeclareExportDeclaration\");}else{if(this.match(75)||this.isLet()||(this.isContextual(130)||this.isContextual(129))&&!insideModule){var label=this.state.value;throw this.raise(FlowErrors.UnsupportedDeclareExportKind,this.state.startLoc,{unsupportedExportKind:label,suggestion:exportSuggestions[label]});}if(this.match(74)||this.match(68)||this.match(80)||this.isContextual(131)){node.declaration=this.flowParseDeclare(this.startNode());node.default=false;return this.finishNode(node,\"DeclareExportDeclaration\");}else if(this.match(55)||this.match(5)||this.isContextual(129)||this.isContextual(130)||this.isContextual(131)){node=this.parseExport(node,null);if(node.type===\"ExportNamedDeclaration\"){node.default=false;delete node.exportKind;return this.castNodeTo(node,\"DeclareExportDeclaration\");}else{return this.castNodeTo(node,\"DeclareExportAllDeclaration\");}}}this.unexpected();}},{key:\"flowParseDeclareModuleExports\",value:function flowParseDeclareModuleExports(node){this.next();this.expectContextual(111);node.typeAnnotation=this.flowParseTypeAnnotation();this.semicolon();return this.finishNode(node,\"DeclareModuleExports\");}},{key:\"flowParseDeclareTypeAlias\",value:function flowParseDeclareTypeAlias(node){this.next();var finished=this.flowParseTypeAlias(node);this.castNodeTo(finished,\"DeclareTypeAlias\");return finished;}},{key:\"flowParseDeclareOpaqueType\",value:function flowParseDeclareOpaqueType(node){this.next();var finished=this.flowParseOpaqueType(node,true);this.castNodeTo(finished,\"DeclareOpaqueType\");return finished;}},{key:\"flowParseDeclareInterface\",value:function flowParseDeclareInterface(node){this.next();this.flowParseInterfaceish(node,false);return this.finishNode(node,\"DeclareInterface\");}},{key:\"flowParseInterfaceish\",value:function flowParseInterfaceish(node,isClass){node.id=this.flowParseRestrictedIdentifier(!isClass,true);this.scope.declareName(node.id.name,isClass?17:8201,node.id.loc.start);if(this.match(47)){node.typeParameters=this.flowParseTypeParameterDeclaration();}else{node.typeParameters=null;}node.extends=[];if(this.eat(81)){do{node.extends.push(this.flowParseInterfaceExtends());}while(!isClass&&this.eat(12));}if(isClass){node.implements=[];node.mixins=[];if(this.eatContextual(117)){do{node.mixins.push(this.flowParseInterfaceExtends());}while(this.eat(12));}if(this.eatContextual(113)){do{node.implements.push(this.flowParseInterfaceExtends());}while(this.eat(12));}}node.body=this.flowParseObjectType({allowStatic:isClass,allowExact:false,allowSpread:false,allowProto:isClass,allowInexact:false});}},{key:\"flowParseInterfaceExtends\",value:function flowParseInterfaceExtends(){var node=this.startNode();node.id=this.flowParseQualifiedTypeIdentifier();if(this.match(47)){node.typeParameters=this.flowParseTypeParameterInstantiation();}else{node.typeParameters=null;}return this.finishNode(node,\"InterfaceExtends\");}},{key:\"flowParseInterface\",value:function flowParseInterface(node){this.flowParseInterfaceish(node,false);return this.finishNode(node,\"InterfaceDeclaration\");}},{key:\"checkNotUnderscore\",value:function checkNotUnderscore(word){if(word===\"_\"){this.raise(FlowErrors.UnexpectedReservedUnderscore,this.state.startLoc);}}},{key:\"checkReservedType\",value:function checkReservedType(word,startLoc,declaration){if(!reservedTypes.has(word))return;this.raise(declaration?FlowErrors.AssignReservedType:FlowErrors.UnexpectedReservedType,startLoc,{reservedType:word});}},{key:\"flowParseRestrictedIdentifier\",value:function flowParseRestrictedIdentifier(liberal,declaration){this.checkReservedType(this.state.value,this.state.startLoc,declaration);return this.parseIdentifier(liberal);}},{key:\"flowParseTypeAlias\",value:function flowParseTypeAlias(node){node.id=this.flowParseRestrictedIdentifier(false,true);this.scope.declareName(node.id.name,8201,node.id.loc.start);if(this.match(47)){node.typeParameters=this.flowParseTypeParameterDeclaration();}else{node.typeParameters=null;}node.right=this.flowParseTypeInitialiser(29);this.semicolon();return this.finishNode(node,\"TypeAlias\");}},{key:\"flowParseOpaqueType\",value:function flowParseOpaqueType(node,declare){this.expectContextual(130);node.id=this.flowParseRestrictedIdentifier(true,true);this.scope.declareName(node.id.name,8201,node.id.loc.start);if(this.match(47)){node.typeParameters=this.flowParseTypeParameterDeclaration();}else{node.typeParameters=null;}node.supertype=null;if(this.match(14)){node.supertype=this.flowParseTypeInitialiser(14);}node.impltype=null;if(!declare){node.impltype=this.flowParseTypeInitialiser(29);}this.semicolon();return this.finishNode(node,\"OpaqueType\");}},{key:\"flowParseTypeParameter\",value:function flowParseTypeParameter(){var requireDefault=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var nodeStartLoc=this.state.startLoc;var node=this.startNode();var variance=this.flowParseVariance();var ident=this.flowParseTypeAnnotatableIdentifier();node.name=ident.name;node.variance=variance;node.bound=ident.typeAnnotation;if(this.match(29)){this.eat(29);node.default=this.flowParseType();}else{if(requireDefault){this.raise(FlowErrors.MissingTypeParamDefault,nodeStartLoc);}}return this.finishNode(node,\"TypeParameter\");}},{key:\"flowParseTypeParameterDeclaration\",value:function flowParseTypeParameterDeclaration(){var oldInType=this.state.inType;var node=this.startNode();node.params=[];this.state.inType=true;if(this.match(47)||this.match(143)){this.next();}else{this.unexpected();}var defaultRequired=false;do{var typeParameter=this.flowParseTypeParameter(defaultRequired);node.params.push(typeParameter);if(typeParameter.default){defaultRequired=true;}if(!this.match(48)){this.expect(12);}}while(!this.match(48));this.expect(48);this.state.inType=oldInType;return this.finishNode(node,\"TypeParameterDeclaration\");}},{key:\"flowInTopLevelContext\",value:function flowInTopLevelContext(cb){if(this.curContext()!==types.brace){var oldContext=this.state.context;this.state.context=[oldContext[0]];try{return cb();}finally{this.state.context=oldContext;}}else{return cb();}}},{key:\"flowParseTypeParameterInstantiationInExpression\",value:function flowParseTypeParameterInstantiationInExpression(){if(this.reScan_lt()!==47)return;return this.flowParseTypeParameterInstantiation();}},{key:\"flowParseTypeParameterInstantiation\",value:function flowParseTypeParameterInstantiation(){var _this6=this;var node=this.startNode();var oldInType=this.state.inType;this.state.inType=true;node.params=[];this.flowInTopLevelContext(function(){_this6.expect(47);var oldNoAnonFunctionType=_this6.state.noAnonFunctionType;_this6.state.noAnonFunctionType=false;while(!_this6.match(48)){node.params.push(_this6.flowParseType());if(!_this6.match(48)){_this6.expect(12);}}_this6.state.noAnonFunctionType=oldNoAnonFunctionType;});this.state.inType=oldInType;if(!this.state.inType&&this.curContext()===types.brace){this.reScan_lt_gt();}this.expect(48);return this.finishNode(node,\"TypeParameterInstantiation\");}},{key:\"flowParseTypeParameterInstantiationCallOrNew\",value:function flowParseTypeParameterInstantiationCallOrNew(){if(this.reScan_lt()!==47)return;var node=this.startNode();var oldInType=this.state.inType;node.params=[];this.state.inType=true;this.expect(47);while(!this.match(48)){node.params.push(this.flowParseTypeOrImplicitInstantiation());if(!this.match(48)){this.expect(12);}}this.expect(48);this.state.inType=oldInType;return this.finishNode(node,\"TypeParameterInstantiation\");}},{key:\"flowParseInterfaceType\",value:function flowParseInterfaceType(){var node=this.startNode();this.expectContextual(129);node.extends=[];if(this.eat(81)){do{node.extends.push(this.flowParseInterfaceExtends());}while(this.eat(12));}node.body=this.flowParseObjectType({allowStatic:false,allowExact:false,allowSpread:false,allowProto:false,allowInexact:false});return this.finishNode(node,\"InterfaceTypeAnnotation\");}},{key:\"flowParseObjectPropertyKey\",value:function flowParseObjectPropertyKey(){return this.match(135)||this.match(134)?_superPropGet(FlowParserMixin,\"parseExprAtom\",this,3)([]):this.parseIdentifier(true);}},{key:\"flowParseObjectTypeIndexer\",value:function flowParseObjectTypeIndexer(node,isStatic,variance){node.static=isStatic;if(this.lookahead().type===14){node.id=this.flowParseObjectPropertyKey();node.key=this.flowParseTypeInitialiser();}else{node.id=null;node.key=this.flowParseType();}this.expect(3);node.value=this.flowParseTypeInitialiser();node.variance=variance;return this.finishNode(node,\"ObjectTypeIndexer\");}},{key:\"flowParseObjectTypeInternalSlot\",value:function flowParseObjectTypeInternalSlot(node,isStatic){node.static=isStatic;node.id=this.flowParseObjectPropertyKey();this.expect(3);this.expect(3);if(this.match(47)||this.match(10)){node.method=true;node.optional=false;node.value=this.flowParseObjectTypeMethodish(this.startNodeAt(node.loc.start));}else{node.method=false;if(this.eat(17)){node.optional=true;}node.value=this.flowParseTypeInitialiser();}return this.finishNode(node,\"ObjectTypeInternalSlot\");}},{key:\"flowParseObjectTypeMethodish\",value:function flowParseObjectTypeMethodish(node){node.params=[];node.rest=null;node.typeParameters=null;node.this=null;if(this.match(47)){node.typeParameters=this.flowParseTypeParameterDeclaration();}this.expect(10);if(this.match(78)){node.this=this.flowParseFunctionTypeParam(true);node.this.name=null;if(!this.match(11)){this.expect(12);}}while(!this.match(11)&&!this.match(21)){node.params.push(this.flowParseFunctionTypeParam(false));if(!this.match(11)){this.expect(12);}}if(this.eat(21)){node.rest=this.flowParseFunctionTypeParam(false);}this.expect(11);node.returnType=this.flowParseTypeInitialiser();return this.finishNode(node,\"FunctionTypeAnnotation\");}},{key:\"flowParseObjectTypeCallProperty\",value:function flowParseObjectTypeCallProperty(node,isStatic){var valueNode=this.startNode();node.static=isStatic;node.value=this.flowParseObjectTypeMethodish(valueNode);return this.finishNode(node,\"ObjectTypeCallProperty\");}},{key:\"flowParseObjectType\",value:function flowParseObjectType(_ref51){var allowStatic=_ref51.allowStatic,allowExact=_ref51.allowExact,allowSpread=_ref51.allowSpread,allowProto=_ref51.allowProto,allowInexact=_ref51.allowInexact;var oldInType=this.state.inType;this.state.inType=true;var nodeStart=this.startNode();nodeStart.callProperties=[];nodeStart.properties=[];nodeStart.indexers=[];nodeStart.internalSlots=[];var endDelim;var exact;var inexact=false;if(allowExact&&this.match(6)){this.expect(6);endDelim=9;exact=true;}else{this.expect(5);endDelim=8;exact=false;}nodeStart.exact=exact;while(!this.match(endDelim)){var isStatic=false;var protoStartLoc=null;var inexactStartLoc=null;var node=this.startNode();if(allowProto&&this.isContextual(118)){var lookahead=this.lookahead();if(lookahead.type!==14&&lookahead.type!==17){this.next();protoStartLoc=this.state.startLoc;allowStatic=false;}}if(allowStatic&&this.isContextual(106)){var _lookahead=this.lookahead();if(_lookahead.type!==14&&_lookahead.type!==17){this.next();isStatic=true;}}var variance=this.flowParseVariance();if(this.eat(0)){if(protoStartLoc!=null){this.unexpected(protoStartLoc);}if(this.eat(0)){if(variance){this.unexpected(variance.loc.start);}nodeStart.internalSlots.push(this.flowParseObjectTypeInternalSlot(node,isStatic));}else{nodeStart.indexers.push(this.flowParseObjectTypeIndexer(node,isStatic,variance));}}else if(this.match(10)||this.match(47)){if(protoStartLoc!=null){this.unexpected(protoStartLoc);}if(variance){this.unexpected(variance.loc.start);}nodeStart.callProperties.push(this.flowParseObjectTypeCallProperty(node,isStatic));}else{var kind=\"init\";if(this.isContextual(99)||this.isContextual(104)){var _lookahead2=this.lookahead();if(tokenIsLiteralPropertyName(_lookahead2.type)){kind=this.state.value;this.next();}}var propOrInexact=this.flowParseObjectTypeProperty(node,isStatic,protoStartLoc,variance,kind,allowSpread,allowInexact!=null?allowInexact:!exact);if(propOrInexact===null){inexact=true;inexactStartLoc=this.state.lastTokStartLoc;}else{nodeStart.properties.push(propOrInexact);}}this.flowObjectTypeSemicolon();if(inexactStartLoc&&!this.match(8)&&!this.match(9)){this.raise(FlowErrors.UnexpectedExplicitInexactInObject,inexactStartLoc);}}this.expect(endDelim);if(allowSpread){nodeStart.inexact=inexact;}var out=this.finishNode(nodeStart,\"ObjectTypeAnnotation\");this.state.inType=oldInType;return out;}},{key:\"flowParseObjectTypeProperty\",value:function flowParseObjectTypeProperty(node,isStatic,protoStartLoc,variance,kind,allowSpread,allowInexact){if(this.eat(21)){var isInexactToken=this.match(12)||this.match(13)||this.match(8)||this.match(9);if(isInexactToken){if(!allowSpread){this.raise(FlowErrors.InexactInsideNonObject,this.state.lastTokStartLoc);}else if(!allowInexact){this.raise(FlowErrors.InexactInsideExact,this.state.lastTokStartLoc);}if(variance){this.raise(FlowErrors.InexactVariance,variance);}return null;}if(!allowSpread){this.raise(FlowErrors.UnexpectedSpreadType,this.state.lastTokStartLoc);}if(protoStartLoc!=null){this.unexpected(protoStartLoc);}if(variance){this.raise(FlowErrors.SpreadVariance,variance);}node.argument=this.flowParseType();return this.finishNode(node,\"ObjectTypeSpreadProperty\");}else{node.key=this.flowParseObjectPropertyKey();node.static=isStatic;node.proto=protoStartLoc!=null;node.kind=kind;var optional=false;if(this.match(47)||this.match(10)){node.method=true;if(protoStartLoc!=null){this.unexpected(protoStartLoc);}if(variance){this.unexpected(variance.loc.start);}node.value=this.flowParseObjectTypeMethodish(this.startNodeAt(node.loc.start));if(kind===\"get\"||kind===\"set\"){this.flowCheckGetterSetterParams(node);}if(!allowSpread&&node.key.name===\"constructor\"&&node.value.this){this.raise(FlowErrors.ThisParamBannedInConstructor,node.value.this);}}else{if(kind!==\"init\")this.unexpected();node.method=false;if(this.eat(17)){optional=true;}node.value=this.flowParseTypeInitialiser();node.variance=variance;}node.optional=optional;return this.finishNode(node,\"ObjectTypeProperty\");}}},{key:\"flowCheckGetterSetterParams\",value:function flowCheckGetterSetterParams(property){var paramCount=property.kind===\"get\"?0:1;var length=property.value.params.length+(property.value.rest?1:0);if(property.value.this){this.raise(property.kind===\"get\"?FlowErrors.GetterMayNotHaveThisParam:FlowErrors.SetterMayNotHaveThisParam,property.value.this);}if(length!==paramCount){this.raise(property.kind===\"get\"?Errors.BadGetterArity:Errors.BadSetterArity,property);}if(property.kind===\"set\"&&property.value.rest){this.raise(Errors.BadSetterRestParameter,property);}}},{key:\"flowObjectTypeSemicolon\",value:function flowObjectTypeSemicolon(){if(!this.eat(13)&&!this.eat(12)&&!this.match(8)&&!this.match(9)){this.unexpected();}}},{key:\"flowParseQualifiedTypeIdentifier\",value:function flowParseQualifiedTypeIdentifier(startLoc,id){startLoc!=null?startLoc:startLoc=this.state.startLoc;var node=id||this.flowParseRestrictedIdentifier(true);while(this.eat(16)){var node2=this.startNodeAt(startLoc);node2.qualification=node;node2.id=this.flowParseRestrictedIdentifier(true);node=this.finishNode(node2,\"QualifiedTypeIdentifier\");}return node;}},{key:\"flowParseGenericType\",value:function flowParseGenericType(startLoc,id){var node=this.startNodeAt(startLoc);node.typeParameters=null;node.id=this.flowParseQualifiedTypeIdentifier(startLoc,id);if(this.match(47)){node.typeParameters=this.flowParseTypeParameterInstantiation();}return this.finishNode(node,\"GenericTypeAnnotation\");}},{key:\"flowParseTypeofType\",value:function flowParseTypeofType(){var node=this.startNode();this.expect(87);node.argument=this.flowParsePrimaryType();return this.finishNode(node,\"TypeofTypeAnnotation\");}},{key:\"flowParseTupleType\",value:function flowParseTupleType(){var node=this.startNode();node.types=[];this.expect(0);while(this.state.pos<this.length&&!this.match(3)){node.types.push(this.flowParseType());if(this.match(3))break;this.expect(12);}this.expect(3);return this.finishNode(node,\"TupleTypeAnnotation\");}},{key:\"flowParseFunctionTypeParam\",value:function flowParseFunctionTypeParam(first){var name=null;var optional=false;var typeAnnotation=null;var node=this.startNode();var lh=this.lookahead();var isThis=this.state.type===78;if(lh.type===14||lh.type===17){if(isThis&&!first){this.raise(FlowErrors.ThisParamMustBeFirst,node);}name=this.parseIdentifier(isThis);if(this.eat(17)){optional=true;if(isThis){this.raise(FlowErrors.ThisParamMayNotBeOptional,node);}}typeAnnotation=this.flowParseTypeInitialiser();}else{typeAnnotation=this.flowParseType();}node.name=name;node.optional=optional;node.typeAnnotation=typeAnnotation;return this.finishNode(node,\"FunctionTypeParam\");}},{key:\"reinterpretTypeAsFunctionTypeParam\",value:function reinterpretTypeAsFunctionTypeParam(type){var node=this.startNodeAt(type.loc.start);node.name=null;node.optional=false;node.typeAnnotation=type;return this.finishNode(node,\"FunctionTypeParam\");}},{key:\"flowParseFunctionTypeParams\",value:function flowParseFunctionTypeParams(){var params=arguments.length>0&&arguments[0]!==undefined?arguments[0]:[];var rest=null;var _this=null;if(this.match(78)){_this=this.flowParseFunctionTypeParam(true);_this.name=null;if(!this.match(11)){this.expect(12);}}while(!this.match(11)&&!this.match(21)){params.push(this.flowParseFunctionTypeParam(false));if(!this.match(11)){this.expect(12);}}if(this.eat(21)){rest=this.flowParseFunctionTypeParam(false);}return{params:params,rest:rest,_this:_this};}},{key:\"flowIdentToTypeAnnotation\",value:function flowIdentToTypeAnnotation(startLoc,node,id){switch(id.name){case\"any\":return this.finishNode(node,\"AnyTypeAnnotation\");case\"bool\":case\"boolean\":return this.finishNode(node,\"BooleanTypeAnnotation\");case\"mixed\":return this.finishNode(node,\"MixedTypeAnnotation\");case\"empty\":return this.finishNode(node,\"EmptyTypeAnnotation\");case\"number\":return this.finishNode(node,\"NumberTypeAnnotation\");case\"string\":return this.finishNode(node,\"StringTypeAnnotation\");case\"symbol\":return this.finishNode(node,\"SymbolTypeAnnotation\");default:this.checkNotUnderscore(id.name);return this.flowParseGenericType(startLoc,id);}}},{key:\"flowParsePrimaryType\",value:function flowParsePrimaryType(){var startLoc=this.state.startLoc;var node=this.startNode();var tmp;var type;var isGroupedType=false;var oldNoAnonFunctionType=this.state.noAnonFunctionType;switch(this.state.type){case 5:return this.flowParseObjectType({allowStatic:false,allowExact:false,allowSpread:true,allowProto:false,allowInexact:true});case 6:return this.flowParseObjectType({allowStatic:false,allowExact:true,allowSpread:true,allowProto:false,allowInexact:false});case 0:this.state.noAnonFunctionType=false;type=this.flowParseTupleType();this.state.noAnonFunctionType=oldNoAnonFunctionType;return type;case 47:{var _node=this.startNode();_node.typeParameters=this.flowParseTypeParameterDeclaration();this.expect(10);tmp=this.flowParseFunctionTypeParams();_node.params=tmp.params;_node.rest=tmp.rest;_node.this=tmp._this;this.expect(11);this.expect(19);_node.returnType=this.flowParseType();return this.finishNode(_node,\"FunctionTypeAnnotation\");}case 10:{var _node2=this.startNode();this.next();if(!this.match(11)&&!this.match(21)){if(tokenIsIdentifier(this.state.type)||this.match(78)){var token=this.lookahead().type;isGroupedType=token!==17&&token!==14;}else{isGroupedType=true;}}if(isGroupedType){this.state.noAnonFunctionType=false;type=this.flowParseType();this.state.noAnonFunctionType=oldNoAnonFunctionType;if(this.state.noAnonFunctionType||!(this.match(12)||this.match(11)&&this.lookahead().type===19)){this.expect(11);return type;}else{this.eat(12);}}if(type){tmp=this.flowParseFunctionTypeParams([this.reinterpretTypeAsFunctionTypeParam(type)]);}else{tmp=this.flowParseFunctionTypeParams();}_node2.params=tmp.params;_node2.rest=tmp.rest;_node2.this=tmp._this;this.expect(11);this.expect(19);_node2.returnType=this.flowParseType();_node2.typeParameters=null;return this.finishNode(_node2,\"FunctionTypeAnnotation\");}case 134:return this.parseLiteral(this.state.value,\"StringLiteralTypeAnnotation\");case 85:case 86:node.value=this.match(85);this.next();return this.finishNode(node,\"BooleanLiteralTypeAnnotation\");case 53:if(this.state.value===\"-\"){this.next();if(this.match(135)){return this.parseLiteralAtNode(-this.state.value,\"NumberLiteralTypeAnnotation\",node);}if(this.match(136)){return this.parseLiteralAtNode(-this.state.value,\"BigIntLiteralTypeAnnotation\",node);}throw this.raise(FlowErrors.UnexpectedSubtractionOperand,this.state.startLoc);}this.unexpected();return;case 135:return this.parseLiteral(this.state.value,\"NumberLiteralTypeAnnotation\");case 136:return this.parseLiteral(this.state.value,\"BigIntLiteralTypeAnnotation\");case 88:this.next();return this.finishNode(node,\"VoidTypeAnnotation\");case 84:this.next();return this.finishNode(node,\"NullLiteralTypeAnnotation\");case 78:this.next();return this.finishNode(node,\"ThisTypeAnnotation\");case 55:this.next();return this.finishNode(node,\"ExistsTypeAnnotation\");case 87:return this.flowParseTypeofType();default:if(tokenIsKeyword(this.state.type)){var label=tokenLabelName(this.state.type);this.next();return _superPropGet(FlowParserMixin,\"createIdentifier\",this,3)([node,label]);}else if(tokenIsIdentifier(this.state.type)){if(this.isContextual(129)){return this.flowParseInterfaceType();}return this.flowIdentToTypeAnnotation(startLoc,node,this.parseIdentifier());}}this.unexpected();}},{key:\"flowParsePostfixType\",value:function flowParsePostfixType(){var startLoc=this.state.startLoc;var type=this.flowParsePrimaryType();var seenOptionalIndexedAccess=false;while((this.match(0)||this.match(18))&&!this.canInsertSemicolon()){var node=this.startNodeAt(startLoc);var optional=this.eat(18);seenOptionalIndexedAccess=seenOptionalIndexedAccess||optional;this.expect(0);if(!optional&&this.match(3)){node.elementType=type;this.next();type=this.finishNode(node,\"ArrayTypeAnnotation\");}else{node.objectType=type;node.indexType=this.flowParseType();this.expect(3);if(seenOptionalIndexedAccess){node.optional=optional;type=this.finishNode(node,\"OptionalIndexedAccessType\");}else{type=this.finishNode(node,\"IndexedAccessType\");}}}return type;}},{key:\"flowParsePrefixType\",value:function flowParsePrefixType(){var node=this.startNode();if(this.eat(17)){node.typeAnnotation=this.flowParsePrefixType();return this.finishNode(node,\"NullableTypeAnnotation\");}else{return this.flowParsePostfixType();}}},{key:\"flowParseAnonFunctionWithoutParens\",value:function flowParseAnonFunctionWithoutParens(){var param=this.flowParsePrefixType();if(!this.state.noAnonFunctionType&&this.eat(19)){var node=this.startNodeAt(param.loc.start);node.params=[this.reinterpretTypeAsFunctionTypeParam(param)];node.rest=null;node.this=null;node.returnType=this.flowParseType();node.typeParameters=null;return this.finishNode(node,\"FunctionTypeAnnotation\");}return param;}},{key:\"flowParseIntersectionType\",value:function flowParseIntersectionType(){var node=this.startNode();this.eat(45);var type=this.flowParseAnonFunctionWithoutParens();node.types=[type];while(this.eat(45)){node.types.push(this.flowParseAnonFunctionWithoutParens());}return node.types.length===1?type:this.finishNode(node,\"IntersectionTypeAnnotation\");}},{key:\"flowParseUnionType\",value:function flowParseUnionType(){var node=this.startNode();this.eat(43);var type=this.flowParseIntersectionType();node.types=[type];while(this.eat(43)){node.types.push(this.flowParseIntersectionType());}return node.types.length===1?type:this.finishNode(node,\"UnionTypeAnnotation\");}},{key:\"flowParseType\",value:function flowParseType(){var oldInType=this.state.inType;this.state.inType=true;var type=this.flowParseUnionType();this.state.inType=oldInType;return type;}},{key:\"flowParseTypeOrImplicitInstantiation\",value:function flowParseTypeOrImplicitInstantiation(){if(this.state.type===132&&this.state.value===\"_\"){var startLoc=this.state.startLoc;var node=this.parseIdentifier();return this.flowParseGenericType(startLoc,node);}else{return this.flowParseType();}}},{key:\"flowParseTypeAnnotation\",value:function flowParseTypeAnnotation(){var node=this.startNode();node.typeAnnotation=this.flowParseTypeInitialiser();return this.finishNode(node,\"TypeAnnotation\");}},{key:\"flowParseTypeAnnotatableIdentifier\",value:function flowParseTypeAnnotatableIdentifier(allowPrimitiveOverride){var ident=allowPrimitiveOverride?this.parseIdentifier():this.flowParseRestrictedIdentifier();if(this.match(14)){ident.typeAnnotation=this.flowParseTypeAnnotation();this.resetEndLocation(ident);}return ident;}},{key:\"typeCastToParameter\",value:function typeCastToParameter(node){node.expression.typeAnnotation=node.typeAnnotation;this.resetEndLocation(node.expression,node.typeAnnotation.loc.end);return node.expression;}},{key:\"flowParseVariance\",value:function flowParseVariance(){var variance=null;if(this.match(53)){variance=this.startNode();if(this.state.value===\"+\"){variance.kind=\"plus\";}else{variance.kind=\"minus\";}this.next();return this.finishNode(variance,\"Variance\");}return variance;}},{key:\"parseFunctionBody\",value:function parseFunctionBody(node,allowExpressionBody){var _this7=this;var isMethod=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;if(allowExpressionBody){this.forwardNoArrowParamsConversionAt(node,function(){return _superPropGet(FlowParserMixin,\"parseFunctionBody\",_this7,3)([node,true,isMethod]);});return;}_superPropGet(FlowParserMixin,\"parseFunctionBody\",this,3)([node,false,isMethod]);}},{key:\"parseFunctionBodyAndFinish\",value:function parseFunctionBodyAndFinish(node,type){var isMethod=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;if(this.match(14)){var typeNode=this.startNode();var _this$flowParseTypeAn3=this.flowParseTypeAndPredicateInitialiser();var _this$flowParseTypeAn4=_slicedToArray(_this$flowParseTypeAn3,2);typeNode.typeAnnotation=_this$flowParseTypeAn4[0];node.predicate=_this$flowParseTypeAn4[1];node.returnType=typeNode.typeAnnotation?this.finishNode(typeNode,\"TypeAnnotation\"):null;}return _superPropGet(FlowParserMixin,\"parseFunctionBodyAndFinish\",this,3)([node,type,isMethod]);}},{key:\"parseStatementLike\",value:function parseStatementLike(flags){if(this.state.strict&&this.isContextual(129)){var lookahead=this.lookahead();if(tokenIsKeywordOrIdentifier(lookahead.type)){var node=this.startNode();this.next();return this.flowParseInterface(node);}}else if(this.isContextual(126)){var _node3=this.startNode();this.next();return this.flowParseEnumDeclaration(_node3);}var stmt=_superPropGet(FlowParserMixin,\"parseStatementLike\",this,3)([flags]);if(this.flowPragma===undefined&&!this.isValidDirective(stmt)){this.flowPragma=null;}return stmt;}},{key:\"parseExpressionStatement\",value:function parseExpressionStatement(node,expr,decorators){if(expr.type===\"Identifier\"){if(expr.name===\"declare\"){if(this.match(80)||tokenIsIdentifier(this.state.type)||this.match(68)||this.match(74)||this.match(82)){return this.flowParseDeclare(node);}}else if(tokenIsIdentifier(this.state.type)){if(expr.name===\"interface\"){return this.flowParseInterface(node);}else if(expr.name===\"type\"){return this.flowParseTypeAlias(node);}else if(expr.name===\"opaque\"){return this.flowParseOpaqueType(node,false);}}}return _superPropGet(FlowParserMixin,\"parseExpressionStatement\",this,3)([node,expr,decorators]);}},{key:\"shouldParseExportDeclaration\",value:function shouldParseExportDeclaration(){var type=this.state.type;if(type===126||tokenIsFlowInterfaceOrTypeOrOpaque(type)){return!this.state.containsEsc;}return _superPropGet(FlowParserMixin,\"shouldParseExportDeclaration\",this,3)([]);}},{key:\"isExportDefaultSpecifier\",value:function isExportDefaultSpecifier(){var type=this.state.type;if(type===126||tokenIsFlowInterfaceOrTypeOrOpaque(type)){return this.state.containsEsc;}return _superPropGet(FlowParserMixin,\"isExportDefaultSpecifier\",this,3)([]);}},{key:\"parseExportDefaultExpression\",value:function parseExportDefaultExpression(){if(this.isContextual(126)){var node=this.startNode();this.next();return this.flowParseEnumDeclaration(node);}return _superPropGet(FlowParserMixin,\"parseExportDefaultExpression\",this,3)([]);}},{key:\"parseConditional\",value:function parseConditional(expr,startLoc,refExpressionErrors){var _this8=this;if(!this.match(17))return expr;if(this.state.maybeInArrowParameters){var nextCh=this.lookaheadCharCode();if(nextCh===44||nextCh===61||nextCh===58||nextCh===41){this.setOptionalParametersError(refExpressionErrors);return expr;}}this.expect(17);var state=this.state.clone();var originalNoArrowAt=this.state.noArrowAt;var node=this.startNodeAt(startLoc);var _this$tryParseConditi=this.tryParseConditionalConsequent(),consequent=_this$tryParseConditi.consequent,failed=_this$tryParseConditi.failed;var _this$getArrowLikeExp=this.getArrowLikeExpressions(consequent),_this$getArrowLikeExp2=_slicedToArray(_this$getArrowLikeExp,2),valid=_this$getArrowLikeExp2[0],invalid=_this$getArrowLikeExp2[1];if(failed||invalid.length>0){var noArrowAt=_toConsumableArray(originalNoArrowAt);if(invalid.length>0){this.state=state;this.state.noArrowAt=noArrowAt;for(var i=0;i<invalid.length;i++){noArrowAt.push(invalid[i].start);}var _this$tryParseConditi2=this.tryParseConditionalConsequent();consequent=_this$tryParseConditi2.consequent;failed=_this$tryParseConditi2.failed;var _this$getArrowLikeExp3=this.getArrowLikeExpressions(consequent);var _this$getArrowLikeExp4=_slicedToArray(_this$getArrowLikeExp3,2);valid=_this$getArrowLikeExp4[0];invalid=_this$getArrowLikeExp4[1];}if(failed&&valid.length>1){this.raise(FlowErrors.AmbiguousConditionalArrow,state.startLoc);}if(failed&&valid.length===1){this.state=state;noArrowAt.push(valid[0].start);this.state.noArrowAt=noArrowAt;var _this$tryParseConditi3=this.tryParseConditionalConsequent();consequent=_this$tryParseConditi3.consequent;failed=_this$tryParseConditi3.failed;}}this.getArrowLikeExpressions(consequent,true);this.state.noArrowAt=originalNoArrowAt;this.expect(14);node.test=expr;node.consequent=consequent;node.alternate=this.forwardNoArrowParamsConversionAt(node,function(){return _this8.parseMaybeAssign(undefined,undefined);});return this.finishNode(node,\"ConditionalExpression\");}},{key:\"tryParseConditionalConsequent\",value:function tryParseConditionalConsequent(){this.state.noArrowParamsConversionAt.push(this.state.start);var consequent=this.parseMaybeAssignAllowIn();var failed=!this.match(14);this.state.noArrowParamsConversionAt.pop();return{consequent:consequent,failed:failed};}},{key:\"getArrowLikeExpressions\",value:function getArrowLikeExpressions(node,disallowInvalid){var _this9=this;var stack=[node];var arrows=[];while(stack.length!==0){var _node4=stack.pop();if(_node4.type===\"ArrowFunctionExpression\"&&_node4.body.type!==\"BlockStatement\"){if(_node4.typeParameters||!_node4.returnType){this.finishArrowValidation(_node4);}else{arrows.push(_node4);}stack.push(_node4.body);}else if(_node4.type===\"ConditionalExpression\"){stack.push(_node4.consequent);stack.push(_node4.alternate);}}if(disallowInvalid){arrows.forEach(function(node){return _this9.finishArrowValidation(node);});return[arrows,[]];}return partition(arrows,function(node){return node.params.every(function(param){return _this9.isAssignable(param,true);});});}},{key:\"finishArrowValidation\",value:function finishArrowValidation(node){var _node$extra;this.toAssignableList(node.params,(_node$extra=node.extra)==null?void 0:_node$extra.trailingCommaLoc,false);this.scope.enter(514|4);_superPropGet(FlowParserMixin,\"checkParams\",this,3)([node,false,true]);this.scope.exit();}},{key:\"forwardNoArrowParamsConversionAt\",value:function forwardNoArrowParamsConversionAt(node,parse){var result;if(this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(node.start))){this.state.noArrowParamsConversionAt.push(this.state.start);result=parse();this.state.noArrowParamsConversionAt.pop();}else{result=parse();}return result;}},{key:\"parseParenItem\",value:function parseParenItem(node,startLoc){var newNode=_superPropGet(FlowParserMixin,\"parseParenItem\",this,3)([node,startLoc]);if(this.eat(17)){newNode.optional=true;this.resetEndLocation(node);}if(this.match(14)){var typeCastNode=this.startNodeAt(startLoc);typeCastNode.expression=newNode;typeCastNode.typeAnnotation=this.flowParseTypeAnnotation();return this.finishNode(typeCastNode,\"TypeCastExpression\");}return newNode;}},{key:\"assertModuleNodeAllowed\",value:function assertModuleNodeAllowed(node){if(node.type===\"ImportDeclaration\"&&(node.importKind===\"type\"||node.importKind===\"typeof\")||node.type===\"ExportNamedDeclaration\"&&node.exportKind===\"type\"||node.type===\"ExportAllDeclaration\"&&node.exportKind===\"type\"){return;}_superPropGet(FlowParserMixin,\"assertModuleNodeAllowed\",this,3)([node]);}},{key:\"parseExportDeclaration\",value:function parseExportDeclaration(node){if(this.isContextual(130)){node.exportKind=\"type\";var declarationNode=this.startNode();this.next();if(this.match(5)){node.specifiers=this.parseExportSpecifiers(true);_superPropGet(FlowParserMixin,\"parseExportFrom\",this,3)([node]);return null;}else{return this.flowParseTypeAlias(declarationNode);}}else if(this.isContextual(131)){node.exportKind=\"type\";var _declarationNode=this.startNode();this.next();return this.flowParseOpaqueType(_declarationNode,false);}else if(this.isContextual(129)){node.exportKind=\"type\";var _declarationNode2=this.startNode();this.next();return this.flowParseInterface(_declarationNode2);}else if(this.isContextual(126)){node.exportKind=\"value\";var _declarationNode3=this.startNode();this.next();return this.flowParseEnumDeclaration(_declarationNode3);}else{return _superPropGet(FlowParserMixin,\"parseExportDeclaration\",this,3)([node]);}}},{key:\"eatExportStar\",value:function eatExportStar(node){if(_superPropGet(FlowParserMixin,\"eatExportStar\",this,3)([node]))return true;if(this.isContextual(130)&&this.lookahead().type===55){node.exportKind=\"type\";this.next();this.next();return true;}return false;}},{key:\"maybeParseExportNamespaceSpecifier\",value:function maybeParseExportNamespaceSpecifier(node){var startLoc=this.state.startLoc;var hasNamespace=_superPropGet(FlowParserMixin,\"maybeParseExportNamespaceSpecifier\",this,3)([node]);if(hasNamespace&&node.exportKind===\"type\"){this.unexpected(startLoc);}return hasNamespace;}},{key:\"parseClassId\",value:function parseClassId(node,isStatement,optionalId){_superPropGet(FlowParserMixin,\"parseClassId\",this,3)([node,isStatement,optionalId]);if(this.match(47)){node.typeParameters=this.flowParseTypeParameterDeclaration();}}},{key:\"parseClassMember\",value:function parseClassMember(classBody,member,state){var startLoc=this.state.startLoc;if(this.isContextual(125)){if(_superPropGet(FlowParserMixin,\"parseClassMemberFromModifier\",this,3)([classBody,member])){return;}member.declare=true;}_superPropGet(FlowParserMixin,\"parseClassMember\",this,3)([classBody,member,state]);if(member.declare){if(member.type!==\"ClassProperty\"&&member.type!==\"ClassPrivateProperty\"&&member.type!==\"PropertyDefinition\"){this.raise(FlowErrors.DeclareClassElement,startLoc);}else if(member.value){this.raise(FlowErrors.DeclareClassFieldInitializer,member.value);}}}},{key:\"isIterator\",value:function isIterator(word){return word===\"iterator\"||word===\"asyncIterator\";}},{key:\"readIterator\",value:function readIterator(){var word=_superPropGet(FlowParserMixin,\"readWord1\",this,3)([]);var fullWord=\"@@\"+word;if(!this.isIterator(word)||!this.state.inType){this.raise(Errors.InvalidIdentifier,this.state.curPosition(),{identifierName:fullWord});}this.finishToken(132,fullWord);}},{key:\"getTokenFromCode\",value:function getTokenFromCode(code){var next=this.input.charCodeAt(this.state.pos+1);if(code===123&&next===124){this.finishOp(6,2);}else if(this.state.inType&&(code===62||code===60)){this.finishOp(code===62?48:47,1);}else if(this.state.inType&&code===63){if(next===46){this.finishOp(18,2);}else{this.finishOp(17,1);}}else if(isIteratorStart(code,next,this.input.charCodeAt(this.state.pos+2))){this.state.pos+=2;this.readIterator();}else{_superPropGet(FlowParserMixin,\"getTokenFromCode\",this,3)([code]);}}},{key:\"isAssignable\",value:function isAssignable(node,isBinding){if(node.type===\"TypeCastExpression\"){return this.isAssignable(node.expression,isBinding);}else{return _superPropGet(FlowParserMixin,\"isAssignable\",this,3)([node,isBinding]);}}},{key:\"toAssignable\",value:function toAssignable(node){var isLHS=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;if(!isLHS&&node.type===\"AssignmentExpression\"&&node.left.type===\"TypeCastExpression\"){node.left=this.typeCastToParameter(node.left);}_superPropGet(FlowParserMixin,\"toAssignable\",this,3)([node,isLHS]);}},{key:\"toAssignableList\",value:function toAssignableList(exprList,trailingCommaLoc,isLHS){for(var i=0;i<exprList.length;i++){var expr=exprList[i];if((expr==null?void 0:expr.type)===\"TypeCastExpression\"){exprList[i]=this.typeCastToParameter(expr);}}_superPropGet(FlowParserMixin,\"toAssignableList\",this,3)([exprList,trailingCommaLoc,isLHS]);}},{key:\"toReferencedList\",value:function toReferencedList(exprList,isParenthesizedExpr){for(var i=0;i<exprList.length;i++){var _expr$extra;var expr=exprList[i];if(expr&&expr.type===\"TypeCastExpression\"&&!((_expr$extra=expr.extra)!=null&&_expr$extra.parenthesized)&&(exprList.length>1||!isParenthesizedExpr)){this.raise(FlowErrors.TypeCastInPattern,expr.typeAnnotation);}}return exprList;}},{key:\"parseArrayLike\",value:function parseArrayLike(close,canBePattern,isTuple,refExpressionErrors){var node=_superPropGet(FlowParserMixin,\"parseArrayLike\",this,3)([close,canBePattern,isTuple,refExpressionErrors]);if(canBePattern&&!this.state.maybeInArrowParameters){this.toReferencedList(node.elements);}return node;}},{key:\"isValidLVal\",value:function isValidLVal(type,isParenthesized,binding){return type===\"TypeCastExpression\"||_superPropGet(FlowParserMixin,\"isValidLVal\",this,3)([type,isParenthesized,binding]);}},{key:\"parseClassProperty\",value:function parseClassProperty(node){if(this.match(14)){node.typeAnnotation=this.flowParseTypeAnnotation();}return _superPropGet(FlowParserMixin,\"parseClassProperty\",this,3)([node]);}},{key:\"parseClassPrivateProperty\",value:function parseClassPrivateProperty(node){if(this.match(14)){node.typeAnnotation=this.flowParseTypeAnnotation();}return _superPropGet(FlowParserMixin,\"parseClassPrivateProperty\",this,3)([node]);}},{key:\"isClassMethod\",value:function isClassMethod(){return this.match(47)||_superPropGet(FlowParserMixin,\"isClassMethod\",this,3)([]);}},{key:\"isClassProperty\",value:function isClassProperty(){return this.match(14)||_superPropGet(FlowParserMixin,\"isClassProperty\",this,3)([]);}},{key:\"isNonstaticConstructor\",value:function isNonstaticConstructor(method){return!this.match(14)&&_superPropGet(FlowParserMixin,\"isNonstaticConstructor\",this,3)([method]);}},{key:\"pushClassMethod\",value:function pushClassMethod(classBody,method,isGenerator,isAsync,isConstructor,allowsDirectSuper){if(method.variance){this.unexpected(method.variance.loc.start);}delete method.variance;if(this.match(47)){method.typeParameters=this.flowParseTypeParameterDeclaration();}_superPropGet(FlowParserMixin,\"pushClassMethod\",this,3)([classBody,method,isGenerator,isAsync,isConstructor,allowsDirectSuper]);if(method.params&&isConstructor){var params=method.params;if(params.length>0&&this.isThisParam(params[0])){this.raise(FlowErrors.ThisParamBannedInConstructor,method);}}else if(method.type===\"MethodDefinition\"&&isConstructor&&method.value.params){var _params2=method.value.params;if(_params2.length>0&&this.isThisParam(_params2[0])){this.raise(FlowErrors.ThisParamBannedInConstructor,method);}}}},{key:\"pushClassPrivateMethod\",value:function pushClassPrivateMethod(classBody,method,isGenerator,isAsync){if(method.variance){this.unexpected(method.variance.loc.start);}delete method.variance;if(this.match(47)){method.typeParameters=this.flowParseTypeParameterDeclaration();}_superPropGet(FlowParserMixin,\"pushClassPrivateMethod\",this,3)([classBody,method,isGenerator,isAsync]);}},{key:\"parseClassSuper\",value:function parseClassSuper(node){_superPropGet(FlowParserMixin,\"parseClassSuper\",this,3)([node]);if(node.superClass&&(this.match(47)||this.match(51))){{node.superTypeParameters=this.flowParseTypeParameterInstantiationInExpression();}}if(this.isContextual(113)){this.next();var implemented=node.implements=[];do{var _node5=this.startNode();_node5.id=this.flowParseRestrictedIdentifier(true);if(this.match(47)){_node5.typeParameters=this.flowParseTypeParameterInstantiation();}else{_node5.typeParameters=null;}implemented.push(this.finishNode(_node5,\"ClassImplements\"));}while(this.eat(12));}}},{key:\"checkGetterSetterParams\",value:function checkGetterSetterParams(method){_superPropGet(FlowParserMixin,\"checkGetterSetterParams\",this,3)([method]);var params=this.getObjectOrClassMethodParams(method);if(params.length>0){var param=params[0];if(this.isThisParam(param)&&method.kind===\"get\"){this.raise(FlowErrors.GetterMayNotHaveThisParam,param);}else if(this.isThisParam(param)){this.raise(FlowErrors.SetterMayNotHaveThisParam,param);}}}},{key:\"parsePropertyNamePrefixOperator\",value:function parsePropertyNamePrefixOperator(node){node.variance=this.flowParseVariance();}},{key:\"parseObjPropValue\",value:function parseObjPropValue(prop,startLoc,isGenerator,isAsync,isPattern,isAccessor,refExpressionErrors){if(prop.variance){this.unexpected(prop.variance.loc.start);}delete prop.variance;var typeParameters;if(this.match(47)&&!isAccessor){typeParameters=this.flowParseTypeParameterDeclaration();if(!this.match(10))this.unexpected();}var result=_superPropGet(FlowParserMixin,\"parseObjPropValue\",this,3)([prop,startLoc,isGenerator,isAsync,isPattern,isAccessor,refExpressionErrors]);if(typeParameters){(result.value||result).typeParameters=typeParameters;}return result;}},{key:\"parseFunctionParamType\",value:function parseFunctionParamType(param){if(this.eat(17)){if(param.type!==\"Identifier\"){this.raise(FlowErrors.PatternIsOptional,param);}if(this.isThisParam(param)){this.raise(FlowErrors.ThisParamMayNotBeOptional,param);}param.optional=true;}if(this.match(14)){param.typeAnnotation=this.flowParseTypeAnnotation();}else if(this.isThisParam(param)){this.raise(FlowErrors.ThisParamAnnotationRequired,param);}if(this.match(29)&&this.isThisParam(param)){this.raise(FlowErrors.ThisParamNoDefault,param);}this.resetEndLocation(param);return param;}},{key:\"parseMaybeDefault\",value:function parseMaybeDefault(startLoc,left){var node=_superPropGet(FlowParserMixin,\"parseMaybeDefault\",this,3)([startLoc,left]);if(node.type===\"AssignmentPattern\"&&node.typeAnnotation&&node.right.start<node.typeAnnotation.start){this.raise(FlowErrors.TypeBeforeInitializer,node.typeAnnotation);}return node;}},{key:\"checkImportReflection\",value:function checkImportReflection(node){_superPropGet(FlowParserMixin,\"checkImportReflection\",this,3)([node]);if(node.module&&node.importKind!==\"value\"){this.raise(FlowErrors.ImportReflectionHasImportType,node.specifiers[0].loc.start);}}},{key:\"parseImportSpecifierLocal\",value:function parseImportSpecifierLocal(node,specifier,type){specifier.local=hasTypeImportKind(node)?this.flowParseRestrictedIdentifier(true,true):this.parseIdentifier();node.specifiers.push(this.finishImportSpecifier(specifier,type));}},{key:\"isPotentialImportPhase\",value:function isPotentialImportPhase(isExport){if(_superPropGet(FlowParserMixin,\"isPotentialImportPhase\",this,3)([isExport]))return true;if(this.isContextual(130)){if(!isExport)return true;var ch=this.lookaheadCharCode();return ch===123||ch===42;}return!isExport&&this.isContextual(87);}},{key:\"applyImportPhase\",value:function applyImportPhase(node,isExport,phase,loc){_superPropGet(FlowParserMixin,\"applyImportPhase\",this,3)([node,isExport,phase,loc]);if(isExport){if(!phase&&this.match(65)){return;}node.exportKind=phase===\"type\"?phase:\"value\";}else{if(phase===\"type\"&&this.match(55))this.unexpected();node.importKind=phase===\"type\"||phase===\"typeof\"?phase:\"value\";}}},{key:\"parseImportSpecifier\",value:function parseImportSpecifier(specifier,importedIsString,isInTypeOnlyImport,isMaybeTypeOnly,bindingType){var firstIdent=specifier.imported;var specifierTypeKind=null;if(firstIdent.type===\"Identifier\"){if(firstIdent.name===\"type\"){specifierTypeKind=\"type\";}else if(firstIdent.name===\"typeof\"){specifierTypeKind=\"typeof\";}}var isBinding=false;if(this.isContextual(93)&&!this.isLookaheadContextual(\"as\")){var as_ident=this.parseIdentifier(true);if(specifierTypeKind!==null&&!tokenIsKeywordOrIdentifier(this.state.type)){specifier.imported=as_ident;specifier.importKind=specifierTypeKind;specifier.local=this.cloneIdentifier(as_ident);}else{specifier.imported=firstIdent;specifier.importKind=null;specifier.local=this.parseIdentifier();}}else{if(specifierTypeKind!==null&&tokenIsKeywordOrIdentifier(this.state.type)){specifier.imported=this.parseIdentifier(true);specifier.importKind=specifierTypeKind;}else{if(importedIsString){throw this.raise(Errors.ImportBindingIsString,specifier,{importName:firstIdent.value});}specifier.imported=firstIdent;specifier.importKind=null;}if(this.eatContextual(93)){specifier.local=this.parseIdentifier();}else{isBinding=true;specifier.local=this.cloneIdentifier(specifier.imported);}}var specifierIsTypeImport=hasTypeImportKind(specifier);if(isInTypeOnlyImport&&specifierIsTypeImport){this.raise(FlowErrors.ImportTypeShorthandOnlyInPureImport,specifier);}if(isInTypeOnlyImport||specifierIsTypeImport){this.checkReservedType(specifier.local.name,specifier.local.loc.start,true);}if(isBinding&&!isInTypeOnlyImport&&!specifierIsTypeImport){this.checkReservedWord(specifier.local.name,specifier.loc.start,true,true);}return this.finishImportSpecifier(specifier,\"ImportSpecifier\");}},{key:\"parseBindingAtom\",value:function parseBindingAtom(){switch(this.state.type){case 78:return this.parseIdentifier(true);default:return _superPropGet(FlowParserMixin,\"parseBindingAtom\",this,3)([]);}}},{key:\"parseFunctionParams\",value:function parseFunctionParams(node,isConstructor){var kind=node.kind;if(kind!==\"get\"&&kind!==\"set\"&&this.match(47)){node.typeParameters=this.flowParseTypeParameterDeclaration();}_superPropGet(FlowParserMixin,\"parseFunctionParams\",this,3)([node,isConstructor]);}},{key:\"parseVarId\",value:function parseVarId(decl,kind){_superPropGet(FlowParserMixin,\"parseVarId\",this,3)([decl,kind]);if(this.match(14)){decl.id.typeAnnotation=this.flowParseTypeAnnotation();this.resetEndLocation(decl.id);}}},{key:\"parseAsyncArrowFromCallExpression\",value:function parseAsyncArrowFromCallExpression(node,call){if(this.match(14)){var oldNoAnonFunctionType=this.state.noAnonFunctionType;this.state.noAnonFunctionType=true;node.returnType=this.flowParseTypeAnnotation();this.state.noAnonFunctionType=oldNoAnonFunctionType;}return _superPropGet(FlowParserMixin,\"parseAsyncArrowFromCallExpression\",this,3)([node,call]);}},{key:\"shouldParseAsyncArrow\",value:function shouldParseAsyncArrow(){return this.match(14)||_superPropGet(FlowParserMixin,\"shouldParseAsyncArrow\",this,3)([]);}},{key:\"parseMaybeAssign\",value:function parseMaybeAssign(refExpressionErrors,afterLeftParse){var _this0=this;var _jsx;var state=null;var jsx;if(this.hasPlugin(\"jsx\")&&(this.match(143)||this.match(47))){state=this.state.clone();jsx=this.tryParse(function(){return _superPropGet(FlowParserMixin,\"parseMaybeAssign\",_this0,3)([refExpressionErrors,afterLeftParse]);},state);if(!jsx.error)return jsx.node;var context=this.state.context;var currentContext=context[context.length-1];if(currentContext===types.j_oTag||currentContext===types.j_expr){context.pop();}}if((_jsx=jsx)!=null&&_jsx.error||this.match(47)){var _jsx2,_jsx3;state=state||this.state.clone();var typeParameters;var arrow=this.tryParse(function(abort){var _arrowExpression$extr;typeParameters=_this0.flowParseTypeParameterDeclaration();var arrowExpression=_this0.forwardNoArrowParamsConversionAt(typeParameters,function(){var result=_superPropGet(FlowParserMixin,\"parseMaybeAssign\",_this0,3)([refExpressionErrors,afterLeftParse]);_this0.resetStartLocationFromNode(result,typeParameters);return result;});if((_arrowExpression$extr=arrowExpression.extra)!=null&&_arrowExpression$extr.parenthesized)abort();var expr=_this0.maybeUnwrapTypeCastExpression(arrowExpression);if(expr.type!==\"ArrowFunctionExpression\")abort();expr.typeParameters=typeParameters;_this0.resetStartLocationFromNode(expr,typeParameters);return arrowExpression;},state);var arrowExpression=null;if(arrow.node&&this.maybeUnwrapTypeCastExpression(arrow.node).type===\"ArrowFunctionExpression\"){if(!arrow.error&&!arrow.aborted){if(arrow.node.async){this.raise(FlowErrors.UnexpectedTypeParameterBeforeAsyncArrowFunction,typeParameters);}return arrow.node;}arrowExpression=arrow.node;}if((_jsx2=jsx)!=null&&_jsx2.node){this.state=jsx.failState;return jsx.node;}if(arrowExpression){this.state=arrow.failState;return arrowExpression;}if((_jsx3=jsx)!=null&&_jsx3.thrown)throw jsx.error;if(arrow.thrown)throw arrow.error;throw this.raise(FlowErrors.UnexpectedTokenAfterTypeParameter,typeParameters);}return _superPropGet(FlowParserMixin,\"parseMaybeAssign\",this,3)([refExpressionErrors,afterLeftParse]);}},{key:\"parseArrow\",value:function parseArrow(node){var _this1=this;if(this.match(14)){var result=this.tryParse(function(){var oldNoAnonFunctionType=_this1.state.noAnonFunctionType;_this1.state.noAnonFunctionType=true;var typeNode=_this1.startNode();var _this1$flowParseTypeA=_this1.flowParseTypeAndPredicateInitialiser();var _this1$flowParseTypeA2=_slicedToArray(_this1$flowParseTypeA,2);typeNode.typeAnnotation=_this1$flowParseTypeA2[0];node.predicate=_this1$flowParseTypeA2[1];_this1.state.noAnonFunctionType=oldNoAnonFunctionType;if(_this1.canInsertSemicolon())_this1.unexpected();if(!_this1.match(19))_this1.unexpected();return typeNode;});if(result.thrown)return null;if(result.error)this.state=result.failState;node.returnType=result.node.typeAnnotation?this.finishNode(result.node,\"TypeAnnotation\"):null;}return _superPropGet(FlowParserMixin,\"parseArrow\",this,3)([node]);}},{key:\"shouldParseArrow\",value:function shouldParseArrow(params){return this.match(14)||_superPropGet(FlowParserMixin,\"shouldParseArrow\",this,3)([params]);}},{key:\"setArrowFunctionParameters\",value:function setArrowFunctionParameters(node,params){if(this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(node.start))){node.params=params;}else{_superPropGet(FlowParserMixin,\"setArrowFunctionParameters\",this,3)([node,params]);}}},{key:\"checkParams\",value:function checkParams(node,allowDuplicates,isArrowFunction){var strictModeChanged=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;if(isArrowFunction&&this.state.noArrowParamsConversionAt.includes(this.offsetToSourcePos(node.start))){return;}for(var i=0;i<node.params.length;i++){if(this.isThisParam(node.params[i])&&i>0){this.raise(FlowErrors.ThisParamMustBeFirst,node.params[i]);}}_superPropGet(FlowParserMixin,\"checkParams\",this,3)([node,allowDuplicates,isArrowFunction,strictModeChanged]);}},{key:\"parseParenAndDistinguishExpression\",value:function parseParenAndDistinguishExpression(canBeArrow){return _superPropGet(FlowParserMixin,\"parseParenAndDistinguishExpression\",this,3)([canBeArrow&&!this.state.noArrowAt.includes(this.sourceToOffsetPos(this.state.start))]);}},{key:\"parseSubscripts\",value:function parseSubscripts(base,startLoc,noCalls){var _this10=this;if(base.type===\"Identifier\"&&base.name===\"async\"&&this.state.noArrowAt.includes(startLoc.index)){this.next();var node=this.startNodeAt(startLoc);node.callee=base;node.arguments=_superPropGet(FlowParserMixin,\"parseCallExpressionArguments\",this,3)([]);base=this.finishNode(node,\"CallExpression\");}else if(base.type===\"Identifier\"&&base.name===\"async\"&&this.match(47)){var state=this.state.clone();var arrow=this.tryParse(function(abort){return _this10.parseAsyncArrowWithTypeParameters(startLoc)||abort();},state);if(!arrow.error&&!arrow.aborted)return arrow.node;var result=this.tryParse(function(){return _superPropGet(FlowParserMixin,\"parseSubscripts\",_this10,3)([base,startLoc,noCalls]);},state);if(result.node&&!result.error)return result.node;if(arrow.node){this.state=arrow.failState;return arrow.node;}if(result.node){this.state=result.failState;return result.node;}throw arrow.error||result.error;}return _superPropGet(FlowParserMixin,\"parseSubscripts\",this,3)([base,startLoc,noCalls]);}},{key:\"parseSubscript\",value:function parseSubscript(base,startLoc,noCalls,subscriptState){var _this11=this;if(this.match(18)&&this.isLookaheadToken_lt()){subscriptState.optionalChainMember=true;if(noCalls){subscriptState.stop=true;return base;}this.next();var node=this.startNodeAt(startLoc);node.callee=base;node.typeArguments=this.flowParseTypeParameterInstantiationInExpression();this.expect(10);node.arguments=this.parseCallExpressionArguments();node.optional=true;return this.finishCallExpression(node,true);}else if(!noCalls&&this.shouldParseTypes()&&(this.match(47)||this.match(51))){var _node6=this.startNodeAt(startLoc);_node6.callee=base;var result=this.tryParse(function(){_node6.typeArguments=_this11.flowParseTypeParameterInstantiationCallOrNew();_this11.expect(10);_node6.arguments=_superPropGet(FlowParserMixin,\"parseCallExpressionArguments\",_this11,3)([]);if(subscriptState.optionalChainMember){_node6.optional=false;}return _this11.finishCallExpression(_node6,subscriptState.optionalChainMember);});if(result.node){if(result.error)this.state=result.failState;return result.node;}}return _superPropGet(FlowParserMixin,\"parseSubscript\",this,3)([base,startLoc,noCalls,subscriptState]);}},{key:\"parseNewCallee\",value:function parseNewCallee(node){var _this12=this;_superPropGet(FlowParserMixin,\"parseNewCallee\",this,3)([node]);var targs=null;if(this.shouldParseTypes()&&this.match(47)){targs=this.tryParse(function(){return _this12.flowParseTypeParameterInstantiationCallOrNew();}).node;}node.typeArguments=targs;}},{key:\"parseAsyncArrowWithTypeParameters\",value:function parseAsyncArrowWithTypeParameters(startLoc){var node=this.startNodeAt(startLoc);this.parseFunctionParams(node,false);if(!this.parseArrow(node))return;return _superPropGet(FlowParserMixin,\"parseArrowExpression\",this,3)([node,undefined,true]);}},{key:\"readToken_mult_modulo\",value:function readToken_mult_modulo(code){var next=this.input.charCodeAt(this.state.pos+1);if(code===42&&next===47&&this.state.hasFlowComment){this.state.hasFlowComment=false;this.state.pos+=2;this.nextToken();return;}_superPropGet(FlowParserMixin,\"readToken_mult_modulo\",this,3)([code]);}},{key:\"readToken_pipe_amp\",value:function readToken_pipe_amp(code){var next=this.input.charCodeAt(this.state.pos+1);if(code===124&&next===125){this.finishOp(9,2);return;}_superPropGet(FlowParserMixin,\"readToken_pipe_amp\",this,3)([code]);}},{key:\"parseTopLevel\",value:function parseTopLevel(file,program){var fileNode=_superPropGet(FlowParserMixin,\"parseTopLevel\",this,3)([file,program]);if(this.state.hasFlowComment){this.raise(FlowErrors.UnterminatedFlowComment,this.state.curPosition());}return fileNode;}},{key:\"skipBlockComment\",value:function skipBlockComment(){if(this.hasPlugin(\"flowComments\")&&this.skipFlowComment()){if(this.state.hasFlowComment){throw this.raise(FlowErrors.NestedFlowComment,this.state.startLoc);}this.hasFlowCommentCompletion();var commentSkip=this.skipFlowComment();if(commentSkip){this.state.pos+=commentSkip;this.state.hasFlowComment=true;}return;}return _superPropGet(FlowParserMixin,\"skipBlockComment\",this,3)([this.state.hasFlowComment?\"*-/\":\"*/\"]);}},{key:\"skipFlowComment\",value:function skipFlowComment(){var pos=this.state.pos;var shiftToFirstNonWhiteSpace=2;while([32,9].includes(this.input.charCodeAt(pos+shiftToFirstNonWhiteSpace))){shiftToFirstNonWhiteSpace++;}var ch2=this.input.charCodeAt(shiftToFirstNonWhiteSpace+pos);var ch3=this.input.charCodeAt(shiftToFirstNonWhiteSpace+pos+1);if(ch2===58&&ch3===58){return shiftToFirstNonWhiteSpace+2;}if(this.input.slice(shiftToFirstNonWhiteSpace+pos,shiftToFirstNonWhiteSpace+pos+12)===\"flow-include\"){return shiftToFirstNonWhiteSpace+12;}if(ch2===58&&ch3!==58){return shiftToFirstNonWhiteSpace;}return false;}},{key:\"hasFlowCommentCompletion\",value:function hasFlowCommentCompletion(){var end=this.input.indexOf(\"*/\",this.state.pos);if(end===-1){throw this.raise(Errors.UnterminatedComment,this.state.curPosition());}}},{key:\"flowEnumErrorBooleanMemberNotInitialized\",value:function flowEnumErrorBooleanMemberNotInitialized(loc,_ref52){var enumName=_ref52.enumName,memberName=_ref52.memberName;this.raise(FlowErrors.EnumBooleanMemberNotInitialized,loc,{memberName:memberName,enumName:enumName});}},{key:\"flowEnumErrorInvalidMemberInitializer\",value:function flowEnumErrorInvalidMemberInitializer(loc,enumContext){return this.raise(!enumContext.explicitType?FlowErrors.EnumInvalidMemberInitializerUnknownType:enumContext.explicitType===\"symbol\"?FlowErrors.EnumInvalidMemberInitializerSymbolType:FlowErrors.EnumInvalidMemberInitializerPrimaryType,loc,enumContext);}},{key:\"flowEnumErrorNumberMemberNotInitialized\",value:function flowEnumErrorNumberMemberNotInitialized(loc,details){this.raise(FlowErrors.EnumNumberMemberNotInitialized,loc,details);}},{key:\"flowEnumErrorStringMemberInconsistentlyInitialized\",value:function flowEnumErrorStringMemberInconsistentlyInitialized(node,details){this.raise(FlowErrors.EnumStringMemberInconsistentlyInitialized,node,details);}},{key:\"flowEnumMemberInit\",value:function flowEnumMemberInit(){var _this13=this;var startLoc=this.state.startLoc;var endOfInit=function endOfInit(){return _this13.match(12)||_this13.match(8);};switch(this.state.type){case 135:{var literal=this.parseNumericLiteral(this.state.value);if(endOfInit()){return{type:\"number\",loc:literal.loc.start,value:literal};}return{type:\"invalid\",loc:startLoc};}case 134:{var _literal=this.parseStringLiteral(this.state.value);if(endOfInit()){return{type:\"string\",loc:_literal.loc.start,value:_literal};}return{type:\"invalid\",loc:startLoc};}case 85:case 86:{var _literal2=this.parseBooleanLiteral(this.match(85));if(endOfInit()){return{type:\"boolean\",loc:_literal2.loc.start,value:_literal2};}return{type:\"invalid\",loc:startLoc};}default:return{type:\"invalid\",loc:startLoc};}}},{key:\"flowEnumMemberRaw\",value:function flowEnumMemberRaw(){var loc=this.state.startLoc;var id=this.parseIdentifier(true);var init=this.eat(29)?this.flowEnumMemberInit():{type:\"none\",loc:loc};return{id:id,init:init};}},{key:\"flowEnumCheckExplicitTypeMismatch\",value:function flowEnumCheckExplicitTypeMismatch(loc,context,expectedType){var explicitType=context.explicitType;if(explicitType===null){return;}if(explicitType!==expectedType){this.flowEnumErrorInvalidMemberInitializer(loc,context);}}},{key:\"flowEnumMembers\",value:function flowEnumMembers(_ref53){var enumName=_ref53.enumName,explicitType=_ref53.explicitType;var seenNames=new Set();var members={booleanMembers:[],numberMembers:[],stringMembers:[],defaultedMembers:[]};var hasUnknownMembers=false;while(!this.match(8)){if(this.eat(21)){hasUnknownMembers=true;break;}var memberNode=this.startNode();var _this$flowEnumMemberR=this.flowEnumMemberRaw(),id=_this$flowEnumMemberR.id,init=_this$flowEnumMemberR.init;var memberName=id.name;if(memberName===\"\"){continue;}if(/^[a-z]/.test(memberName)){this.raise(FlowErrors.EnumInvalidMemberName,id,{memberName:memberName,suggestion:memberName[0].toUpperCase()+memberName.slice(1),enumName:enumName});}if(seenNames.has(memberName)){this.raise(FlowErrors.EnumDuplicateMemberName,id,{memberName:memberName,enumName:enumName});}seenNames.add(memberName);var context={enumName:enumName,explicitType:explicitType,memberName:memberName};memberNode.id=id;switch(init.type){case\"boolean\":{this.flowEnumCheckExplicitTypeMismatch(init.loc,context,\"boolean\");memberNode.init=init.value;members.booleanMembers.push(this.finishNode(memberNode,\"EnumBooleanMember\"));break;}case\"number\":{this.flowEnumCheckExplicitTypeMismatch(init.loc,context,\"number\");memberNode.init=init.value;members.numberMembers.push(this.finishNode(memberNode,\"EnumNumberMember\"));break;}case\"string\":{this.flowEnumCheckExplicitTypeMismatch(init.loc,context,\"string\");memberNode.init=init.value;members.stringMembers.push(this.finishNode(memberNode,\"EnumStringMember\"));break;}case\"invalid\":{throw this.flowEnumErrorInvalidMemberInitializer(init.loc,context);}case\"none\":{switch(explicitType){case\"boolean\":this.flowEnumErrorBooleanMemberNotInitialized(init.loc,context);break;case\"number\":this.flowEnumErrorNumberMemberNotInitialized(init.loc,context);break;default:members.defaultedMembers.push(this.finishNode(memberNode,\"EnumDefaultedMember\"));}}}if(!this.match(8)){this.expect(12);}}return{members:members,hasUnknownMembers:hasUnknownMembers};}},{key:\"flowEnumStringMembers\",value:function flowEnumStringMembers(initializedMembers,defaultedMembers,_ref54){var enumName=_ref54.enumName;if(initializedMembers.length===0){return defaultedMembers;}else if(defaultedMembers.length===0){return initializedMembers;}else if(defaultedMembers.length>initializedMembers.length){var _iterator=_createForOfIteratorHelper(initializedMembers),_step;try{for(_iterator.s();!(_step=_iterator.n()).done;){var member=_step.value;this.flowEnumErrorStringMemberInconsistentlyInitialized(member,{enumName:enumName});}}catch(err){_iterator.e(err);}finally{_iterator.f();}return defaultedMembers;}else{var _iterator2=_createForOfIteratorHelper(defaultedMembers),_step2;try{for(_iterator2.s();!(_step2=_iterator2.n()).done;){var _member=_step2.value;this.flowEnumErrorStringMemberInconsistentlyInitialized(_member,{enumName:enumName});}}catch(err){_iterator2.e(err);}finally{_iterator2.f();}return initializedMembers;}}},{key:\"flowEnumParseExplicitType\",value:function flowEnumParseExplicitType(_ref55){var enumName=_ref55.enumName;if(!this.eatContextual(102))return null;if(!tokenIsIdentifier(this.state.type)){throw this.raise(FlowErrors.EnumInvalidExplicitTypeUnknownSupplied,this.state.startLoc,{enumName:enumName});}var value=this.state.value;this.next();if(value!==\"boolean\"&&value!==\"number\"&&value!==\"string\"&&value!==\"symbol\"){this.raise(FlowErrors.EnumInvalidExplicitType,this.state.startLoc,{enumName:enumName,invalidEnumType:value});}return value;}},{key:\"flowEnumBody\",value:function flowEnumBody(node,id){var _this14=this;var enumName=id.name;var nameLoc=id.loc.start;var explicitType=this.flowEnumParseExplicitType({enumName:enumName});this.expect(5);var _this$flowEnumMembers=this.flowEnumMembers({enumName:enumName,explicitType:explicitType}),members=_this$flowEnumMembers.members,hasUnknownMembers=_this$flowEnumMembers.hasUnknownMembers;node.hasUnknownMembers=hasUnknownMembers;switch(explicitType){case\"boolean\":node.explicitType=true;node.members=members.booleanMembers;this.expect(8);return this.finishNode(node,\"EnumBooleanBody\");case\"number\":node.explicitType=true;node.members=members.numberMembers;this.expect(8);return this.finishNode(node,\"EnumNumberBody\");case\"string\":node.explicitType=true;node.members=this.flowEnumStringMembers(members.stringMembers,members.defaultedMembers,{enumName:enumName});this.expect(8);return this.finishNode(node,\"EnumStringBody\");case\"symbol\":node.members=members.defaultedMembers;this.expect(8);return this.finishNode(node,\"EnumSymbolBody\");default:{var empty=function empty(){node.members=[];_this14.expect(8);return _this14.finishNode(node,\"EnumStringBody\");};node.explicitType=false;var boolsLen=members.booleanMembers.length;var numsLen=members.numberMembers.length;var strsLen=members.stringMembers.length;var defaultedLen=members.defaultedMembers.length;if(!boolsLen&&!numsLen&&!strsLen&&!defaultedLen){return empty();}else if(!boolsLen&&!numsLen){node.members=this.flowEnumStringMembers(members.stringMembers,members.defaultedMembers,{enumName:enumName});this.expect(8);return this.finishNode(node,\"EnumStringBody\");}else if(!numsLen&&!strsLen&&boolsLen>=defaultedLen){var _iterator3=_createForOfIteratorHelper(members.defaultedMembers),_step3;try{for(_iterator3.s();!(_step3=_iterator3.n()).done;){var member=_step3.value;this.flowEnumErrorBooleanMemberNotInitialized(member.loc.start,{enumName:enumName,memberName:member.id.name});}}catch(err){_iterator3.e(err);}finally{_iterator3.f();}node.members=members.booleanMembers;this.expect(8);return this.finishNode(node,\"EnumBooleanBody\");}else if(!boolsLen&&!strsLen&&numsLen>=defaultedLen){var _iterator4=_createForOfIteratorHelper(members.defaultedMembers),_step4;try{for(_iterator4.s();!(_step4=_iterator4.n()).done;){var _member2=_step4.value;this.flowEnumErrorNumberMemberNotInitialized(_member2.loc.start,{enumName:enumName,memberName:_member2.id.name});}}catch(err){_iterator4.e(err);}finally{_iterator4.f();}node.members=members.numberMembers;this.expect(8);return this.finishNode(node,\"EnumNumberBody\");}else{this.raise(FlowErrors.EnumInconsistentMemberValues,nameLoc,{enumName:enumName});return empty();}}}}},{key:\"flowParseEnumDeclaration\",value:function flowParseEnumDeclaration(node){var id=this.parseIdentifier();node.id=id;node.body=this.flowEnumBody(this.startNode(),id);return this.finishNode(node,\"EnumDeclaration\");}},{key:\"jsxParseOpeningElementAfterName\",value:function jsxParseOpeningElementAfterName(node){if(this.shouldParseTypes()){if(this.match(47)||this.match(51)){node.typeArguments=this.flowParseTypeParameterInstantiationInExpression();}}return _superPropGet(FlowParserMixin,\"jsxParseOpeningElementAfterName\",this,3)([node]);}},{key:\"isLookaheadToken_lt\",value:function isLookaheadToken_lt(){var next=this.nextTokenStart();if(this.input.charCodeAt(next)===60){var afterNext=this.input.charCodeAt(next+1);return afterNext!==60&&afterNext!==61;}return false;}},{key:\"reScan_lt_gt\",value:function reScan_lt_gt(){var type=this.state.type;if(type===47){this.state.pos-=1;this.readToken_lt();}else if(type===48){this.state.pos-=1;this.readToken_gt();}}},{key:\"reScan_lt\",value:function reScan_lt(){var type=this.state.type;if(type===51){this.state.pos-=2;this.finishOp(47,1);return 47;}return type;}},{key:\"maybeUnwrapTypeCastExpression\",value:function maybeUnwrapTypeCastExpression(node){return node.type===\"TypeCastExpression\"?node.expression:node;}}]);}(superClass);};var entities={__proto__:null,quot:\"\\\"\",amp:\"&\",apos:\"'\",lt:\"<\",gt:\">\",nbsp:\"\\xA0\",iexcl:\"\\xA1\",cent:\"\\xA2\",pound:\"\\xA3\",curren:\"\\xA4\",yen:\"\\xA5\",brvbar:\"\\xA6\",sect:\"\\xA7\",uml:\"\\xA8\",copy:\"\\xA9\",ordf:\"\\xAA\",laquo:\"\\xAB\",not:\"\\xAC\",shy:\"\\xAD\",reg:\"\\xAE\",macr:\"\\xAF\",deg:\"\\xB0\",plusmn:\"\\xB1\",sup2:\"\\xB2\",sup3:\"\\xB3\",acute:\"\\xB4\",micro:\"\\xB5\",para:\"\\xB6\",middot:\"\\xB7\",cedil:\"\\xB8\",sup1:\"\\xB9\",ordm:\"\\xBA\",raquo:\"\\xBB\",frac14:\"\\xBC\",frac12:\"\\xBD\",frac34:\"\\xBE\",iquest:\"\\xBF\",Agrave:\"\\xC0\",Aacute:\"\\xC1\",Acirc:\"\\xC2\",Atilde:\"\\xC3\",Auml:\"\\xC4\",Aring:\"\\xC5\",AElig:\"\\xC6\",Ccedil:\"\\xC7\",Egrave:\"\\xC8\",Eacute:\"\\xC9\",Ecirc:\"\\xCA\",Euml:\"\\xCB\",Igrave:\"\\xCC\",Iacute:\"\\xCD\",Icirc:\"\\xCE\",Iuml:\"\\xCF\",ETH:\"\\xD0\",Ntilde:\"\\xD1\",Ograve:\"\\xD2\",Oacute:\"\\xD3\",Ocirc:\"\\xD4\",Otilde:\"\\xD5\",Ouml:\"\\xD6\",times:\"\\xD7\",Oslash:\"\\xD8\",Ugrave:\"\\xD9\",Uacute:\"\\xDA\",Ucirc:\"\\xDB\",Uuml:\"\\xDC\",Yacute:\"\\xDD\",THORN:\"\\xDE\",szlig:\"\\xDF\",agrave:\"\\xE0\",aacute:\"\\xE1\",acirc:\"\\xE2\",atilde:\"\\xE3\",auml:\"\\xE4\",aring:\"\\xE5\",aelig:\"\\xE6\",ccedil:\"\\xE7\",egrave:\"\\xE8\",eacute:\"\\xE9\",ecirc:\"\\xEA\",euml:\"\\xEB\",igrave:\"\\xEC\",iacute:\"\\xED\",icirc:\"\\xEE\",iuml:\"\\xEF\",eth:\"\\xF0\",ntilde:\"\\xF1\",ograve:\"\\xF2\",oacute:\"\\xF3\",ocirc:\"\\xF4\",otilde:\"\\xF5\",ouml:\"\\xF6\",divide:\"\\xF7\",oslash:\"\\xF8\",ugrave:\"\\xF9\",uacute:\"\\xFA\",ucirc:\"\\xFB\",uuml:\"\\xFC\",yacute:\"\\xFD\",thorn:\"\\xFE\",yuml:\"\\xFF\",OElig:\"\\u0152\",oelig:\"\\u0153\",Scaron:\"\\u0160\",scaron:\"\\u0161\",Yuml:\"\\u0178\",fnof:\"\\u0192\",circ:\"\\u02C6\",tilde:\"\\u02DC\",Alpha:\"\\u0391\",Beta:\"\\u0392\",Gamma:\"\\u0393\",Delta:\"\\u0394\",Epsilon:\"\\u0395\",Zeta:\"\\u0396\",Eta:\"\\u0397\",Theta:\"\\u0398\",Iota:\"\\u0399\",Kappa:\"\\u039A\",Lambda:\"\\u039B\",Mu:\"\\u039C\",Nu:\"\\u039D\",Xi:\"\\u039E\",Omicron:\"\\u039F\",Pi:\"\\u03A0\",Rho:\"\\u03A1\",Sigma:\"\\u03A3\",Tau:\"\\u03A4\",Upsilon:\"\\u03A5\",Phi:\"\\u03A6\",Chi:\"\\u03A7\",Psi:\"\\u03A8\",Omega:\"\\u03A9\",alpha:\"\\u03B1\",beta:\"\\u03B2\",gamma:\"\\u03B3\",delta:\"\\u03B4\",epsilon:\"\\u03B5\",zeta:\"\\u03B6\",eta:\"\\u03B7\",theta:\"\\u03B8\",iota:\"\\u03B9\",kappa:\"\\u03BA\",lambda:\"\\u03BB\",mu:\"\\u03BC\",nu:\"\\u03BD\",xi:\"\\u03BE\",omicron:\"\\u03BF\",pi:\"\\u03C0\",rho:\"\\u03C1\",sigmaf:\"\\u03C2\",sigma:\"\\u03C3\",tau:\"\\u03C4\",upsilon:\"\\u03C5\",phi:\"\\u03C6\",chi:\"\\u03C7\",psi:\"\\u03C8\",omega:\"\\u03C9\",thetasym:\"\\u03D1\",upsih:\"\\u03D2\",piv:\"\\u03D6\",ensp:\"\\u2002\",emsp:\"\\u2003\",thinsp:\"\\u2009\",zwnj:\"\\u200C\",zwj:\"\\u200D\",lrm:\"\\u200E\",rlm:\"\\u200F\",ndash:\"\\u2013\",mdash:\"\\u2014\",lsquo:\"\\u2018\",rsquo:\"\\u2019\",sbquo:\"\\u201A\",ldquo:\"\\u201C\",rdquo:\"\\u201D\",bdquo:\"\\u201E\",dagger:\"\\u2020\",Dagger:\"\\u2021\",bull:\"\\u2022\",hellip:\"\\u2026\",permil:\"\\u2030\",prime:\"\\u2032\",Prime:\"\\u2033\",lsaquo:\"\\u2039\",rsaquo:\"\\u203A\",oline:\"\\u203E\",frasl:\"\\u2044\",euro:\"\\u20AC\",image:\"\\u2111\",weierp:\"\\u2118\",real:\"\\u211C\",trade:\"\\u2122\",alefsym:\"\\u2135\",larr:\"\\u2190\",uarr:\"\\u2191\",rarr:\"\\u2192\",darr:\"\\u2193\",harr:\"\\u2194\",crarr:\"\\u21B5\",lArr:\"\\u21D0\",uArr:\"\\u21D1\",rArr:\"\\u21D2\",dArr:\"\\u21D3\",hArr:\"\\u21D4\",forall:\"\\u2200\",part:\"\\u2202\",exist:\"\\u2203\",empty:\"\\u2205\",nabla:\"\\u2207\",isin:\"\\u2208\",notin:\"\\u2209\",ni:\"\\u220B\",prod:\"\\u220F\",sum:\"\\u2211\",minus:\"\\u2212\",lowast:\"\\u2217\",radic:\"\\u221A\",prop:\"\\u221D\",infin:\"\\u221E\",ang:\"\\u2220\",and:\"\\u2227\",or:\"\\u2228\",cap:\"\\u2229\",cup:\"\\u222A\",int:\"\\u222B\",there4:\"\\u2234\",sim:\"\\u223C\",cong:\"\\u2245\",asymp:\"\\u2248\",ne:\"\\u2260\",equiv:\"\\u2261\",le:\"\\u2264\",ge:\"\\u2265\",sub:\"\\u2282\",sup:\"\\u2283\",nsub:\"\\u2284\",sube:\"\\u2286\",supe:\"\\u2287\",oplus:\"\\u2295\",otimes:\"\\u2297\",perp:\"\\u22A5\",sdot:\"\\u22C5\",lceil:\"\\u2308\",rceil:\"\\u2309\",lfloor:\"\\u230A\",rfloor:\"\\u230B\",lang:\"\\u2329\",rang:\"\\u232A\",loz:\"\\u25CA\",spades:\"\\u2660\",clubs:\"\\u2663\",hearts:\"\\u2665\",diams:\"\\u2666\"};var lineBreak=/\\r\\n|[\\r\\n\\u2028\\u2029]/;var lineBreakG=new RegExp(lineBreak.source,\"g\");function isNewLine(code){switch(code){case 10:case 13:case 8232:case 8233:return true;default:return false;}}function hasNewLine(input,start,end){for(var i=start;i<end;i++){if(isNewLine(input.charCodeAt(i))){return true;}}return false;}var skipWhiteSpace=/(?:\\s|\\/\\/.*|\\/\\*[^]*?\\*\\/)*/g;var skipWhiteSpaceInLine=/(?:[^\\S\\n\\r\\u2028\\u2029]|\\/\\/.*|\\/\\*.*?\\*\\/)*/g;function isWhitespace(code){switch(code){case 0x0009:case 0x000b:case 0x000c:case 32:case 160:case 5760:case 0x2000:case 0x2001:case 0x2002:case 0x2003:case 0x2004:case 0x2005:case 0x2006:case 0x2007:case 0x2008:case 0x2009:case 0x200a:case 0x202f:case 0x205f:case 0x3000:case 0xfeff:return true;default:return false;}}var JsxErrors=ParseErrorEnum(_templateObject3||(_templateObject3=_taggedTemplateLiteral([\"jsx\"])))({AttributeIsEmpty:\"JSX attributes must only be assigned a non-empty expression.\",MissingClosingTagElement:function MissingClosingTagElement(_ref56){var openingTagName=_ref56.openingTagName;return\"Expected corresponding JSX closing tag for <\".concat(openingTagName,\">.\");},MissingClosingTagFragment:\"Expected corresponding JSX closing tag for <>.\",UnexpectedSequenceExpression:\"Sequence expressions cannot be directly nested inside JSX. Did you mean to wrap it in parentheses (...)?\",UnexpectedToken:function UnexpectedToken(_ref57){var unexpected=_ref57.unexpected,HTMLEntity=_ref57.HTMLEntity;return\"Unexpected token `\".concat(unexpected,\"`. Did you mean `\").concat(HTMLEntity,\"` or `{'\").concat(unexpected,\"'}`?\");},UnsupportedJsxValue:\"JSX value should be either an expression or a quoted JSX text.\",UnterminatedJsxContent:\"Unterminated JSX contents.\",UnwrappedAdjacentJSXElements:\"Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>?\"});function isFragment(object){return object?object.type===\"JSXOpeningFragment\"||object.type===\"JSXClosingFragment\":false;}function getQualifiedJSXName(object){if(object.type===\"JSXIdentifier\"){return object.name;}if(object.type===\"JSXNamespacedName\"){return object.namespace.name+\":\"+object.name.name;}if(object.type===\"JSXMemberExpression\"){return getQualifiedJSXName(object.object)+\".\"+getQualifiedJSXName(object.property);}throw new Error(\"Node had unexpected type: \"+object.type);}var jsx=function jsx(superClass){return/*#__PURE__*/function(_superClass3){function JSXParserMixin(){_classCallCheck(this,JSXParserMixin);return _callSuper(this,JSXParserMixin,arguments);}_inherits(JSXParserMixin,_superClass3);return _createClass(JSXParserMixin,[{key:\"jsxReadToken\",value:function jsxReadToken(){var out=\"\";var chunkStart=this.state.pos;for(;;){if(this.state.pos>=this.length){throw this.raise(JsxErrors.UnterminatedJsxContent,this.state.startLoc);}var ch=this.input.charCodeAt(this.state.pos);switch(ch){case 60:case 123:if(this.state.pos===this.state.start){if(ch===60&&this.state.canStartJSXElement){++this.state.pos;this.finishToken(143);}else{_superPropGet(JSXParserMixin,\"getTokenFromCode\",this,3)([ch]);}return;}out+=this.input.slice(chunkStart,this.state.pos);this.finishToken(142,out);return;case 38:out+=this.input.slice(chunkStart,this.state.pos);out+=this.jsxReadEntity();chunkStart=this.state.pos;break;case 62:case 125:default:if(isNewLine(ch)){out+=this.input.slice(chunkStart,this.state.pos);out+=this.jsxReadNewLine(true);chunkStart=this.state.pos;}else{++this.state.pos;}}}}},{key:\"jsxReadNewLine\",value:function jsxReadNewLine(normalizeCRLF){var ch=this.input.charCodeAt(this.state.pos);var out;++this.state.pos;if(ch===13&&this.input.charCodeAt(this.state.pos)===10){++this.state.pos;out=normalizeCRLF?\"\\n\":\"\\r\\n\";}else{out=String.fromCharCode(ch);}++this.state.curLine;this.state.lineStart=this.state.pos;return out;}},{key:\"jsxReadString\",value:function jsxReadString(quote){var out=\"\";var chunkStart=++this.state.pos;for(;;){if(this.state.pos>=this.length){throw this.raise(Errors.UnterminatedString,this.state.startLoc);}var ch=this.input.charCodeAt(this.state.pos);if(ch===quote)break;if(ch===38){out+=this.input.slice(chunkStart,this.state.pos);out+=this.jsxReadEntity();chunkStart=this.state.pos;}else if(isNewLine(ch)){out+=this.input.slice(chunkStart,this.state.pos);out+=this.jsxReadNewLine(false);chunkStart=this.state.pos;}else{++this.state.pos;}}out+=this.input.slice(chunkStart,this.state.pos++);this.finishToken(134,out);}},{key:\"jsxReadEntity\",value:function jsxReadEntity(){var startPos=++this.state.pos;if(this.codePointAtPos(this.state.pos)===35){++this.state.pos;var radix=10;if(this.codePointAtPos(this.state.pos)===120){radix=16;++this.state.pos;}var codePoint=this.readInt(radix,undefined,false,\"bail\");if(codePoint!==null&&this.codePointAtPos(this.state.pos)===59){++this.state.pos;return String.fromCodePoint(codePoint);}}else{var count=0;var semi=false;while(count++<10&&this.state.pos<this.length&&!(semi=this.codePointAtPos(this.state.pos)===59)){++this.state.pos;}if(semi){var desc=this.input.slice(startPos,this.state.pos);var entity=entities[desc];++this.state.pos;if(entity){return entity;}}}this.state.pos=startPos;return\"&\";}},{key:\"jsxReadWord\",value:function jsxReadWord(){var ch;var start=this.state.pos;do{ch=this.input.charCodeAt(++this.state.pos);}while(isIdentifierChar(ch)||ch===45);this.finishToken(141,this.input.slice(start,this.state.pos));}},{key:\"jsxParseIdentifier\",value:function jsxParseIdentifier(){var node=this.startNode();if(this.match(141)){node.name=this.state.value;}else if(tokenIsKeyword(this.state.type)){node.name=tokenLabelName(this.state.type);}else{this.unexpected();}this.next();return this.finishNode(node,\"JSXIdentifier\");}},{key:\"jsxParseNamespacedName\",value:function jsxParseNamespacedName(){var startLoc=this.state.startLoc;var name=this.jsxParseIdentifier();if(!this.eat(14))return name;var node=this.startNodeAt(startLoc);node.namespace=name;node.name=this.jsxParseIdentifier();return this.finishNode(node,\"JSXNamespacedName\");}},{key:\"jsxParseElementName\",value:function jsxParseElementName(){var startLoc=this.state.startLoc;var node=this.jsxParseNamespacedName();if(node.type===\"JSXNamespacedName\"){return node;}while(this.eat(16)){var newNode=this.startNodeAt(startLoc);newNode.object=node;newNode.property=this.jsxParseIdentifier();node=this.finishNode(newNode,\"JSXMemberExpression\");}return node;}},{key:\"jsxParseAttributeValue\",value:function jsxParseAttributeValue(){var node;switch(this.state.type){case 5:node=this.startNode();this.setContext(types.brace);this.next();node=this.jsxParseExpressionContainer(node,types.j_oTag);if(node.expression.type===\"JSXEmptyExpression\"){this.raise(JsxErrors.AttributeIsEmpty,node);}return node;case 143:case 134:return this.parseExprAtom();default:throw this.raise(JsxErrors.UnsupportedJsxValue,this.state.startLoc);}}},{key:\"jsxParseEmptyExpression\",value:function jsxParseEmptyExpression(){var node=this.startNodeAt(this.state.lastTokEndLoc);return this.finishNodeAt(node,\"JSXEmptyExpression\",this.state.startLoc);}},{key:\"jsxParseSpreadChild\",value:function jsxParseSpreadChild(node){this.next();node.expression=this.parseExpression();this.setContext(types.j_expr);this.state.canStartJSXElement=true;this.expect(8);return this.finishNode(node,\"JSXSpreadChild\");}},{key:\"jsxParseExpressionContainer\",value:function jsxParseExpressionContainer(node,previousContext){if(this.match(8)){node.expression=this.jsxParseEmptyExpression();}else{var expression=this.parseExpression();node.expression=expression;}this.setContext(previousContext);this.state.canStartJSXElement=true;this.expect(8);return this.finishNode(node,\"JSXExpressionContainer\");}},{key:\"jsxParseAttribute\",value:function jsxParseAttribute(){var node=this.startNode();if(this.match(5)){this.setContext(types.brace);this.next();this.expect(21);node.argument=this.parseMaybeAssignAllowIn();this.setContext(types.j_oTag);this.state.canStartJSXElement=true;this.expect(8);return this.finishNode(node,\"JSXSpreadAttribute\");}node.name=this.jsxParseNamespacedName();node.value=this.eat(29)?this.jsxParseAttributeValue():null;return this.finishNode(node,\"JSXAttribute\");}},{key:\"jsxParseOpeningElementAt\",value:function jsxParseOpeningElementAt(startLoc){var node=this.startNodeAt(startLoc);if(this.eat(144)){return this.finishNode(node,\"JSXOpeningFragment\");}node.name=this.jsxParseElementName();return this.jsxParseOpeningElementAfterName(node);}},{key:\"jsxParseOpeningElementAfterName\",value:function jsxParseOpeningElementAfterName(node){var attributes=[];while(!this.match(56)&&!this.match(144)){attributes.push(this.jsxParseAttribute());}node.attributes=attributes;node.selfClosing=this.eat(56);this.expect(144);return this.finishNode(node,\"JSXOpeningElement\");}},{key:\"jsxParseClosingElementAt\",value:function jsxParseClosingElementAt(startLoc){var node=this.startNodeAt(startLoc);if(this.eat(144)){return this.finishNode(node,\"JSXClosingFragment\");}node.name=this.jsxParseElementName();this.expect(144);return this.finishNode(node,\"JSXClosingElement\");}},{key:\"jsxParseElementAt\",value:function jsxParseElementAt(startLoc){var node=this.startNodeAt(startLoc);var children=[];var openingElement=this.jsxParseOpeningElementAt(startLoc);var closingElement=null;if(!openingElement.selfClosing){contents:for(;;){switch(this.state.type){case 143:startLoc=this.state.startLoc;this.next();if(this.eat(56)){closingElement=this.jsxParseClosingElementAt(startLoc);break contents;}children.push(this.jsxParseElementAt(startLoc));break;case 142:children.push(this.parseLiteral(this.state.value,\"JSXText\"));break;case 5:{var _node7=this.startNode();this.setContext(types.brace);this.next();if(this.match(21)){children.push(this.jsxParseSpreadChild(_node7));}else{children.push(this.jsxParseExpressionContainer(_node7,types.j_expr));}break;}default:this.unexpected();}}if(isFragment(openingElement)&&!isFragment(closingElement)&&closingElement!==null){this.raise(JsxErrors.MissingClosingTagFragment,closingElement);}else if(!isFragment(openingElement)&&isFragment(closingElement)){this.raise(JsxErrors.MissingClosingTagElement,closingElement,{openingTagName:getQualifiedJSXName(openingElement.name)});}else if(!isFragment(openingElement)&&!isFragment(closingElement)){if(getQualifiedJSXName(closingElement.name)!==getQualifiedJSXName(openingElement.name)){this.raise(JsxErrors.MissingClosingTagElement,closingElement,{openingTagName:getQualifiedJSXName(openingElement.name)});}}}if(isFragment(openingElement)){node.openingFragment=openingElement;node.closingFragment=closingElement;}else{node.openingElement=openingElement;node.closingElement=closingElement;}node.children=children;if(this.match(47)){throw this.raise(JsxErrors.UnwrappedAdjacentJSXElements,this.state.startLoc);}return isFragment(openingElement)?this.finishNode(node,\"JSXFragment\"):this.finishNode(node,\"JSXElement\");}},{key:\"jsxParseElement\",value:function jsxParseElement(){var startLoc=this.state.startLoc;this.next();return this.jsxParseElementAt(startLoc);}},{key:\"setContext\",value:function setContext(newContext){var context=this.state.context;context[context.length-1]=newContext;}},{key:\"parseExprAtom\",value:function parseExprAtom(refExpressionErrors){if(this.match(143)){return this.jsxParseElement();}else if(this.match(47)&&this.input.charCodeAt(this.state.pos)!==33){this.replaceToken(143);return this.jsxParseElement();}else{return _superPropGet(JSXParserMixin,\"parseExprAtom\",this,3)([refExpressionErrors]);}}},{key:\"skipSpace\",value:function skipSpace(){var curContext=this.curContext();if(!curContext.preserveSpace)_superPropGet(JSXParserMixin,\"skipSpace\",this,3)([]);}},{key:\"getTokenFromCode\",value:function getTokenFromCode(code){var context=this.curContext();if(context===types.j_expr){this.jsxReadToken();return;}if(context===types.j_oTag||context===types.j_cTag){if(isIdentifierStart(code)){this.jsxReadWord();return;}if(code===62){++this.state.pos;this.finishToken(144);return;}if((code===34||code===39)&&context===types.j_oTag){this.jsxReadString(code);return;}}if(code===60&&this.state.canStartJSXElement&&this.input.charCodeAt(this.state.pos+1)!==33){++this.state.pos;this.finishToken(143);return;}_superPropGet(JSXParserMixin,\"getTokenFromCode\",this,3)([code]);}},{key:\"updateContext\",value:function updateContext(prevType){var _this$state=this.state,context=_this$state.context,type=_this$state.type;if(type===56&&prevType===143){context.splice(-2,2,types.j_cTag);this.state.canStartJSXElement=false;}else if(type===143){context.push(types.j_oTag);}else if(type===144){var out=context[context.length-1];if(out===types.j_oTag&&prevType===56||out===types.j_cTag){context.pop();this.state.canStartJSXElement=context[context.length-1]===types.j_expr;}else{this.setContext(types.j_expr);this.state.canStartJSXElement=true;}}else{this.state.canStartJSXElement=tokenComesBeforeExpression(type);}}}]);}(superClass);};var TypeScriptScope=/*#__PURE__*/function(_Scope2){function TypeScriptScope(){var _this15;_classCallCheck(this,TypeScriptScope);for(var _len5=arguments.length,args=new Array(_len5),_key5=0;_key5<_len5;_key5++){args[_key5]=arguments[_key5];}_this15=_callSuper(this,TypeScriptScope,[].concat(args));_this15.tsNames=new Map();return _this15;}_inherits(TypeScriptScope,_Scope2);return _createClass(TypeScriptScope);}(Scope);var TypeScriptScopeHandler=/*#__PURE__*/function(_ScopeHandler2){function TypeScriptScopeHandler(){var _this16;_classCallCheck(this,TypeScriptScopeHandler);for(var _len6=arguments.length,args=new Array(_len6),_key6=0;_key6<_len6;_key6++){args[_key6]=arguments[_key6];}_this16=_callSuper(this,TypeScriptScopeHandler,[].concat(args));_this16.importsStack=[];return _this16;}_inherits(TypeScriptScopeHandler,_ScopeHandler2);return _createClass(TypeScriptScopeHandler,[{key:\"createScope\",value:function createScope(flags){this.importsStack.push(new Set());return new TypeScriptScope(flags);}},{key:\"enter\",value:function enter(flags){if(flags===1024){this.importsStack.push(new Set());}_superPropGet(TypeScriptScopeHandler,\"enter\",this,3)([flags]);}},{key:\"exit\",value:function exit(){var flags=_superPropGet(TypeScriptScopeHandler,\"exit\",this,3)([]);if(flags===1024){this.importsStack.pop();}return flags;}},{key:\"hasImport\",value:function hasImport(name,allowShadow){var len=this.importsStack.length;if(this.importsStack[len-1].has(name)){return true;}if(!allowShadow&&len>1){for(var i=0;i<len-1;i++){if(this.importsStack[i].has(name))return true;}}return false;}},{key:\"declareName\",value:function declareName(name,bindingType,loc){if(bindingType&4096){if(this.hasImport(name,true)){this.parser.raise(Errors.VarRedeclaration,loc,{identifierName:name});}this.importsStack[this.importsStack.length-1].add(name);return;}var scope=this.currentScope();var type=scope.tsNames.get(name)||0;if(bindingType&1024){this.maybeExportDefined(scope,name);scope.tsNames.set(name,type|16);return;}_superPropGet(TypeScriptScopeHandler,\"declareName\",this,3)([name,bindingType,loc]);if(bindingType&2){if(!(bindingType&1)){this.checkRedeclarationInScope(scope,name,bindingType,loc);this.maybeExportDefined(scope,name);}type=type|1;}if(bindingType&256){type=type|2;}if(bindingType&512){type=type|4;}if(bindingType&128){type=type|8;}if(type)scope.tsNames.set(name,type);}},{key:\"isRedeclaredInScope\",value:function isRedeclaredInScope(scope,name,bindingType){var type=scope.tsNames.get(name);if((type&2)>0){if(bindingType&256){var isConst=!!(bindingType&512);var wasConst=(type&4)>0;return isConst!==wasConst;}return true;}if(bindingType&128&&(type&8)>0){if(scope.names.get(name)&2){return!!(bindingType&1);}else{return false;}}if(bindingType&2&&(type&1)>0){return true;}return _superPropGet(TypeScriptScopeHandler,\"isRedeclaredInScope\",this,3)([scope,name,bindingType]);}},{key:\"checkLocalExport\",value:function checkLocalExport(id){var name=id.name;if(this.hasImport(name))return;var len=this.scopeStack.length;for(var i=len-1;i>=0;i--){var scope=this.scopeStack[i];var type=scope.tsNames.get(name);if((type&1)>0||(type&16)>0){return;}}_superPropGet(TypeScriptScopeHandler,\"checkLocalExport\",this,3)([id]);}}]);}(ScopeHandler);var ProductionParameterHandler=/*#__PURE__*/function(){function ProductionParameterHandler(){_classCallCheck(this,ProductionParameterHandler);this.stacks=[];}return _createClass(ProductionParameterHandler,[{key:\"enter\",value:function enter(flags){this.stacks.push(flags);}},{key:\"exit\",value:function exit(){this.stacks.pop();}},{key:\"currentFlags\",value:function currentFlags(){return this.stacks[this.stacks.length-1];}},{key:\"hasAwait\",get:function get(){return(this.currentFlags()&2)>0;}},{key:\"hasYield\",get:function get(){return(this.currentFlags()&1)>0;}},{key:\"hasReturn\",get:function get(){return(this.currentFlags()&4)>0;}},{key:\"hasIn\",get:function get(){return(this.currentFlags()&8)>0;}}]);}();function functionFlags(isAsync,isGenerator){return(isAsync?2:0)|(isGenerator?1:0);}var BaseParser=/*#__PURE__*/function(){function BaseParser(){_classCallCheck(this,BaseParser);this.sawUnambiguousESM=false;this.ambiguousScriptDifferentAst=false;}return _createClass(BaseParser,[{key:\"sourceToOffsetPos\",value:function sourceToOffsetPos(sourcePos){return sourcePos+this.startIndex;}},{key:\"offsetToSourcePos\",value:function offsetToSourcePos(offsetPos){return offsetPos-this.startIndex;}},{key:\"hasPlugin\",value:function hasPlugin(pluginConfig){if(typeof pluginConfig===\"string\"){return this.plugins.has(pluginConfig);}else{var _pluginConfig=_slicedToArray(pluginConfig,2),pluginName=_pluginConfig[0],pluginOptions=_pluginConfig[1];if(!this.hasPlugin(pluginName)){return false;}var actualOptions=this.plugins.get(pluginName);for(var _i3=0,_Object$keys3=Object.keys(pluginOptions);_i3<_Object$keys3.length;_i3++){var key=_Object$keys3[_i3];if((actualOptions==null?void 0:actualOptions[key])!==pluginOptions[key]){return false;}}return true;}}},{key:\"getPluginOption\",value:function getPluginOption(plugin,name){var _this$plugins$get;return(_this$plugins$get=this.plugins.get(plugin))==null?void 0:_this$plugins$get[name];}}]);}();function setTrailingComments(node,comments){if(node.trailingComments===undefined){node.trailingComments=comments;}else{var _node$trailingComment;(_node$trailingComment=node.trailingComments).unshift.apply(_node$trailingComment,_toConsumableArray(comments));}}function setLeadingComments(node,comments){if(node.leadingComments===undefined){node.leadingComments=comments;}else{var _node$leadingComments;(_node$leadingComments=node.leadingComments).unshift.apply(_node$leadingComments,_toConsumableArray(comments));}}function setInnerComments(node,comments){if(node.innerComments===undefined){node.innerComments=comments;}else{var _node$innerComments;(_node$innerComments=node.innerComments).unshift.apply(_node$innerComments,_toConsumableArray(comments));}}function adjustInnerComments(node,elements,commentWS){var lastElement=null;var i=elements.length;while(lastElement===null&&i>0){lastElement=elements[--i];}if(lastElement===null||lastElement.start>commentWS.start){setInnerComments(node,commentWS.comments);}else{setTrailingComments(lastElement,commentWS.comments);}}var CommentsParser=/*#__PURE__*/function(_BaseParser){function CommentsParser(){_classCallCheck(this,CommentsParser);return _callSuper(this,CommentsParser,arguments);}_inherits(CommentsParser,_BaseParser);return _createClass(CommentsParser,[{key:\"addComment\",value:function addComment(comment){if(this.filename)comment.loc.filename=this.filename;var commentsLen=this.state.commentsLen;if(this.comments.length!==commentsLen){this.comments.length=commentsLen;}this.comments.push(comment);this.state.commentsLen++;}},{key:\"processComment\",value:function processComment(node){var commentStack=this.state.commentStack;var commentStackLength=commentStack.length;if(commentStackLength===0)return;var i=commentStackLength-1;var lastCommentWS=commentStack[i];if(lastCommentWS.start===node.end){lastCommentWS.leadingNode=node;i--;}var nodeStart=node.start;for(;i>=0;i--){var commentWS=commentStack[i];var commentEnd=commentWS.end;if(commentEnd>nodeStart){commentWS.containingNode=node;this.finalizeComment(commentWS);commentStack.splice(i,1);}else{if(commentEnd===nodeStart){commentWS.trailingNode=node;}break;}}}},{key:\"finalizeComment\",value:function finalizeComment(commentWS){var _node$options;var comments=commentWS.comments;if(commentWS.leadingNode!==null||commentWS.trailingNode!==null){if(commentWS.leadingNode!==null){setTrailingComments(commentWS.leadingNode,comments);}if(commentWS.trailingNode!==null){setLeadingComments(commentWS.trailingNode,comments);}}else{var node=commentWS.containingNode,commentStart=commentWS.start;if(this.input.charCodeAt(this.offsetToSourcePos(commentStart)-1)===44){switch(node.type){case\"ObjectExpression\":case\"ObjectPattern\":case\"RecordExpression\":adjustInnerComments(node,node.properties,commentWS);break;case\"CallExpression\":case\"OptionalCallExpression\":adjustInnerComments(node,node.arguments,commentWS);break;case\"ImportExpression\":adjustInnerComments(node,[node.source,(_node$options=node.options)!=null?_node$options:null],commentWS);break;case\"FunctionDeclaration\":case\"FunctionExpression\":case\"ArrowFunctionExpression\":case\"ObjectMethod\":case\"ClassMethod\":case\"ClassPrivateMethod\":adjustInnerComments(node,node.params,commentWS);break;case\"ArrayExpression\":case\"ArrayPattern\":case\"TupleExpression\":adjustInnerComments(node,node.elements,commentWS);break;case\"ExportNamedDeclaration\":case\"ImportDeclaration\":adjustInnerComments(node,node.specifiers,commentWS);break;case\"TSEnumDeclaration\":{adjustInnerComments(node,node.members,commentWS);}break;case\"TSEnumBody\":adjustInnerComments(node,node.members,commentWS);break;default:{setInnerComments(node,comments);}}}else{setInnerComments(node,comments);}}}},{key:\"finalizeRemainingComments\",value:function finalizeRemainingComments(){var commentStack=this.state.commentStack;for(var i=commentStack.length-1;i>=0;i--){this.finalizeComment(commentStack[i]);}this.state.commentStack=[];}},{key:\"resetPreviousNodeTrailingComments\",value:function resetPreviousNodeTrailingComments(node){var commentStack=this.state.commentStack;var length=commentStack.length;if(length===0)return;var commentWS=commentStack[length-1];if(commentWS.leadingNode===node){commentWS.leadingNode=null;}}},{key:\"takeSurroundingComments\",value:function takeSurroundingComments(node,start,end){var commentStack=this.state.commentStack;var commentStackLength=commentStack.length;if(commentStackLength===0)return;var i=commentStackLength-1;for(;i>=0;i--){var commentWS=commentStack[i];var commentEnd=commentWS.end;var commentStart=commentWS.start;if(commentStart===end){commentWS.leadingNode=node;}else if(commentEnd===start){commentWS.trailingNode=node;}else if(commentEnd<start){break;}}}}]);}(BaseParser);var State=/*#__PURE__*/function(){function State(){_classCallCheck(this,State);this.flags=1024;this.startIndex=void 0;this.curLine=void 0;this.lineStart=void 0;this.startLoc=void 0;this.endLoc=void 0;this.errors=[];this.potentialArrowAt=-1;this.noArrowAt=[];this.noArrowParamsConversionAt=[];this.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null};this.labels=[];this.commentsLen=0;this.commentStack=[];this.pos=0;this.type=140;this.value=null;this.start=0;this.end=0;this.lastTokEndLoc=null;this.lastTokStartLoc=null;this.context=[types.brace];this.firstInvalidTemplateEscapePos=null;this.strictErrors=new Map();this.tokensLength=0;}return _createClass(State,[{key:\"strict\",get:function get(){return(this.flags&1)>0;},set:function set(v){if(v)this.flags|=1;else this.flags&=-2;}},{key:\"init\",value:function init(_ref58){var strictMode=_ref58.strictMode,sourceType=_ref58.sourceType,startIndex=_ref58.startIndex,startLine=_ref58.startLine,startColumn=_ref58.startColumn;this.strict=strictMode===false?false:strictMode===true?true:sourceType===\"module\";this.startIndex=startIndex;this.curLine=startLine;this.lineStart=-startColumn;this.startLoc=this.endLoc=new Position(startLine,startColumn,startIndex);}},{key:\"maybeInArrowParameters\",get:function get(){return(this.flags&2)>0;},set:function set(v){if(v)this.flags|=2;else this.flags&=-3;}},{key:\"inType\",get:function get(){return(this.flags&4)>0;},set:function set(v){if(v)this.flags|=4;else this.flags&=-5;}},{key:\"noAnonFunctionType\",get:function get(){return(this.flags&8)>0;},set:function set(v){if(v)this.flags|=8;else this.flags&=-9;}},{key:\"hasFlowComment\",get:function get(){return(this.flags&16)>0;},set:function set(v){if(v)this.flags|=16;else this.flags&=-17;}},{key:\"isAmbientContext\",get:function get(){return(this.flags&32)>0;},set:function set(v){if(v)this.flags|=32;else this.flags&=-33;}},{key:\"inAbstractClass\",get:function get(){return(this.flags&64)>0;},set:function set(v){if(v)this.flags|=64;else this.flags&=-65;}},{key:\"inDisallowConditionalTypesContext\",get:function get(){return(this.flags&128)>0;},set:function set(v){if(v)this.flags|=128;else this.flags&=-129;}},{key:\"soloAwait\",get:function get(){return(this.flags&256)>0;},set:function set(v){if(v)this.flags|=256;else this.flags&=-257;}},{key:\"inFSharpPipelineDirectBody\",get:function get(){return(this.flags&512)>0;},set:function set(v){if(v)this.flags|=512;else this.flags&=-513;}},{key:\"canStartJSXElement\",get:function get(){return(this.flags&1024)>0;},set:function set(v){if(v)this.flags|=1024;else this.flags&=-1025;}},{key:\"containsEsc\",get:function get(){return(this.flags&2048)>0;},set:function set(v){if(v)this.flags|=2048;else this.flags&=-2049;}},{key:\"hasTopLevelAwait\",get:function get(){return(this.flags&4096)>0;},set:function set(v){if(v)this.flags|=4096;else this.flags&=-4097;}},{key:\"curPosition\",value:function curPosition(){return new Position(this.curLine,this.pos-this.lineStart,this.pos+this.startIndex);}},{key:\"clone\",value:function clone(){var state=new State();state.flags=this.flags;state.startIndex=this.startIndex;state.curLine=this.curLine;state.lineStart=this.lineStart;state.startLoc=this.startLoc;state.endLoc=this.endLoc;state.errors=this.errors.slice();state.potentialArrowAt=this.potentialArrowAt;state.noArrowAt=this.noArrowAt.slice();state.noArrowParamsConversionAt=this.noArrowParamsConversionAt.slice();state.topicContext=this.topicContext;state.labels=this.labels.slice();state.commentsLen=this.commentsLen;state.commentStack=this.commentStack.slice();state.pos=this.pos;state.type=this.type;state.value=this.value;state.start=this.start;state.end=this.end;state.lastTokEndLoc=this.lastTokEndLoc;state.lastTokStartLoc=this.lastTokStartLoc;state.context=this.context.slice();state.firstInvalidTemplateEscapePos=this.firstInvalidTemplateEscapePos;state.strictErrors=this.strictErrors;state.tokensLength=this.tokensLength;return state;}}]);}();var _isDigit=function isDigit(code){return code>=48&&code<=57;};var forbiddenNumericSeparatorSiblings={decBinOct:new Set([46,66,69,79,95,98,101,111]),hex:new Set([46,88,95,120])};var isAllowedNumericSeparatorSibling={bin:function bin(ch){return ch===48||ch===49;},oct:function oct(ch){return ch>=48&&ch<=55;},dec:function dec(ch){return ch>=48&&ch<=57;},hex:function hex(ch){return ch>=48&&ch<=57||ch>=65&&ch<=70||ch>=97&&ch<=102;}};function readStringContents(type,input,pos,lineStart,curLine,errors){var initialPos=pos;var initialLineStart=lineStart;var initialCurLine=curLine;var out=\"\";var firstInvalidLoc=null;var chunkStart=pos;var length=input.length;for(;;){if(pos>=length){errors.unterminated(initialPos,initialLineStart,initialCurLine);out+=input.slice(chunkStart,pos);break;}var ch=input.charCodeAt(pos);if(isStringEnd(type,ch,input,pos)){out+=input.slice(chunkStart,pos);break;}if(ch===92){out+=input.slice(chunkStart,pos);var res=readEscapedChar(input,pos,lineStart,curLine,type===\"template\",errors);if(res.ch===null&&!firstInvalidLoc){firstInvalidLoc={pos:pos,lineStart:lineStart,curLine:curLine};}else{out+=res.ch;}pos=res.pos;lineStart=res.lineStart;curLine=res.curLine;chunkStart=pos;}else if(ch===8232||ch===8233){++pos;++curLine;lineStart=pos;}else if(ch===10||ch===13){if(type===\"template\"){out+=input.slice(chunkStart,pos)+\"\\n\";++pos;if(ch===13&&input.charCodeAt(pos)===10){++pos;}++curLine;chunkStart=lineStart=pos;}else{errors.unterminated(initialPos,initialLineStart,initialCurLine);}}else{++pos;}}return{pos:pos,str:out,firstInvalidLoc:firstInvalidLoc,lineStart:lineStart,curLine:curLine,containsInvalid:!!firstInvalidLoc};}function isStringEnd(type,ch,input,pos){if(type===\"template\"){return ch===96||ch===36&&input.charCodeAt(pos+1)===123;}return ch===(type===\"double\"?34:39);}function readEscapedChar(input,pos,lineStart,curLine,inTemplate,errors){var throwOnInvalid=!inTemplate;pos++;var res=function res(ch){return{pos:pos,ch:ch,lineStart:lineStart,curLine:curLine};};var ch=input.charCodeAt(pos++);switch(ch){case 110:return res(\"\\n\");case 114:return res(\"\\r\");case 120:{var _code;var _readHexChar=readHexChar(input,pos,lineStart,curLine,2,false,throwOnInvalid,errors);_code=_readHexChar.code;pos=_readHexChar.pos;return res(_code===null?null:String.fromCharCode(_code));}case 117:{var _code2;var _readCodePoint=_readCodePoint2(input,pos,lineStart,curLine,throwOnInvalid,errors);_code2=_readCodePoint.code;pos=_readCodePoint.pos;return res(_code2===null?null:String.fromCodePoint(_code2));}case 116:return res(\"\\t\");case 98:return res(\"\\b\");case 118:return res(\"\\x0B\");case 102:return res(\"\\f\");case 13:if(input.charCodeAt(pos)===10){++pos;}case 10:lineStart=pos;++curLine;case 8232:case 8233:return res(\"\");case 56:case 57:if(inTemplate){return res(null);}else{errors.strictNumericEscape(pos-1,lineStart,curLine);}default:if(ch>=48&&ch<=55){var startPos=pos-1;var match=/^[0-7]+/.exec(input.slice(startPos,pos+2));var octalStr=match[0];var octal=parseInt(octalStr,8);if(octal>255){octalStr=octalStr.slice(0,-1);octal=parseInt(octalStr,8);}pos+=octalStr.length-1;var next=input.charCodeAt(pos);if(octalStr!==\"0\"||next===56||next===57){if(inTemplate){return res(null);}else{errors.strictNumericEscape(startPos,lineStart,curLine);}}return res(String.fromCharCode(octal));}return res(String.fromCharCode(ch));}}function readHexChar(input,pos,lineStart,curLine,len,forceLen,throwOnInvalid,errors){var initialPos=pos;var n;var _readInt=_readInt2(input,pos,lineStart,curLine,16,len,forceLen,false,errors,!throwOnInvalid);n=_readInt.n;pos=_readInt.pos;if(n===null){if(throwOnInvalid){errors.invalidEscapeSequence(initialPos,lineStart,curLine);}else{pos=initialPos-1;}}return{code:n,pos:pos};}function _readInt2(input,pos,lineStart,curLine,radix,len,forceLen,allowNumSeparator,errors,bailOnError){var start=pos;var forbiddenSiblings=radix===16?forbiddenNumericSeparatorSiblings.hex:forbiddenNumericSeparatorSiblings.decBinOct;var isAllowedSibling=radix===16?isAllowedNumericSeparatorSibling.hex:radix===10?isAllowedNumericSeparatorSibling.dec:radix===8?isAllowedNumericSeparatorSibling.oct:isAllowedNumericSeparatorSibling.bin;var invalid=false;var total=0;for(var i=0,e=len==null?Infinity:len;i<e;++i){var _code3=input.charCodeAt(pos);var val=void 0;if(_code3===95&&allowNumSeparator!==\"bail\"){var prev=input.charCodeAt(pos-1);var next=input.charCodeAt(pos+1);if(!allowNumSeparator){if(bailOnError)return{n:null,pos:pos};errors.numericSeparatorInEscapeSequence(pos,lineStart,curLine);}else if(Number.isNaN(next)||!isAllowedSibling(next)||forbiddenSiblings.has(prev)||forbiddenSiblings.has(next)){if(bailOnError)return{n:null,pos:pos};errors.unexpectedNumericSeparator(pos,lineStart,curLine);}++pos;continue;}if(_code3>=97){val=_code3-97+10;}else if(_code3>=65){val=_code3-65+10;}else if(_isDigit(_code3)){val=_code3-48;}else{val=Infinity;}if(val>=radix){if(val<=9&&bailOnError){return{n:null,pos:pos};}else if(val<=9&&errors.invalidDigit(pos,lineStart,curLine,radix)){val=0;}else if(forceLen){val=0;invalid=true;}else{break;}}++pos;total=total*radix+val;}if(pos===start||len!=null&&pos-start!==len||invalid){return{n:null,pos:pos};}return{n:total,pos:pos};}function _readCodePoint2(input,pos,lineStart,curLine,throwOnInvalid,errors){var ch=input.charCodeAt(pos);var code;if(ch===123){++pos;var _readHexChar2=readHexChar(input,pos,lineStart,curLine,input.indexOf(\"}\",pos)-pos,true,throwOnInvalid,errors);code=_readHexChar2.code;pos=_readHexChar2.pos;++pos;if(code!==null&&code>0x10ffff){if(throwOnInvalid){errors.invalidCodePoint(pos,lineStart,curLine);}else{return{code:null,pos:pos};}}}else{var _readHexChar3=readHexChar(input,pos,lineStart,curLine,4,false,throwOnInvalid,errors);code=_readHexChar3.code;pos=_readHexChar3.pos;}return{code:code,pos:pos};}function buildPosition(pos,lineStart,curLine){return new Position(curLine,pos-lineStart,pos);}var VALID_REGEX_FLAGS=new Set([103,109,115,105,121,117,100,118]);var Token=/*#__PURE__*/_createClass(function Token(state){_classCallCheck(this,Token);var startIndex=state.startIndex||0;this.type=state.type;this.value=state.value;this.start=startIndex+state.start;this.end=startIndex+state.end;this.loc=new SourceLocation(state.startLoc,state.endLoc);});var Tokenizer=/*#__PURE__*/function(_CommentsParser){function Tokenizer(options,input){var _this17;_classCallCheck(this,Tokenizer);_this17=_callSuper(this,Tokenizer);_this17.isLookahead=void 0;_this17.tokens=[];_this17.errorHandlers_readInt={invalidDigit:function invalidDigit(pos,lineStart,curLine,radix){if(!(_this17.optionFlags&2048))return false;_this17.raise(Errors.InvalidDigit,buildPosition(pos,lineStart,curLine),{radix:radix});return true;},numericSeparatorInEscapeSequence:_this17.errorBuilder(Errors.NumericSeparatorInEscapeSequence),unexpectedNumericSeparator:_this17.errorBuilder(Errors.UnexpectedNumericSeparator)};_this17.errorHandlers_readCodePoint=Object.assign({},_this17.errorHandlers_readInt,{invalidEscapeSequence:_this17.errorBuilder(Errors.InvalidEscapeSequence),invalidCodePoint:_this17.errorBuilder(Errors.InvalidCodePoint)});_this17.errorHandlers_readStringContents_string=Object.assign({},_this17.errorHandlers_readCodePoint,{strictNumericEscape:function strictNumericEscape(pos,lineStart,curLine){_this17.recordStrictModeErrors(Errors.StrictNumericEscape,buildPosition(pos,lineStart,curLine));},unterminated:function unterminated(pos,lineStart,curLine){throw _this17.raise(Errors.UnterminatedString,buildPosition(pos-1,lineStart,curLine));}});_this17.errorHandlers_readStringContents_template=Object.assign({},_this17.errorHandlers_readCodePoint,{strictNumericEscape:_this17.errorBuilder(Errors.StrictNumericEscape),unterminated:function unterminated(pos,lineStart,curLine){throw _this17.raise(Errors.UnterminatedTemplate,buildPosition(pos,lineStart,curLine));}});_this17.state=new State();_this17.state.init(options);_this17.input=input;_this17.length=input.length;_this17.comments=[];_this17.isLookahead=false;return _this17;}_inherits(Tokenizer,_CommentsParser);return _createClass(Tokenizer,[{key:\"pushToken\",value:function pushToken(token){this.tokens.length=this.state.tokensLength;this.tokens.push(token);++this.state.tokensLength;}},{key:\"next\",value:function next(){this.checkKeywordEscapes();if(this.optionFlags&256){this.pushToken(new Token(this.state));}this.state.lastTokEndLoc=this.state.endLoc;this.state.lastTokStartLoc=this.state.startLoc;this.nextToken();}},{key:\"eat\",value:function eat(type){if(this.match(type)){this.next();return true;}else{return false;}}},{key:\"match\",value:function match(type){return this.state.type===type;}},{key:\"createLookaheadState\",value:function createLookaheadState(state){return{pos:state.pos,value:null,type:state.type,start:state.start,end:state.end,context:[this.curContext()],inType:state.inType,startLoc:state.startLoc,lastTokEndLoc:state.lastTokEndLoc,curLine:state.curLine,lineStart:state.lineStart,curPosition:state.curPosition};}},{key:\"lookahead\",value:function lookahead(){var old=this.state;this.state=this.createLookaheadState(old);this.isLookahead=true;this.nextToken();this.isLookahead=false;var curr=this.state;this.state=old;return curr;}},{key:\"nextTokenStart\",value:function nextTokenStart(){return this.nextTokenStartSince(this.state.pos);}},{key:\"nextTokenStartSince\",value:function nextTokenStartSince(pos){skipWhiteSpace.lastIndex=pos;return skipWhiteSpace.test(this.input)?skipWhiteSpace.lastIndex:pos;}},{key:\"lookaheadCharCode\",value:function lookaheadCharCode(){return this.lookaheadCharCodeSince(this.state.pos);}},{key:\"lookaheadCharCodeSince\",value:function lookaheadCharCodeSince(pos){return this.input.charCodeAt(this.nextTokenStartSince(pos));}},{key:\"nextTokenInLineStart\",value:function nextTokenInLineStart(){return this.nextTokenInLineStartSince(this.state.pos);}},{key:\"nextTokenInLineStartSince\",value:function nextTokenInLineStartSince(pos){skipWhiteSpaceInLine.lastIndex=pos;return skipWhiteSpaceInLine.test(this.input)?skipWhiteSpaceInLine.lastIndex:pos;}},{key:\"lookaheadInLineCharCode\",value:function lookaheadInLineCharCode(){return this.input.charCodeAt(this.nextTokenInLineStart());}},{key:\"codePointAtPos\",value:function codePointAtPos(pos){var cp=this.input.charCodeAt(pos);if((cp&0xfc00)===0xd800&&++pos<this.input.length){var trail=this.input.charCodeAt(pos);if((trail&0xfc00)===0xdc00){cp=0x10000+((cp&0x3ff)<<10)+(trail&0x3ff);}}return cp;}},{key:\"setStrict\",value:function setStrict(strict){var _this18=this;this.state.strict=strict;if(strict){this.state.strictErrors.forEach(function(_ref59){var _ref60=_slicedToArray(_ref59,2),toParseError=_ref60[0],at=_ref60[1];return _this18.raise(toParseError,at);});this.state.strictErrors.clear();}}},{key:\"curContext\",value:function curContext(){return this.state.context[this.state.context.length-1];}},{key:\"nextToken\",value:function nextToken(){this.skipSpace();this.state.start=this.state.pos;if(!this.isLookahead)this.state.startLoc=this.state.curPosition();if(this.state.pos>=this.length){this.finishToken(140);return;}this.getTokenFromCode(this.codePointAtPos(this.state.pos));}},{key:\"skipBlockComment\",value:function skipBlockComment(commentEnd){var startLoc;if(!this.isLookahead)startLoc=this.state.curPosition();var start=this.state.pos;var end=this.input.indexOf(commentEnd,start+2);if(end===-1){throw this.raise(Errors.UnterminatedComment,this.state.curPosition());}this.state.pos=end+commentEnd.length;lineBreakG.lastIndex=start+2;while(lineBreakG.test(this.input)&&lineBreakG.lastIndex<=end){++this.state.curLine;this.state.lineStart=lineBreakG.lastIndex;}if(this.isLookahead)return;var comment={type:\"CommentBlock\",value:this.input.slice(start+2,end),start:this.sourceToOffsetPos(start),end:this.sourceToOffsetPos(end+commentEnd.length),loc:new SourceLocation(startLoc,this.state.curPosition())};if(this.optionFlags&256)this.pushToken(comment);return comment;}},{key:\"skipLineComment\",value:function skipLineComment(startSkip){var start=this.state.pos;var startLoc;if(!this.isLookahead)startLoc=this.state.curPosition();var ch=this.input.charCodeAt(this.state.pos+=startSkip);if(this.state.pos<this.length){while(!isNewLine(ch)&&++this.state.pos<this.length){ch=this.input.charCodeAt(this.state.pos);}}if(this.isLookahead)return;var end=this.state.pos;var value=this.input.slice(start+startSkip,end);var comment={type:\"CommentLine\",value:value,start:this.sourceToOffsetPos(start),end:this.sourceToOffsetPos(end),loc:new SourceLocation(startLoc,this.state.curPosition())};if(this.optionFlags&256)this.pushToken(comment);return comment;}},{key:\"skipSpace\",value:function skipSpace(){var spaceStart=this.state.pos;var comments=this.optionFlags&4096?[]:null;loop:while(this.state.pos<this.length){var ch=this.input.charCodeAt(this.state.pos);switch(ch){case 32:case 160:case 9:++this.state.pos;break;case 13:if(this.input.charCodeAt(this.state.pos+1)===10){++this.state.pos;}case 10:case 8232:case 8233:++this.state.pos;++this.state.curLine;this.state.lineStart=this.state.pos;break;case 47:switch(this.input.charCodeAt(this.state.pos+1)){case 42:{var comment=this.skipBlockComment(\"*/\");if(comment!==undefined){this.addComment(comment);comments==null||comments.push(comment);}break;}case 47:{var _comment=this.skipLineComment(2);if(_comment!==undefined){this.addComment(_comment);comments==null||comments.push(_comment);}break;}default:break loop;}break;default:if(isWhitespace(ch)){++this.state.pos;}else if(ch===45&&!this.inModule&&this.optionFlags&8192){var pos=this.state.pos;if(this.input.charCodeAt(pos+1)===45&&this.input.charCodeAt(pos+2)===62&&(spaceStart===0||this.state.lineStart>spaceStart)){var _comment2=this.skipLineComment(3);if(_comment2!==undefined){this.addComment(_comment2);comments==null||comments.push(_comment2);}}else{break loop;}}else if(ch===60&&!this.inModule&&this.optionFlags&8192){var _pos=this.state.pos;if(this.input.charCodeAt(_pos+1)===33&&this.input.charCodeAt(_pos+2)===45&&this.input.charCodeAt(_pos+3)===45){var _comment3=this.skipLineComment(4);if(_comment3!==undefined){this.addComment(_comment3);comments==null||comments.push(_comment3);}}else{break loop;}}else{break loop;}}}if((comments==null?void 0:comments.length)>0){var end=this.state.pos;var commentWhitespace={start:this.sourceToOffsetPos(spaceStart),end:this.sourceToOffsetPos(end),comments:comments,leadingNode:null,trailingNode:null,containingNode:null};this.state.commentStack.push(commentWhitespace);}}},{key:\"finishToken\",value:function finishToken(type,val){this.state.end=this.state.pos;this.state.endLoc=this.state.curPosition();var prevType=this.state.type;this.state.type=type;this.state.value=val;if(!this.isLookahead){this.updateContext(prevType);}}},{key:\"replaceToken\",value:function replaceToken(type){this.state.type=type;this.updateContext();}},{key:\"readToken_numberSign\",value:function readToken_numberSign(){if(this.state.pos===0&&this.readToken_interpreter()){return;}var nextPos=this.state.pos+1;var next=this.codePointAtPos(nextPos);if(next>=48&&next<=57){throw this.raise(Errors.UnexpectedDigitAfterHash,this.state.curPosition());}if(next===123||next===91&&this.hasPlugin(\"recordAndTuple\")){this.expectPlugin(\"recordAndTuple\");if(this.getPluginOption(\"recordAndTuple\",\"syntaxType\")===\"bar\"){throw this.raise(next===123?Errors.RecordExpressionHashIncorrectStartSyntaxType:Errors.TupleExpressionHashIncorrectStartSyntaxType,this.state.curPosition());}this.state.pos+=2;if(next===123){this.finishToken(7);}else{this.finishToken(1);}}else if(isIdentifierStart(next)){++this.state.pos;this.finishToken(139,this.readWord1(next));}else if(next===92){++this.state.pos;this.finishToken(139,this.readWord1());}else{this.finishOp(27,1);}}},{key:\"readToken_dot\",value:function readToken_dot(){var next=this.input.charCodeAt(this.state.pos+1);if(next>=48&&next<=57){this.readNumber(true);return;}if(next===46&&this.input.charCodeAt(this.state.pos+2)===46){this.state.pos+=3;this.finishToken(21);}else{++this.state.pos;this.finishToken(16);}}},{key:\"readToken_slash\",value:function readToken_slash(){var next=this.input.charCodeAt(this.state.pos+1);if(next===61){this.finishOp(31,2);}else{this.finishOp(56,1);}}},{key:\"readToken_interpreter\",value:function readToken_interpreter(){if(this.state.pos!==0||this.length<2)return false;var ch=this.input.charCodeAt(this.state.pos+1);if(ch!==33)return false;var start=this.state.pos;this.state.pos+=1;while(!isNewLine(ch)&&++this.state.pos<this.length){ch=this.input.charCodeAt(this.state.pos);}var value=this.input.slice(start+2,this.state.pos);this.finishToken(28,value);return true;}},{key:\"readToken_mult_modulo\",value:function readToken_mult_modulo(code){var type=code===42?55:54;var width=1;var next=this.input.charCodeAt(this.state.pos+1);if(code===42&&next===42){width++;next=this.input.charCodeAt(this.state.pos+2);type=57;}if(next===61&&!this.state.inType){width++;type=code===37?33:30;}this.finishOp(type,width);}},{key:\"readToken_pipe_amp\",value:function readToken_pipe_amp(code){var next=this.input.charCodeAt(this.state.pos+1);if(next===code){if(this.input.charCodeAt(this.state.pos+2)===61){this.finishOp(30,3);}else{this.finishOp(code===124?41:42,2);}return;}if(code===124){if(next===62){this.finishOp(39,2);return;}if(this.hasPlugin(\"recordAndTuple\")&&next===125){if(this.getPluginOption(\"recordAndTuple\",\"syntaxType\")!==\"bar\"){throw this.raise(Errors.RecordExpressionBarIncorrectEndSyntaxType,this.state.curPosition());}this.state.pos+=2;this.finishToken(9);return;}if(this.hasPlugin(\"recordAndTuple\")&&next===93){if(this.getPluginOption(\"recordAndTuple\",\"syntaxType\")!==\"bar\"){throw this.raise(Errors.TupleExpressionBarIncorrectEndSyntaxType,this.state.curPosition());}this.state.pos+=2;this.finishToken(4);return;}}if(next===61){this.finishOp(30,2);return;}this.finishOp(code===124?43:45,1);}},{key:\"readToken_caret\",value:function readToken_caret(){var next=this.input.charCodeAt(this.state.pos+1);if(next===61&&!this.state.inType){this.finishOp(32,2);}else if(next===94&&this.hasPlugin([\"pipelineOperator\",{proposal:\"hack\",topicToken:\"^^\"}])){this.finishOp(37,2);var lookaheadCh=this.input.codePointAt(this.state.pos);if(lookaheadCh===94){this.unexpected();}}else{this.finishOp(44,1);}}},{key:\"readToken_atSign\",value:function readToken_atSign(){var next=this.input.charCodeAt(this.state.pos+1);if(next===64&&this.hasPlugin([\"pipelineOperator\",{proposal:\"hack\",topicToken:\"@@\"}])){this.finishOp(38,2);}else{this.finishOp(26,1);}}},{key:\"readToken_plus_min\",value:function readToken_plus_min(code){var next=this.input.charCodeAt(this.state.pos+1);if(next===code){this.finishOp(34,2);return;}if(next===61){this.finishOp(30,2);}else{this.finishOp(53,1);}}},{key:\"readToken_lt\",value:function readToken_lt(){var pos=this.state.pos;var next=this.input.charCodeAt(pos+1);if(next===60){if(this.input.charCodeAt(pos+2)===61){this.finishOp(30,3);return;}this.finishOp(51,2);return;}if(next===61){this.finishOp(49,2);return;}this.finishOp(47,1);}},{key:\"readToken_gt\",value:function readToken_gt(){var pos=this.state.pos;var next=this.input.charCodeAt(pos+1);if(next===62){var size=this.input.charCodeAt(pos+2)===62?3:2;if(this.input.charCodeAt(pos+size)===61){this.finishOp(30,size+1);return;}this.finishOp(52,size);return;}if(next===61){this.finishOp(49,2);return;}this.finishOp(48,1);}},{key:\"readToken_eq_excl\",value:function readToken_eq_excl(code){var next=this.input.charCodeAt(this.state.pos+1);if(next===61){this.finishOp(46,this.input.charCodeAt(this.state.pos+2)===61?3:2);return;}if(code===61&&next===62){this.state.pos+=2;this.finishToken(19);return;}this.finishOp(code===61?29:35,1);}},{key:\"readToken_question\",value:function readToken_question(){var next=this.input.charCodeAt(this.state.pos+1);var next2=this.input.charCodeAt(this.state.pos+2);if(next===63){if(next2===61){this.finishOp(30,3);}else{this.finishOp(40,2);}}else if(next===46&&!(next2>=48&&next2<=57)){this.state.pos+=2;this.finishToken(18);}else{++this.state.pos;this.finishToken(17);}}},{key:\"getTokenFromCode\",value:function getTokenFromCode(code){switch(code){case 46:this.readToken_dot();return;case 40:++this.state.pos;this.finishToken(10);return;case 41:++this.state.pos;this.finishToken(11);return;case 59:++this.state.pos;this.finishToken(13);return;case 44:++this.state.pos;this.finishToken(12);return;case 91:if(this.hasPlugin(\"recordAndTuple\")&&this.input.charCodeAt(this.state.pos+1)===124){if(this.getPluginOption(\"recordAndTuple\",\"syntaxType\")!==\"bar\"){throw this.raise(Errors.TupleExpressionBarIncorrectStartSyntaxType,this.state.curPosition());}this.state.pos+=2;this.finishToken(2);}else{++this.state.pos;this.finishToken(0);}return;case 93:++this.state.pos;this.finishToken(3);return;case 123:if(this.hasPlugin(\"recordAndTuple\")&&this.input.charCodeAt(this.state.pos+1)===124){if(this.getPluginOption(\"recordAndTuple\",\"syntaxType\")!==\"bar\"){throw this.raise(Errors.RecordExpressionBarIncorrectStartSyntaxType,this.state.curPosition());}this.state.pos+=2;this.finishToken(6);}else{++this.state.pos;this.finishToken(5);}return;case 125:++this.state.pos;this.finishToken(8);return;case 58:if(this.hasPlugin(\"functionBind\")&&this.input.charCodeAt(this.state.pos+1)===58){this.finishOp(15,2);}else{++this.state.pos;this.finishToken(14);}return;case 63:this.readToken_question();return;case 96:this.readTemplateToken();return;case 48:{var next=this.input.charCodeAt(this.state.pos+1);if(next===120||next===88){this.readRadixNumber(16);return;}if(next===111||next===79){this.readRadixNumber(8);return;}if(next===98||next===66){this.readRadixNumber(2);return;}}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:this.readNumber(false);return;case 34:case 39:this.readString(code);return;case 47:this.readToken_slash();return;case 37:case 42:this.readToken_mult_modulo(code);return;case 124:case 38:this.readToken_pipe_amp(code);return;case 94:this.readToken_caret();return;case 43:case 45:this.readToken_plus_min(code);return;case 60:this.readToken_lt();return;case 62:this.readToken_gt();return;case 61:case 33:this.readToken_eq_excl(code);return;case 126:this.finishOp(36,1);return;case 64:this.readToken_atSign();return;case 35:this.readToken_numberSign();return;case 92:this.readWord();return;default:if(isIdentifierStart(code)){this.readWord(code);return;}}throw this.raise(Errors.InvalidOrUnexpectedToken,this.state.curPosition(),{unexpected:String.fromCodePoint(code)});}},{key:\"finishOp\",value:function finishOp(type,size){var str=this.input.slice(this.state.pos,this.state.pos+size);this.state.pos+=size;this.finishToken(type,str);}},{key:\"readRegexp\",value:function readRegexp(){var startLoc=this.state.startLoc;var start=this.state.start+1;var escaped,inClass;var pos=this.state.pos;for(;;++pos){if(pos>=this.length){throw this.raise(Errors.UnterminatedRegExp,createPositionWithColumnOffset(startLoc,1));}var ch=this.input.charCodeAt(pos);if(isNewLine(ch)){throw this.raise(Errors.UnterminatedRegExp,createPositionWithColumnOffset(startLoc,1));}if(escaped){escaped=false;}else{if(ch===91){inClass=true;}else if(ch===93&&inClass){inClass=false;}else if(ch===47&&!inClass){break;}escaped=ch===92;}}var content=this.input.slice(start,pos);++pos;var mods=\"\";var nextPos=function nextPos(){return createPositionWithColumnOffset(startLoc,pos+2-start);};while(pos<this.length){var cp=this.codePointAtPos(pos);var char=String.fromCharCode(cp);if(VALID_REGEX_FLAGS.has(cp)){if(cp===118){if(mods.includes(\"u\")){this.raise(Errors.IncompatibleRegExpUVFlags,nextPos());}}else if(cp===117){if(mods.includes(\"v\")){this.raise(Errors.IncompatibleRegExpUVFlags,nextPos());}}if(mods.includes(char)){this.raise(Errors.DuplicateRegExpFlags,nextPos());}}else if(isIdentifierChar(cp)||cp===92){this.raise(Errors.MalformedRegExpFlags,nextPos());}else{break;}++pos;mods+=char;}this.state.pos=pos;this.finishToken(138,{pattern:content,flags:mods});}},{key:\"readInt\",value:function readInt(radix,len){var forceLen=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;var allowNumSeparator=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;var _readInt3=_readInt2(this.input,this.state.pos,this.state.lineStart,this.state.curLine,radix,len,forceLen,allowNumSeparator,this.errorHandlers_readInt,false),n=_readInt3.n,pos=_readInt3.pos;this.state.pos=pos;return n;}},{key:\"readRadixNumber\",value:function readRadixNumber(radix){var start=this.state.pos;var startLoc=this.state.curPosition();var isBigInt=false;this.state.pos+=2;var val=this.readInt(radix);if(val==null){this.raise(Errors.InvalidDigit,createPositionWithColumnOffset(startLoc,2),{radix:radix});}var next=this.input.charCodeAt(this.state.pos);if(next===110){++this.state.pos;isBigInt=true;}else if(next===109){throw this.raise(Errors.InvalidDecimal,startLoc);}if(isIdentifierStart(this.codePointAtPos(this.state.pos))){throw this.raise(Errors.NumberIdentifier,this.state.curPosition());}if(isBigInt){var str=this.input.slice(start,this.state.pos).replace(/[_n]/g,\"\");this.finishToken(136,str);return;}this.finishToken(135,val);}},{key:\"readNumber\",value:function readNumber(startsWithDot){var start=this.state.pos;var startLoc=this.state.curPosition();var isFloat=false;var isBigInt=false;var hasExponent=false;var isOctal=false;if(!startsWithDot&&this.readInt(10)===null){this.raise(Errors.InvalidNumber,this.state.curPosition());}var hasLeadingZero=this.state.pos-start>=2&&this.input.charCodeAt(start)===48;if(hasLeadingZero){var integer=this.input.slice(start,this.state.pos);this.recordStrictModeErrors(Errors.StrictOctalLiteral,startLoc);if(!this.state.strict){var underscorePos=integer.indexOf(\"_\");if(underscorePos>0){this.raise(Errors.ZeroDigitNumericSeparator,createPositionWithColumnOffset(startLoc,underscorePos));}}isOctal=hasLeadingZero&&!/[89]/.test(integer);}var next=this.input.charCodeAt(this.state.pos);if(next===46&&!isOctal){++this.state.pos;this.readInt(10);isFloat=true;next=this.input.charCodeAt(this.state.pos);}if((next===69||next===101)&&!isOctal){next=this.input.charCodeAt(++this.state.pos);if(next===43||next===45){++this.state.pos;}if(this.readInt(10)===null){this.raise(Errors.InvalidOrMissingExponent,startLoc);}isFloat=true;hasExponent=true;next=this.input.charCodeAt(this.state.pos);}if(next===110){if(isFloat||hasLeadingZero){this.raise(Errors.InvalidBigIntLiteral,startLoc);}++this.state.pos;isBigInt=true;}if(next===109){this.expectPlugin(\"decimal\",this.state.curPosition());if(hasExponent||hasLeadingZero){this.raise(Errors.InvalidDecimal,startLoc);}++this.state.pos;var isDecimal=true;}if(isIdentifierStart(this.codePointAtPos(this.state.pos))){throw this.raise(Errors.NumberIdentifier,this.state.curPosition());}var str=this.input.slice(start,this.state.pos).replace(/[_mn]/g,\"\");if(isBigInt){this.finishToken(136,str);return;}if(isDecimal){this.finishToken(137,str);return;}var val=isOctal?parseInt(str,8):parseFloat(str);this.finishToken(135,val);}},{key:\"readCodePoint\",value:function readCodePoint(throwOnInvalid){var _readCodePoint3=_readCodePoint2(this.input,this.state.pos,this.state.lineStart,this.state.curLine,throwOnInvalid,this.errorHandlers_readCodePoint),code=_readCodePoint3.code,pos=_readCodePoint3.pos;this.state.pos=pos;return code;}},{key:\"readString\",value:function readString(quote){var _readStringContents=readStringContents(quote===34?\"double\":\"single\",this.input,this.state.pos+1,this.state.lineStart,this.state.curLine,this.errorHandlers_readStringContents_string),str=_readStringContents.str,pos=_readStringContents.pos,curLine=_readStringContents.curLine,lineStart=_readStringContents.lineStart;this.state.pos=pos+1;this.state.lineStart=lineStart;this.state.curLine=curLine;this.finishToken(134,str);}},{key:\"readTemplateContinuation\",value:function readTemplateContinuation(){if(!this.match(8)){this.unexpected(null,8);}this.state.pos--;this.readTemplateToken();}},{key:\"readTemplateToken\",value:function readTemplateToken(){var opening=this.input[this.state.pos];var _readStringContents2=readStringContents(\"template\",this.input,this.state.pos+1,this.state.lineStart,this.state.curLine,this.errorHandlers_readStringContents_template),str=_readStringContents2.str,firstInvalidLoc=_readStringContents2.firstInvalidLoc,pos=_readStringContents2.pos,curLine=_readStringContents2.curLine,lineStart=_readStringContents2.lineStart;this.state.pos=pos+1;this.state.lineStart=lineStart;this.state.curLine=curLine;if(firstInvalidLoc){this.state.firstInvalidTemplateEscapePos=new Position(firstInvalidLoc.curLine,firstInvalidLoc.pos-firstInvalidLoc.lineStart,this.sourceToOffsetPos(firstInvalidLoc.pos));}if(this.input.codePointAt(pos)===96){this.finishToken(24,firstInvalidLoc?null:opening+str+\"`\");}else{this.state.pos++;this.finishToken(25,firstInvalidLoc?null:opening+str+\"${\");}}},{key:\"recordStrictModeErrors\",value:function recordStrictModeErrors(toParseError,at){var index=at.index;if(this.state.strict&&!this.state.strictErrors.has(index)){this.raise(toParseError,at);}else{this.state.strictErrors.set(index,[toParseError,at]);}}},{key:\"readWord1\",value:function readWord1(firstCode){this.state.containsEsc=false;var word=\"\";var start=this.state.pos;var chunkStart=this.state.pos;if(firstCode!==undefined){this.state.pos+=firstCode<=0xffff?1:2;}while(this.state.pos<this.length){var ch=this.codePointAtPos(this.state.pos);if(isIdentifierChar(ch)){this.state.pos+=ch<=0xffff?1:2;}else if(ch===92){this.state.containsEsc=true;word+=this.input.slice(chunkStart,this.state.pos);var escStart=this.state.curPosition();var identifierCheck=this.state.pos===start?isIdentifierStart:isIdentifierChar;if(this.input.charCodeAt(++this.state.pos)!==117){this.raise(Errors.MissingUnicodeEscape,this.state.curPosition());chunkStart=this.state.pos-1;continue;}++this.state.pos;var esc=this.readCodePoint(true);if(esc!==null){if(!identifierCheck(esc)){this.raise(Errors.EscapedCharNotAnIdentifier,escStart);}word+=String.fromCodePoint(esc);}chunkStart=this.state.pos;}else{break;}}return word+this.input.slice(chunkStart,this.state.pos);}},{key:\"readWord\",value:function readWord(firstCode){var word=this.readWord1(firstCode);var type=keywords$1.get(word);if(type!==undefined){this.finishToken(type,tokenLabelName(type));}else{this.finishToken(132,word);}}},{key:\"checkKeywordEscapes\",value:function checkKeywordEscapes(){var type=this.state.type;if(tokenIsKeyword(type)&&this.state.containsEsc){this.raise(Errors.InvalidEscapedReservedWord,this.state.startLoc,{reservedWord:tokenLabelName(type)});}}},{key:\"raise\",value:function raise(toParseError,at){var details=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};var loc=at instanceof Position?at:at.loc.start;var error=toParseError(loc,details);if(!(this.optionFlags&2048))throw error;if(!this.isLookahead)this.state.errors.push(error);return error;}},{key:\"raiseOverwrite\",value:function raiseOverwrite(toParseError,at){var details=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};var loc=at instanceof Position?at:at.loc.start;var pos=loc.index;var errors=this.state.errors;for(var i=errors.length-1;i>=0;i--){var error=errors[i];if(error.loc.index===pos){return errors[i]=toParseError(loc,details);}if(error.loc.index<pos)break;}return this.raise(toParseError,at,details);}},{key:\"updateContext\",value:function updateContext(prevType){}},{key:\"unexpected\",value:function unexpected(loc,type){throw this.raise(Errors.UnexpectedToken,loc!=null?loc:this.state.startLoc,{expected:type?tokenLabelName(type):null});}},{key:\"expectPlugin\",value:function expectPlugin(pluginName,loc){if(this.hasPlugin(pluginName)){return true;}throw this.raise(Errors.MissingPlugin,loc!=null?loc:this.state.startLoc,{missingPlugin:[pluginName]});}},{key:\"expectOnePlugin\",value:function expectOnePlugin(pluginNames){var _this19=this;if(!pluginNames.some(function(name){return _this19.hasPlugin(name);})){throw this.raise(Errors.MissingOneOfPlugins,this.state.startLoc,{missingPlugin:pluginNames});}}},{key:\"errorBuilder\",value:function errorBuilder(error){var _this20=this;return function(pos,lineStart,curLine){_this20.raise(error,buildPosition(pos,lineStart,curLine));};}}]);}(CommentsParser);var ClassScope=/*#__PURE__*/_createClass(function ClassScope(){_classCallCheck(this,ClassScope);this.privateNames=new Set();this.loneAccessors=new Map();this.undefinedPrivateNames=new Map();});var ClassScopeHandler=/*#__PURE__*/function(){function ClassScopeHandler(parser){_classCallCheck(this,ClassScopeHandler);this.parser=void 0;this.stack=[];this.undefinedPrivateNames=new Map();this.parser=parser;}return _createClass(ClassScopeHandler,[{key:\"current\",value:function current(){return this.stack[this.stack.length-1];}},{key:\"enter\",value:function enter(){this.stack.push(new ClassScope());}},{key:\"exit\",value:function exit(){var oldClassScope=this.stack.pop();var current=this.current();for(var _i4=0,_Array$from=Array.from(oldClassScope.undefinedPrivateNames);_i4<_Array$from.length;_i4++){var _Array$from$_i=_slicedToArray(_Array$from[_i4],2),name=_Array$from$_i[0],loc=_Array$from$_i[1];if(current){if(!current.undefinedPrivateNames.has(name)){current.undefinedPrivateNames.set(name,loc);}}else{this.parser.raise(Errors.InvalidPrivateFieldResolution,loc,{identifierName:name});}}}},{key:\"declarePrivateName\",value:function declarePrivateName(name,elementType,loc){var _this$current=this.current(),privateNames=_this$current.privateNames,loneAccessors=_this$current.loneAccessors,undefinedPrivateNames=_this$current.undefinedPrivateNames;var redefined=privateNames.has(name);if(elementType&3){var accessor=redefined&&loneAccessors.get(name);if(accessor){var oldStatic=accessor&4;var newStatic=elementType&4;var oldKind=accessor&3;var newKind=elementType&3;redefined=oldKind===newKind||oldStatic!==newStatic;if(!redefined)loneAccessors.delete(name);}else if(!redefined){loneAccessors.set(name,elementType);}}if(redefined){this.parser.raise(Errors.PrivateNameRedeclaration,loc,{identifierName:name});}privateNames.add(name);undefinedPrivateNames.delete(name);}},{key:\"usePrivateName\",value:function usePrivateName(name,loc){var classScope;var _iterator5=_createForOfIteratorHelper(this.stack),_step5;try{for(_iterator5.s();!(_step5=_iterator5.n()).done;){classScope=_step5.value;if(classScope.privateNames.has(name))return;}}catch(err){_iterator5.e(err);}finally{_iterator5.f();}if(classScope){classScope.undefinedPrivateNames.set(name,loc);}else{this.parser.raise(Errors.InvalidPrivateFieldResolution,loc,{identifierName:name});}}}]);}();var ExpressionScope=/*#__PURE__*/function(){function ExpressionScope(){var type=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0;_classCallCheck(this,ExpressionScope);this.type=type;}return _createClass(ExpressionScope,[{key:\"canBeArrowParameterDeclaration\",value:function canBeArrowParameterDeclaration(){return this.type===2||this.type===1;}},{key:\"isCertainlyParameterDeclaration\",value:function isCertainlyParameterDeclaration(){return this.type===3;}}]);}();var ArrowHeadParsingScope=/*#__PURE__*/function(_ExpressionScope){function ArrowHeadParsingScope(type){var _this21;_classCallCheck(this,ArrowHeadParsingScope);_this21=_callSuper(this,ArrowHeadParsingScope,[type]);_this21.declarationErrors=new Map();return _this21;}_inherits(ArrowHeadParsingScope,_ExpressionScope);return _createClass(ArrowHeadParsingScope,[{key:\"recordDeclarationError\",value:function recordDeclarationError(ParsingErrorClass,at){var index=at.index;this.declarationErrors.set(index,[ParsingErrorClass,at]);}},{key:\"clearDeclarationError\",value:function clearDeclarationError(index){this.declarationErrors.delete(index);}},{key:\"iterateErrors\",value:function iterateErrors(iterator){this.declarationErrors.forEach(iterator);}}]);}(ExpressionScope);var ExpressionScopeHandler=/*#__PURE__*/function(){function ExpressionScopeHandler(parser){_classCallCheck(this,ExpressionScopeHandler);this.parser=void 0;this.stack=[new ExpressionScope()];this.parser=parser;}return _createClass(ExpressionScopeHandler,[{key:\"enter\",value:function enter(scope){this.stack.push(scope);}},{key:\"exit\",value:function exit(){this.stack.pop();}},{key:\"recordParameterInitializerError\",value:function recordParameterInitializerError(toParseError,node){var origin=node.loc.start;var stack=this.stack;var i=stack.length-1;var scope=stack[i];while(!scope.isCertainlyParameterDeclaration()){if(scope.canBeArrowParameterDeclaration()){scope.recordDeclarationError(toParseError,origin);}else{return;}scope=stack[--i];}this.parser.raise(toParseError,origin);}},{key:\"recordArrowParameterBindingError\",value:function recordArrowParameterBindingError(error,node){var stack=this.stack;var scope=stack[stack.length-1];var origin=node.loc.start;if(scope.isCertainlyParameterDeclaration()){this.parser.raise(error,origin);}else if(scope.canBeArrowParameterDeclaration()){scope.recordDeclarationError(error,origin);}else{return;}}},{key:\"recordAsyncArrowParametersError\",value:function recordAsyncArrowParametersError(at){var stack=this.stack;var i=stack.length-1;var scope=stack[i];while(scope.canBeArrowParameterDeclaration()){if(scope.type===2){scope.recordDeclarationError(Errors.AwaitBindingIdentifier,at);}scope=stack[--i];}}},{key:\"validateAsPattern\",value:function validateAsPattern(){var _this22=this;var stack=this.stack;var currentScope=stack[stack.length-1];if(!currentScope.canBeArrowParameterDeclaration())return;currentScope.iterateErrors(function(_ref61){var _ref62=_slicedToArray(_ref61,2),toParseError=_ref62[0],loc=_ref62[1];_this22.parser.raise(toParseError,loc);var i=stack.length-2;var scope=stack[i];while(scope.canBeArrowParameterDeclaration()){scope.clearDeclarationError(loc.index);scope=stack[--i];}});}}]);}();function newParameterDeclarationScope(){return new ExpressionScope(3);}function newArrowHeadScope(){return new ArrowHeadParsingScope(1);}function newAsyncArrowScope(){return new ArrowHeadParsingScope(2);}function newExpressionScope(){return new ExpressionScope();}var UtilParser=/*#__PURE__*/function(_Tokenizer){function UtilParser(){_classCallCheck(this,UtilParser);return _callSuper(this,UtilParser,arguments);}_inherits(UtilParser,_Tokenizer);return _createClass(UtilParser,[{key:\"addExtra\",value:function addExtra(node,key,value){var enumerable=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;if(!node)return;var extra=node.extra;if(extra==null){extra={};node.extra=extra;}if(enumerable){extra[key]=value;}else{Object.defineProperty(extra,key,{enumerable:enumerable,value:value});}}},{key:\"isContextual\",value:function isContextual(token){return this.state.type===token&&!this.state.containsEsc;}},{key:\"isUnparsedContextual\",value:function isUnparsedContextual(nameStart,name){if(this.input.startsWith(name,nameStart)){var nextCh=this.input.charCodeAt(nameStart+name.length);return!(isIdentifierChar(nextCh)||(nextCh&0xfc00)===0xd800);}return false;}},{key:\"isLookaheadContextual\",value:function isLookaheadContextual(name){var next=this.nextTokenStart();return this.isUnparsedContextual(next,name);}},{key:\"eatContextual\",value:function eatContextual(token){if(this.isContextual(token)){this.next();return true;}return false;}},{key:\"expectContextual\",value:function expectContextual(token,toParseError){if(!this.eatContextual(token)){if(toParseError!=null){throw this.raise(toParseError,this.state.startLoc);}this.unexpected(null,token);}}},{key:\"canInsertSemicolon\",value:function canInsertSemicolon(){return this.match(140)||this.match(8)||this.hasPrecedingLineBreak();}},{key:\"hasPrecedingLineBreak\",value:function hasPrecedingLineBreak(){return hasNewLine(this.input,this.offsetToSourcePos(this.state.lastTokEndLoc.index),this.state.start);}},{key:\"hasFollowingLineBreak\",value:function hasFollowingLineBreak(){return hasNewLine(this.input,this.state.end,this.nextTokenStart());}},{key:\"isLineTerminator\",value:function isLineTerminator(){return this.eat(13)||this.canInsertSemicolon();}},{key:\"semicolon\",value:function semicolon(){var allowAsi=arguments.length>0&&arguments[0]!==undefined?arguments[0]:true;if(allowAsi?this.isLineTerminator():this.eat(13))return;this.raise(Errors.MissingSemicolon,this.state.lastTokEndLoc);}},{key:\"expect\",value:function expect(type,loc){if(!this.eat(type)){this.unexpected(loc,type);}}},{key:\"tryParse\",value:function tryParse(fn){var oldState=arguments.length>1&&arguments[1]!==undefined?arguments[1]:this.state.clone();var abortSignal={node:null};try{var node=fn(function(){var node=arguments.length>0&&arguments[0]!==undefined?arguments[0]:null;abortSignal.node=node;throw abortSignal;});if(this.state.errors.length>oldState.errors.length){var failState=this.state;this.state=oldState;this.state.tokensLength=failState.tokensLength;return{node:node,error:failState.errors[oldState.errors.length],thrown:false,aborted:false,failState:failState};}return{node:node,error:null,thrown:false,aborted:false,failState:null};}catch(error){var _failState=this.state;this.state=oldState;if(error instanceof SyntaxError){return{node:null,error:error,thrown:true,aborted:false,failState:_failState};}if(error===abortSignal){return{node:abortSignal.node,error:null,thrown:false,aborted:true,failState:_failState};}throw error;}}},{key:\"checkExpressionErrors\",value:function checkExpressionErrors(refExpressionErrors,andThrow){if(!refExpressionErrors)return false;var shorthandAssignLoc=refExpressionErrors.shorthandAssignLoc,doubleProtoLoc=refExpressionErrors.doubleProtoLoc,privateKeyLoc=refExpressionErrors.privateKeyLoc,optionalParametersLoc=refExpressionErrors.optionalParametersLoc,voidPatternLoc=refExpressionErrors.voidPatternLoc;var hasErrors=!!shorthandAssignLoc||!!doubleProtoLoc||!!optionalParametersLoc||!!privateKeyLoc||!!voidPatternLoc;if(!andThrow){return hasErrors;}if(shorthandAssignLoc!=null){this.raise(Errors.InvalidCoverInitializedName,shorthandAssignLoc);}if(doubleProtoLoc!=null){this.raise(Errors.DuplicateProto,doubleProtoLoc);}if(privateKeyLoc!=null){this.raise(Errors.UnexpectedPrivateField,privateKeyLoc);}if(optionalParametersLoc!=null){this.unexpected(optionalParametersLoc);}if(voidPatternLoc!=null){this.raise(Errors.InvalidCoverDiscardElement,voidPatternLoc);}}},{key:\"isLiteralPropertyName\",value:function isLiteralPropertyName(){return tokenIsLiteralPropertyName(this.state.type);}},{key:\"isPrivateName\",value:function isPrivateName(node){return node.type===\"PrivateName\";}},{key:\"getPrivateNameSV\",value:function getPrivateNameSV(node){return node.id.name;}},{key:\"hasPropertyAsPrivateName\",value:function hasPropertyAsPrivateName(node){return(node.type===\"MemberExpression\"||node.type===\"OptionalMemberExpression\")&&this.isPrivateName(node.property);}},{key:\"isObjectProperty\",value:function isObjectProperty(node){return node.type===\"ObjectProperty\";}},{key:\"isObjectMethod\",value:function isObjectMethod(node){return node.type===\"ObjectMethod\";}},{key:\"initializeScopes\",value:function initializeScopes(){var _this23=this;var inModule=arguments.length>0&&arguments[0]!==undefined?arguments[0]:this.options.sourceType===\"module\";var oldLabels=this.state.labels;this.state.labels=[];var oldExportedIdentifiers=this.exportedIdentifiers;this.exportedIdentifiers=new Set();var oldInModule=this.inModule;this.inModule=inModule;var oldScope=this.scope;var ScopeHandler=this.getScopeHandler();this.scope=new ScopeHandler(this,inModule);var oldProdParam=this.prodParam;this.prodParam=new ProductionParameterHandler();var oldClassScope=this.classScope;this.classScope=new ClassScopeHandler(this);var oldExpressionScope=this.expressionScope;this.expressionScope=new ExpressionScopeHandler(this);return function(){_this23.state.labels=oldLabels;_this23.exportedIdentifiers=oldExportedIdentifiers;_this23.inModule=oldInModule;_this23.scope=oldScope;_this23.prodParam=oldProdParam;_this23.classScope=oldClassScope;_this23.expressionScope=oldExpressionScope;};}},{key:\"enterInitialScopes\",value:function enterInitialScopes(){var paramFlags=0;if(this.inModule||this.optionFlags&1){paramFlags|=2;}if(this.optionFlags&32){paramFlags|=1;}var isCommonJS=!this.inModule&&this.options.sourceType===\"commonjs\";if(isCommonJS||this.optionFlags&2){paramFlags|=4;}this.prodParam.enter(paramFlags);var scopeFlags=isCommonJS?514:1;if(this.optionFlags&4){scopeFlags|=512;}this.scope.enter(scopeFlags);}},{key:\"checkDestructuringPrivate\",value:function checkDestructuringPrivate(refExpressionErrors){var privateKeyLoc=refExpressionErrors.privateKeyLoc;if(privateKeyLoc!==null){this.expectPlugin(\"destructuringPrivate\",privateKeyLoc);}}}]);}(Tokenizer);var ExpressionErrors=/*#__PURE__*/_createClass(function ExpressionErrors(){_classCallCheck(this,ExpressionErrors);this.shorthandAssignLoc=null;this.doubleProtoLoc=null;this.privateKeyLoc=null;this.optionalParametersLoc=null;this.voidPatternLoc=null;});var Node=/*#__PURE__*/_createClass(function Node(parser,pos,loc){_classCallCheck(this,Node);this.type=\"\";this.start=pos;this.end=0;this.loc=new SourceLocation(loc);if((parser==null?void 0:parser.optionFlags)&128)this.range=[pos,0];if(parser!=null&&parser.filename)this.loc.filename=parser.filename;});var NodePrototype=Node.prototype;{NodePrototype.__clone=function(){var newNode=new Node(undefined,this.start,this.loc.start);var keys=Object.keys(this);for(var i=0,length=keys.length;i<length;i++){var key=keys[i];if(key!==\"leadingComments\"&&key!==\"trailingComments\"&&key!==\"innerComments\"){newNode[key]=this[key];}}return newNode;};}var NodeUtils=/*#__PURE__*/function(_UtilParser){function NodeUtils(){_classCallCheck(this,NodeUtils);return _callSuper(this,NodeUtils,arguments);}_inherits(NodeUtils,_UtilParser);return _createClass(NodeUtils,[{key:\"startNode\",value:function startNode(){var loc=this.state.startLoc;return new Node(this,loc.index,loc);}},{key:\"startNodeAt\",value:function startNodeAt(loc){return new Node(this,loc.index,loc);}},{key:\"startNodeAtNode\",value:function startNodeAtNode(type){return this.startNodeAt(type.loc.start);}},{key:\"finishNode\",value:function finishNode(node,type){return this.finishNodeAt(node,type,this.state.lastTokEndLoc);}},{key:\"finishNodeAt\",value:function finishNodeAt(node,type,endLoc){node.type=type;node.end=endLoc.index;node.loc.end=endLoc;if(this.optionFlags&128)node.range[1]=endLoc.index;if(this.optionFlags&4096){this.processComment(node);}return node;}},{key:\"resetStartLocation\",value:function resetStartLocation(node,startLoc){node.start=startLoc.index;node.loc.start=startLoc;if(this.optionFlags&128)node.range[0]=startLoc.index;}},{key:\"resetEndLocation\",value:function resetEndLocation(node){var endLoc=arguments.length>1&&arguments[1]!==undefined?arguments[1]:this.state.lastTokEndLoc;node.end=endLoc.index;node.loc.end=endLoc;if(this.optionFlags&128)node.range[1]=endLoc.index;}},{key:\"resetStartLocationFromNode\",value:function resetStartLocationFromNode(node,locationNode){this.resetStartLocation(node,locationNode.loc.start);}},{key:\"castNodeTo\",value:function castNodeTo(node,type){node.type=type;return node;}},{key:\"cloneIdentifier\",value:function cloneIdentifier(node){var type=node.type,start=node.start,end=node.end,loc=node.loc,range=node.range,name=node.name;var cloned=Object.create(NodePrototype);cloned.type=type;cloned.start=start;cloned.end=end;cloned.loc=loc;cloned.range=range;cloned.name=name;if(node.extra)cloned.extra=node.extra;return cloned;}},{key:\"cloneStringLiteral\",value:function cloneStringLiteral(node){var type=node.type,start=node.start,end=node.end,loc=node.loc,range=node.range,extra=node.extra;var cloned=Object.create(NodePrototype);cloned.type=type;cloned.start=start;cloned.end=end;cloned.loc=loc;cloned.range=range;cloned.extra=extra;cloned.value=node.value;return cloned;}}]);}(UtilParser);var _unwrapParenthesizedExpression=function unwrapParenthesizedExpression(node){return node.type===\"ParenthesizedExpression\"?_unwrapParenthesizedExpression(node.expression):node;};var LValParser=/*#__PURE__*/function(_NodeUtils){function LValParser(){_classCallCheck(this,LValParser);return _callSuper(this,LValParser,arguments);}_inherits(LValParser,_NodeUtils);return _createClass(LValParser,[{key:\"toAssignable\",value:function toAssignable(node){var isLHS=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;var _node$extra,_node$extra3;var parenthesized=undefined;if(node.type===\"ParenthesizedExpression\"||(_node$extra=node.extra)!=null&&_node$extra.parenthesized){parenthesized=_unwrapParenthesizedExpression(node);if(isLHS){if(parenthesized.type===\"Identifier\"){this.expressionScope.recordArrowParameterBindingError(Errors.InvalidParenthesizedAssignment,node);}else if(parenthesized.type!==\"MemberExpression\"&&!this.isOptionalMemberExpression(parenthesized)){this.raise(Errors.InvalidParenthesizedAssignment,node);}}else{this.raise(Errors.InvalidParenthesizedAssignment,node);}}switch(node.type){case\"Identifier\":case\"ObjectPattern\":case\"ArrayPattern\":case\"AssignmentPattern\":case\"RestElement\":case\"VoidPattern\":break;case\"ObjectExpression\":this.castNodeTo(node,\"ObjectPattern\");for(var i=0,length=node.properties.length,last=length-1;i<length;i++){var _node$extra2;var prop=node.properties[i];var isLast=i===last;this.toAssignableObjectExpressionProp(prop,isLast,isLHS);if(isLast&&prop.type===\"RestElement\"&&(_node$extra2=node.extra)!=null&&_node$extra2.trailingCommaLoc){this.raise(Errors.RestTrailingComma,node.extra.trailingCommaLoc);}}break;case\"ObjectProperty\":{var key=node.key,value=node.value;if(this.isPrivateName(key)){this.classScope.usePrivateName(this.getPrivateNameSV(key),key.loc.start);}this.toAssignable(value,isLHS);break;}case\"SpreadElement\":{throw new Error(\"Internal @babel/parser error (this is a bug, please report it).\"+\" SpreadElement should be converted by .toAssignable's caller.\");}case\"ArrayExpression\":this.castNodeTo(node,\"ArrayPattern\");this.toAssignableList(node.elements,(_node$extra3=node.extra)==null?void 0:_node$extra3.trailingCommaLoc,isLHS);break;case\"AssignmentExpression\":if(node.operator!==\"=\"){this.raise(Errors.MissingEqInAssignment,node.left.loc.end);}this.castNodeTo(node,\"AssignmentPattern\");delete node.operator;if(node.left.type===\"VoidPattern\"){this.raise(Errors.VoidPatternInitializer,node.left);}this.toAssignable(node.left,isLHS);break;case\"ParenthesizedExpression\":this.toAssignable(parenthesized,isLHS);break;}}},{key:\"toAssignableObjectExpressionProp\",value:function toAssignableObjectExpressionProp(prop,isLast,isLHS){if(prop.type===\"ObjectMethod\"){this.raise(prop.kind===\"get\"||prop.kind===\"set\"?Errors.PatternHasAccessor:Errors.PatternHasMethod,prop.key);}else if(prop.type===\"SpreadElement\"){this.castNodeTo(prop,\"RestElement\");var arg=prop.argument;this.checkToRestConversion(arg,false);this.toAssignable(arg,isLHS);if(!isLast){this.raise(Errors.RestTrailingComma,prop);}}else{this.toAssignable(prop,isLHS);}}},{key:\"toAssignableList\",value:function toAssignableList(exprList,trailingCommaLoc,isLHS){var end=exprList.length-1;for(var i=0;i<=end;i++){var elt=exprList[i];if(!elt)continue;this.toAssignableListItem(exprList,i,isLHS);if(elt.type===\"RestElement\"){if(i<end){this.raise(Errors.RestTrailingComma,elt);}else if(trailingCommaLoc){this.raise(Errors.RestTrailingComma,trailingCommaLoc);}}}}},{key:\"toAssignableListItem\",value:function toAssignableListItem(exprList,index,isLHS){var node=exprList[index];if(node.type===\"SpreadElement\"){this.castNodeTo(node,\"RestElement\");var arg=node.argument;this.checkToRestConversion(arg,true);this.toAssignable(arg,isLHS);}else{this.toAssignable(node,isLHS);}}},{key:\"isAssignable\",value:function isAssignable(node,isBinding){var _this24=this;switch(node.type){case\"Identifier\":case\"ObjectPattern\":case\"ArrayPattern\":case\"AssignmentPattern\":case\"RestElement\":case\"VoidPattern\":return true;case\"ObjectExpression\":{var last=node.properties.length-1;return node.properties.every(function(prop,i){return prop.type!==\"ObjectMethod\"&&(i===last||prop.type!==\"SpreadElement\")&&_this24.isAssignable(prop);});}case\"ObjectProperty\":return this.isAssignable(node.value);case\"SpreadElement\":return this.isAssignable(node.argument);case\"ArrayExpression\":return node.elements.every(function(element){return element===null||_this24.isAssignable(element);});case\"AssignmentExpression\":return node.operator===\"=\";case\"ParenthesizedExpression\":return this.isAssignable(node.expression);case\"MemberExpression\":case\"OptionalMemberExpression\":return!isBinding;default:return false;}}},{key:\"toReferencedList\",value:function toReferencedList(exprList,isParenthesizedExpr){return exprList;}},{key:\"toReferencedListDeep\",value:function toReferencedListDeep(exprList,isParenthesizedExpr){this.toReferencedList(exprList,isParenthesizedExpr);var _iterator6=_createForOfIteratorHelper(exprList),_step6;try{for(_iterator6.s();!(_step6=_iterator6.n()).done;){var expr=_step6.value;if((expr==null?void 0:expr.type)===\"ArrayExpression\"){this.toReferencedListDeep(expr.elements);}}}catch(err){_iterator6.e(err);}finally{_iterator6.f();}}},{key:\"parseSpread\",value:function parseSpread(refExpressionErrors){var node=this.startNode();this.next();node.argument=this.parseMaybeAssignAllowIn(refExpressionErrors,undefined);return this.finishNode(node,\"SpreadElement\");}},{key:\"parseRestBinding\",value:function parseRestBinding(){var node=this.startNode();this.next();var argument=this.parseBindingAtom();if(argument.type===\"VoidPattern\"){this.raise(Errors.UnexpectedVoidPattern,argument);}node.argument=argument;return this.finishNode(node,\"RestElement\");}},{key:\"parseBindingAtom\",value:function parseBindingAtom(){switch(this.state.type){case 0:{var node=this.startNode();this.next();node.elements=this.parseBindingList(3,93,1);return this.finishNode(node,\"ArrayPattern\");}case 5:return this.parseObjectLike(8,true);case 88:return this.parseVoidPattern(null);}return this.parseIdentifier();}},{key:\"parseBindingList\",value:function parseBindingList(close,closeCharCode,flags){var allowEmpty=flags&1;var elts=[];var first=true;while(!this.eat(close)){if(first){first=false;}else{this.expect(12);}if(allowEmpty&&this.match(12)){elts.push(null);}else if(this.eat(close)){break;}else if(this.match(21)){var rest=this.parseRestBinding();if(this.hasPlugin(\"flow\")||flags&2){rest=this.parseFunctionParamType(rest);}elts.push(rest);if(!this.checkCommaAfterRest(closeCharCode)){this.expect(close);break;}}else{var decorators=[];if(flags&2){if(this.match(26)&&this.hasPlugin(\"decorators\")){this.raise(Errors.UnsupportedParameterDecorator,this.state.startLoc);}while(this.match(26)){decorators.push(this.parseDecorator());}}elts.push(this.parseBindingElement(flags,decorators));}}return elts;}},{key:\"parseBindingRestProperty\",value:function parseBindingRestProperty(prop){this.next();if(this.hasPlugin(\"discardBinding\")&&this.match(88)){prop.argument=this.parseVoidPattern(null);this.raise(Errors.UnexpectedVoidPattern,prop.argument);}else{prop.argument=this.parseIdentifier();}this.checkCommaAfterRest(125);return this.finishNode(prop,\"RestElement\");}},{key:\"parseBindingProperty\",value:function parseBindingProperty(){var _this$state2=this.state,type=_this$state2.type,startLoc=_this$state2.startLoc;if(type===21){return this.parseBindingRestProperty(this.startNode());}var prop=this.startNode();if(type===139){this.expectPlugin(\"destructuringPrivate\",startLoc);this.classScope.usePrivateName(this.state.value,startLoc);prop.key=this.parsePrivateName();}else{this.parsePropertyName(prop);}prop.method=false;return this.parseObjPropValue(prop,startLoc,false,false,true,false);}},{key:\"parseBindingElement\",value:function parseBindingElement(flags,decorators){var left=this.parseMaybeDefault();if(this.hasPlugin(\"flow\")||flags&2){this.parseFunctionParamType(left);}if(decorators.length){left.decorators=decorators;this.resetStartLocationFromNode(left,decorators[0]);}var elt=this.parseMaybeDefault(left.loc.start,left);return elt;}},{key:\"parseFunctionParamType\",value:function parseFunctionParamType(param){return param;}},{key:\"parseMaybeDefault\",value:function parseMaybeDefault(startLoc,left){startLoc!=null?startLoc:startLoc=this.state.startLoc;left=left!=null?left:this.parseBindingAtom();if(!this.eat(29))return left;var node=this.startNodeAt(startLoc);if(left.type===\"VoidPattern\"){this.raise(Errors.VoidPatternInitializer,left);}node.left=left;node.right=this.parseMaybeAssignAllowIn();return this.finishNode(node,\"AssignmentPattern\");}},{key:\"isValidLVal\",value:function isValidLVal(type,isUnparenthesizedInAssign,binding){switch(type){case\"AssignmentPattern\":return\"left\";case\"RestElement\":return\"argument\";case\"ObjectProperty\":return\"value\";case\"ParenthesizedExpression\":return\"expression\";case\"ArrayPattern\":return\"elements\";case\"ObjectPattern\":return\"properties\";case\"VoidPattern\":return true;}return false;}},{key:\"isOptionalMemberExpression\",value:function isOptionalMemberExpression(expression){return expression.type===\"OptionalMemberExpression\";}},{key:\"checkLVal\",value:function checkLVal(expression,ancestor){var binding=arguments.length>2&&arguments[2]!==undefined?arguments[2]:64;var checkClashes=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;var strictModeChanged=arguments.length>4&&arguments[4]!==undefined?arguments[4]:false;var hasParenthesizedAncestor=arguments.length>5&&arguments[5]!==undefined?arguments[5]:false;var _expression$extra;var type=expression.type;if(this.isObjectMethod(expression))return;var isOptionalMemberExpression=this.isOptionalMemberExpression(expression);if(isOptionalMemberExpression||type===\"MemberExpression\"){if(isOptionalMemberExpression){this.expectPlugin(\"optionalChainingAssign\",expression.loc.start);if(ancestor.type!==\"AssignmentExpression\"){this.raise(Errors.InvalidLhsOptionalChaining,expression,{ancestor:ancestor});}}if(binding!==64){this.raise(Errors.InvalidPropertyBindingPattern,expression);}return;}if(type===\"Identifier\"){this.checkIdentifier(expression,binding,strictModeChanged);var name=expression.name;if(checkClashes){if(checkClashes.has(name)){this.raise(Errors.ParamDupe,expression);}else{checkClashes.add(name);}}return;}else if(type===\"VoidPattern\"&&ancestor.type===\"CatchClause\"){this.raise(Errors.VoidPatternCatchClauseParam,expression);}var validity=this.isValidLVal(type,!(hasParenthesizedAncestor||(_expression$extra=expression.extra)!=null&&_expression$extra.parenthesized)&&ancestor.type===\"AssignmentExpression\",binding);if(validity===true)return;if(validity===false){var ParseErrorClass=binding===64?Errors.InvalidLhs:Errors.InvalidLhsBinding;this.raise(ParseErrorClass,expression,{ancestor:ancestor});return;}var key,isParenthesizedExpression;if(typeof validity===\"string\"){key=validity;isParenthesizedExpression=type===\"ParenthesizedExpression\";}else{var _validity=_slicedToArray(validity,2);key=_validity[0];isParenthesizedExpression=_validity[1];}var nextAncestor=type===\"ArrayPattern\"||type===\"ObjectPattern\"?{type:type}:ancestor;var val=expression[key];if(Array.isArray(val)){var _iterator7=_createForOfIteratorHelper(val),_step7;try{for(_iterator7.s();!(_step7=_iterator7.n()).done;){var child=_step7.value;if(child){this.checkLVal(child,nextAncestor,binding,checkClashes,strictModeChanged,isParenthesizedExpression);}}}catch(err){_iterator7.e(err);}finally{_iterator7.f();}}else if(val){this.checkLVal(val,nextAncestor,binding,checkClashes,strictModeChanged,isParenthesizedExpression);}}},{key:\"checkIdentifier\",value:function checkIdentifier(at,bindingType){var strictModeChanged=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;if(this.state.strict&&(strictModeChanged?isStrictBindReservedWord(at.name,this.inModule):isStrictBindOnlyReservedWord(at.name))){if(bindingType===64){this.raise(Errors.StrictEvalArguments,at,{referenceName:at.name});}else{this.raise(Errors.StrictEvalArgumentsBinding,at,{bindingName:at.name});}}if(bindingType&8192&&at.name===\"let\"){this.raise(Errors.LetInLexicalBinding,at);}if(!(bindingType&64)){this.declareNameFromIdentifier(at,bindingType);}}},{key:\"declareNameFromIdentifier\",value:function declareNameFromIdentifier(identifier,binding){this.scope.declareName(identifier.name,binding,identifier.loc.start);}},{key:\"checkToRestConversion\",value:function checkToRestConversion(node,allowPattern){switch(node.type){case\"ParenthesizedExpression\":this.checkToRestConversion(node.expression,allowPattern);break;case\"Identifier\":case\"MemberExpression\":break;case\"ArrayExpression\":case\"ObjectExpression\":if(allowPattern)break;default:this.raise(Errors.InvalidRestAssignmentPattern,node);}}},{key:\"checkCommaAfterRest\",value:function checkCommaAfterRest(close){if(!this.match(12)){return false;}this.raise(this.lookaheadCharCode()===close?Errors.RestTrailingComma:Errors.ElementAfterRest,this.state.startLoc);return true;}}]);}(NodeUtils);function nonNull(x){if(x==null){throw new Error(\"Unexpected \".concat(x,\" value.\"));}return x;}function assert(x){if(!x){throw new Error(\"Assert fail\");}}var TSErrors=ParseErrorEnum(_templateObject4||(_templateObject4=_taggedTemplateLiteral([\"typescript\"])))({AbstractMethodHasImplementation:function AbstractMethodHasImplementation(_ref63){var methodName=_ref63.methodName;return\"Method '\".concat(methodName,\"' cannot have an implementation because it is marked abstract.\");},AbstractPropertyHasInitializer:function AbstractPropertyHasInitializer(_ref64){var propertyName=_ref64.propertyName;return\"Property '\".concat(propertyName,\"' cannot have an initializer because it is marked abstract.\");},AccessorCannotBeOptional:\"An 'accessor' property cannot be declared optional.\",AccessorCannotDeclareThisParameter:\"'get' and 'set' accessors cannot declare 'this' parameters.\",AccessorCannotHaveTypeParameters:\"An accessor cannot have type parameters.\",ClassMethodHasDeclare:\"Class methods cannot have the 'declare' modifier.\",ClassMethodHasReadonly:\"Class methods cannot have the 'readonly' modifier.\",ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference:\"A 'const' initializer in an ambient context must be a string or numeric literal or literal enum reference.\",ConstructorHasTypeParameters:\"Type parameters cannot appear on a constructor declaration.\",DeclareAccessor:function DeclareAccessor(_ref65){var kind=_ref65.kind;return\"'declare' is not allowed in \".concat(kind,\"ters.\");},DeclareClassFieldHasInitializer:\"Initializers are not allowed in ambient contexts.\",DeclareFunctionHasImplementation:\"An implementation cannot be declared in ambient contexts.\",DuplicateAccessibilityModifier:function DuplicateAccessibilityModifier(_ref66){var modifier=_ref66.modifier;return\"Accessibility modifier already seen: '\".concat(modifier,\"'.\");},DuplicateModifier:function DuplicateModifier(_ref67){var modifier=_ref67.modifier;return\"Duplicate modifier: '\".concat(modifier,\"'.\");},EmptyHeritageClauseType:function EmptyHeritageClauseType(_ref68){var token=_ref68.token;return\"'\".concat(token,\"' list cannot be empty.\");},EmptyTypeArguments:\"Type argument list cannot be empty.\",EmptyTypeParameters:\"Type parameter list cannot be empty.\",ExpectedAmbientAfterExportDeclare:\"'export declare' must be followed by an ambient declaration.\",ImportAliasHasImportType:\"An import alias can not use 'import type'.\",ImportReflectionHasImportType:\"An `import module` declaration can not use `type` modifier\",IncompatibleModifiers:function IncompatibleModifiers(_ref69){var modifiers=_ref69.modifiers;return\"'\".concat(modifiers[0],\"' modifier cannot be used with '\").concat(modifiers[1],\"' modifier.\");},IndexSignatureHasAbstract:\"Index signatures cannot have the 'abstract' modifier.\",IndexSignatureHasAccessibility:function IndexSignatureHasAccessibility(_ref70){var modifier=_ref70.modifier;return\"Index signatures cannot have an accessibility modifier ('\".concat(modifier,\"').\");},IndexSignatureHasDeclare:\"Index signatures cannot have the 'declare' modifier.\",IndexSignatureHasOverride:\"'override' modifier cannot appear on an index signature.\",IndexSignatureHasStatic:\"Index signatures cannot have the 'static' modifier.\",InitializerNotAllowedInAmbientContext:\"Initializers are not allowed in ambient contexts.\",InvalidHeritageClauseType:function InvalidHeritageClauseType(_ref71){var token=_ref71.token;return\"'\".concat(token,\"' list can only include identifiers or qualified-names with optional type arguments.\");},InvalidModifierOnAwaitUsingDeclaration:function InvalidModifierOnAwaitUsingDeclaration(modifier){return\"'\".concat(modifier,\"' modifier cannot appear on an await using declaration.\");},InvalidModifierOnTypeMember:function InvalidModifierOnTypeMember(_ref72){var modifier=_ref72.modifier;return\"'\".concat(modifier,\"' modifier cannot appear on a type member.\");},InvalidModifierOnTypeParameter:function InvalidModifierOnTypeParameter(_ref73){var modifier=_ref73.modifier;return\"'\".concat(modifier,\"' modifier cannot appear on a type parameter.\");},InvalidModifierOnTypeParameterPositions:function InvalidModifierOnTypeParameterPositions(_ref74){var modifier=_ref74.modifier;return\"'\".concat(modifier,\"' modifier can only appear on a type parameter of a class, interface or type alias.\");},InvalidModifierOnUsingDeclaration:function InvalidModifierOnUsingDeclaration(modifier){return\"'\".concat(modifier,\"' modifier cannot appear on a using declaration.\");},InvalidModifiersOrder:function InvalidModifiersOrder(_ref75){var orderedModifiers=_ref75.orderedModifiers;return\"'\".concat(orderedModifiers[0],\"' modifier must precede '\").concat(orderedModifiers[1],\"' modifier.\");},InvalidPropertyAccessAfterInstantiationExpression:\"Invalid property access after an instantiation expression. \"+\"You can either wrap the instantiation expression in parentheses, or delete the type arguments.\",InvalidTupleMemberLabel:\"Tuple members must be labeled with a simple identifier.\",MissingInterfaceName:\"'interface' declarations must be followed by an identifier.\",NonAbstractClassHasAbstractMethod:\"Abstract methods can only appear within an abstract class.\",NonClassMethodPropertyHasAbstractModifier:\"'abstract' modifier can only appear on a class, method, or property declaration.\",OptionalTypeBeforeRequired:\"A required element cannot follow an optional element.\",OverrideNotInSubClass:\"This member cannot have an 'override' modifier because its containing class does not extend another class.\",PatternIsOptional:\"A binding pattern parameter cannot be optional in an implementation signature.\",PrivateElementHasAbstract:\"Private elements cannot have the 'abstract' modifier.\",PrivateElementHasAccessibility:function PrivateElementHasAccessibility(_ref76){var modifier=_ref76.modifier;return\"Private elements cannot have an accessibility modifier ('\".concat(modifier,\"').\");},ReadonlyForMethodSignature:\"'readonly' modifier can only appear on a property declaration or index signature.\",ReservedArrowTypeParam:\"This syntax is reserved in files with the .mts or .cts extension. Add a trailing comma, as in `<T,>() => ...`.\",ReservedTypeAssertion:\"This syntax is reserved in files with the .mts or .cts extension. Use an `as` expression instead.\",SetAccessorCannotHaveOptionalParameter:\"A 'set' accessor cannot have an optional parameter.\",SetAccessorCannotHaveRestParameter:\"A 'set' accessor cannot have rest parameter.\",SetAccessorCannotHaveReturnType:\"A 'set' accessor cannot have a return type annotation.\",SingleTypeParameterWithoutTrailingComma:function SingleTypeParameterWithoutTrailingComma(_ref77){var typeParameterName=_ref77.typeParameterName;return\"Single type parameter \".concat(typeParameterName,\" should have a trailing comma. Example usage: <\").concat(typeParameterName,\",>.\");},StaticBlockCannotHaveModifier:\"Static class blocks cannot have any modifier.\",TupleOptionalAfterType:\"A labeled tuple optional element must be declared using a question mark after the name and before the colon (`name?: type`), rather than after the type (`name: type?`).\",TypeAnnotationAfterAssign:\"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.\",TypeImportCannotSpecifyDefaultAndNamed:\"A type-only import can specify a default import or named bindings, but not both.\",TypeModifierIsUsedInTypeExports:\"The 'type' modifier cannot be used on a named export when 'export type' is used on its export statement.\",TypeModifierIsUsedInTypeImports:\"The 'type' modifier cannot be used on a named import when 'import type' is used on its import statement.\",UnexpectedParameterModifier:\"A parameter property is only allowed in a constructor implementation.\",UnexpectedReadonly:\"'readonly' type modifier is only permitted on array and tuple literal types.\",UnexpectedTypeAnnotation:\"Did not expect a type annotation here.\",UnexpectedTypeCastInParameter:\"Unexpected type cast in parameter position.\",UnsupportedImportTypeArgument:\"Argument in a type import must be a string literal.\",UnsupportedParameterPropertyKind:\"A parameter property may not be declared using a binding pattern.\",UnsupportedSignatureParameterKind:function UnsupportedSignatureParameterKind(_ref78){var type=_ref78.type;return\"Name in a signature must be an Identifier, ObjectPattern or ArrayPattern, instead got \".concat(type,\".\");},UsingDeclarationInAmbientContext:function UsingDeclarationInAmbientContext(kind){return\"'\".concat(kind,\"' declarations are not allowed in ambient contexts.\");}});function keywordTypeFromName(value){switch(value){case\"any\":return\"TSAnyKeyword\";case\"boolean\":return\"TSBooleanKeyword\";case\"bigint\":return\"TSBigIntKeyword\";case\"never\":return\"TSNeverKeyword\";case\"number\":return\"TSNumberKeyword\";case\"object\":return\"TSObjectKeyword\";case\"string\":return\"TSStringKeyword\";case\"symbol\":return\"TSSymbolKeyword\";case\"undefined\":return\"TSUndefinedKeyword\";case\"unknown\":return\"TSUnknownKeyword\";default:return undefined;}}function tsIsAccessModifier(modifier){return modifier===\"private\"||modifier===\"public\"||modifier===\"protected\";}function tsIsVarianceAnnotations(modifier){return modifier===\"in\"||modifier===\"out\";}var typescript=function typescript(superClass){return/*#__PURE__*/function(_superClass4){function TypeScriptParserMixin(){var _this25;_classCallCheck(this,TypeScriptParserMixin);for(var _len7=arguments.length,args=new Array(_len7),_key7=0;_key7<_len7;_key7++){args[_key7]=arguments[_key7];}_this25=_callSuper(this,TypeScriptParserMixin,[].concat(args));_this25.tsParseInOutModifiers=_this25.tsParseModifiers.bind(_this25,{allowedModifiers:[\"in\",\"out\"],disallowedModifiers:[\"const\",\"public\",\"private\",\"protected\",\"readonly\",\"declare\",\"abstract\",\"override\"],errorTemplate:TSErrors.InvalidModifierOnTypeParameter});_this25.tsParseConstModifier=_this25.tsParseModifiers.bind(_this25,{allowedModifiers:[\"const\"],disallowedModifiers:[\"in\",\"out\"],errorTemplate:TSErrors.InvalidModifierOnTypeParameterPositions});_this25.tsParseInOutConstModifiers=_this25.tsParseModifiers.bind(_this25,{allowedModifiers:[\"in\",\"out\",\"const\"],disallowedModifiers:[\"public\",\"private\",\"protected\",\"readonly\",\"declare\",\"abstract\",\"override\"],errorTemplate:TSErrors.InvalidModifierOnTypeParameter});return _this25;}_inherits(TypeScriptParserMixin,_superClass4);return _createClass(TypeScriptParserMixin,[{key:\"getScopeHandler\",value:function getScopeHandler(){return TypeScriptScopeHandler;}},{key:\"tsIsIdentifier\",value:function tsIsIdentifier(){return tokenIsIdentifier(this.state.type);}},{key:\"tsTokenCanFollowModifier\",value:function tsTokenCanFollowModifier(){return this.match(0)||this.match(5)||this.match(55)||this.match(21)||this.match(139)||this.isLiteralPropertyName();}},{key:\"tsNextTokenOnSameLineAndCanFollowModifier\",value:function tsNextTokenOnSameLineAndCanFollowModifier(){this.next();if(this.hasPrecedingLineBreak()){return false;}return this.tsTokenCanFollowModifier();}},{key:\"tsNextTokenCanFollowModifier\",value:function tsNextTokenCanFollowModifier(){if(this.match(106)){this.next();return this.tsTokenCanFollowModifier();}return this.tsNextTokenOnSameLineAndCanFollowModifier();}},{key:\"tsParseModifier\",value:function tsParseModifier(allowedModifiers,stopOnStartOfClassStaticBlock,hasSeenStaticModifier){if(!tokenIsIdentifier(this.state.type)&&this.state.type!==58&&this.state.type!==75){return undefined;}var modifier=this.state.value;if(allowedModifiers.includes(modifier)){if(hasSeenStaticModifier&&this.match(106)){return undefined;}if(stopOnStartOfClassStaticBlock&&this.tsIsStartOfStaticBlocks()){return undefined;}if(this.tsTryParse(this.tsNextTokenCanFollowModifier.bind(this))){return modifier;}}return undefined;}},{key:\"tsParseModifiers\",value:function tsParseModifiers(_ref79,modified){var _this26=this;var allowedModifiers=_ref79.allowedModifiers,disallowedModifiers=_ref79.disallowedModifiers,stopOnStartOfClassStaticBlock=_ref79.stopOnStartOfClassStaticBlock,_ref79$errorTemplate=_ref79.errorTemplate,errorTemplate=_ref79$errorTemplate===void 0?TSErrors.InvalidModifierOnTypeMember:_ref79$errorTemplate;var enforceOrder=function enforceOrder(loc,modifier,before,after){if(modifier===before&&modified[after]){_this26.raise(TSErrors.InvalidModifiersOrder,loc,{orderedModifiers:[before,after]});}};var incompatible=function incompatible(loc,modifier,mod1,mod2){if(modified[mod1]&&modifier===mod2||modified[mod2]&&modifier===mod1){_this26.raise(TSErrors.IncompatibleModifiers,loc,{modifiers:[mod1,mod2]});}};for(;;){var startLoc=this.state.startLoc;var modifier=this.tsParseModifier(allowedModifiers.concat(disallowedModifiers!=null?disallowedModifiers:[]),stopOnStartOfClassStaticBlock,modified.static);if(!modifier)break;if(tsIsAccessModifier(modifier)){if(modified.accessibility){this.raise(TSErrors.DuplicateAccessibilityModifier,startLoc,{modifier:modifier});}else{enforceOrder(startLoc,modifier,modifier,\"override\");enforceOrder(startLoc,modifier,modifier,\"static\");enforceOrder(startLoc,modifier,modifier,\"readonly\");modified.accessibility=modifier;}}else if(tsIsVarianceAnnotations(modifier)){if(modified[modifier]){this.raise(TSErrors.DuplicateModifier,startLoc,{modifier:modifier});}modified[modifier]=true;enforceOrder(startLoc,modifier,\"in\",\"out\");}else{if(hasOwnProperty.call(modified,modifier)){this.raise(TSErrors.DuplicateModifier,startLoc,{modifier:modifier});}else{enforceOrder(startLoc,modifier,\"static\",\"readonly\");enforceOrder(startLoc,modifier,\"static\",\"override\");enforceOrder(startLoc,modifier,\"override\",\"readonly\");enforceOrder(startLoc,modifier,\"abstract\",\"override\");incompatible(startLoc,modifier,\"declare\",\"override\");incompatible(startLoc,modifier,\"static\",\"abstract\");}modified[modifier]=true;}if(disallowedModifiers!=null&&disallowedModifiers.includes(modifier)){this.raise(errorTemplate,startLoc,{modifier:modifier});}}}},{key:\"tsIsListTerminator\",value:function tsIsListTerminator(kind){switch(kind){case\"EnumMembers\":case\"TypeMembers\":return this.match(8);case\"HeritageClauseElement\":return this.match(5);case\"TupleElementTypes\":return this.match(3);case\"TypeParametersOrArguments\":return this.match(48);}}},{key:\"tsParseList\",value:function tsParseList(kind,parseElement){var result=[];while(!this.tsIsListTerminator(kind)){result.push(parseElement());}return result;}},{key:\"tsParseDelimitedList\",value:function tsParseDelimitedList(kind,parseElement,refTrailingCommaPos){return nonNull(this.tsParseDelimitedListWorker(kind,parseElement,true,refTrailingCommaPos));}},{key:\"tsParseDelimitedListWorker\",value:function tsParseDelimitedListWorker(kind,parseElement,expectSuccess,refTrailingCommaPos){var result=[];var trailingCommaPos=-1;for(;;){if(this.tsIsListTerminator(kind)){break;}trailingCommaPos=-1;var element=parseElement();if(element==null){return undefined;}result.push(element);if(this.eat(12)){trailingCommaPos=this.state.lastTokStartLoc.index;continue;}if(this.tsIsListTerminator(kind)){break;}if(expectSuccess){this.expect(12);}return undefined;}if(refTrailingCommaPos){refTrailingCommaPos.value=trailingCommaPos;}return result;}},{key:\"tsParseBracketedList\",value:function tsParseBracketedList(kind,parseElement,bracket,skipFirstToken,refTrailingCommaPos){if(!skipFirstToken){if(bracket){this.expect(0);}else{this.expect(47);}}var result=this.tsParseDelimitedList(kind,parseElement,refTrailingCommaPos);if(bracket){this.expect(3);}else{this.expect(48);}return result;}},{key:\"tsParseImportType\",value:function tsParseImportType(){var node=this.startNode();this.expect(83);this.expect(10);if(!this.match(134)){this.raise(TSErrors.UnsupportedImportTypeArgument,this.state.startLoc);{node.argument=_superPropGet(TypeScriptParserMixin,\"parseExprAtom\",this,3)([]);}}else{{node.argument=this.parseStringLiteral(this.state.value);}}if(this.eat(12)){node.options=this.tsParseImportTypeOptions();}else{node.options=null;}this.expect(11);if(this.eat(16)){node.qualifier=this.tsParseEntityName(1|2);}if(this.match(47)){{node.typeParameters=this.tsParseTypeArguments();}}return this.finishNode(node,\"TSImportType\");}},{key:\"tsParseImportTypeOptions\",value:function tsParseImportTypeOptions(){var node=this.startNode();this.expect(5);var withProperty=this.startNode();if(this.isContextual(76)){withProperty.method=false;withProperty.key=this.parseIdentifier(true);withProperty.computed=false;withProperty.shorthand=false;}else{this.unexpected(null,76);}this.expect(14);withProperty.value=this.tsParseImportTypeWithPropertyValue();node.properties=[this.finishObjectProperty(withProperty)];this.expect(8);return this.finishNode(node,\"ObjectExpression\");}},{key:\"tsParseImportTypeWithPropertyValue\",value:function tsParseImportTypeWithPropertyValue(){var node=this.startNode();var properties=[];this.expect(5);while(!this.match(8)){var type=this.state.type;if(tokenIsIdentifier(type)||type===134){properties.push(_superPropGet(TypeScriptParserMixin,\"parsePropertyDefinition\",this,3)([null]));}else{this.unexpected();}this.eat(12);}node.properties=properties;this.next();return this.finishNode(node,\"ObjectExpression\");}},{key:\"tsParseEntityName\",value:function tsParseEntityName(flags){var entity;if(flags&1&&this.match(78)){if(flags&2){entity=this.parseIdentifier(true);}else{var node=this.startNode();this.next();entity=this.finishNode(node,\"ThisExpression\");}}else{entity=this.parseIdentifier(!!(flags&1));}while(this.eat(16)){var _node8=this.startNodeAtNode(entity);_node8.left=entity;_node8.right=this.parseIdentifier(!!(flags&1));entity=this.finishNode(_node8,\"TSQualifiedName\");}return entity;}},{key:\"tsParseTypeReference\",value:function tsParseTypeReference(){var node=this.startNode();node.typeName=this.tsParseEntityName(1);if(!this.hasPrecedingLineBreak()&&this.match(47)){{node.typeParameters=this.tsParseTypeArguments();}}return this.finishNode(node,\"TSTypeReference\");}},{key:\"tsParseThisTypePredicate\",value:function tsParseThisTypePredicate(lhs){this.next();var node=this.startNodeAtNode(lhs);node.parameterName=lhs;node.typeAnnotation=this.tsParseTypeAnnotation(false);node.asserts=false;return this.finishNode(node,\"TSTypePredicate\");}},{key:\"tsParseThisTypeNode\",value:function tsParseThisTypeNode(){var node=this.startNode();this.next();return this.finishNode(node,\"TSThisType\");}},{key:\"tsParseTypeQuery\",value:function tsParseTypeQuery(){var node=this.startNode();this.expect(87);if(this.match(83)){node.exprName=this.tsParseImportType();}else{{node.exprName=this.tsParseEntityName(1|2);}}if(!this.hasPrecedingLineBreak()&&this.match(47)){{node.typeParameters=this.tsParseTypeArguments();}}return this.finishNode(node,\"TSTypeQuery\");}},{key:\"tsParseTypeParameter\",value:function tsParseTypeParameter(parseModifiers){var node=this.startNode();parseModifiers(node);node.name=this.tsParseTypeParameterName();node.constraint=this.tsEatThenParseType(81);node.default=this.tsEatThenParseType(29);return this.finishNode(node,\"TSTypeParameter\");}},{key:\"tsTryParseTypeParameters\",value:function tsTryParseTypeParameters(parseModifiers){if(this.match(47)){return this.tsParseTypeParameters(parseModifiers);}}},{key:\"tsParseTypeParameters\",value:function tsParseTypeParameters(parseModifiers){var node=this.startNode();if(this.match(47)||this.match(143)){this.next();}else{this.unexpected();}var refTrailingCommaPos={value:-1};node.params=this.tsParseBracketedList(\"TypeParametersOrArguments\",this.tsParseTypeParameter.bind(this,parseModifiers),false,true,refTrailingCommaPos);if(node.params.length===0){this.raise(TSErrors.EmptyTypeParameters,node);}if(refTrailingCommaPos.value!==-1){this.addExtra(node,\"trailingComma\",refTrailingCommaPos.value);}return this.finishNode(node,\"TSTypeParameterDeclaration\");}},{key:\"tsFillSignature\",value:function tsFillSignature(returnToken,signature){var returnTokenRequired=returnToken===19;var paramsKey=\"parameters\";var returnTypeKey=\"typeAnnotation\";signature.typeParameters=this.tsTryParseTypeParameters(this.tsParseConstModifier);this.expect(10);signature[paramsKey]=this.tsParseBindingListForSignature();if(returnTokenRequired){signature[returnTypeKey]=this.tsParseTypeOrTypePredicateAnnotation(returnToken);}else if(this.match(returnToken)){signature[returnTypeKey]=this.tsParseTypeOrTypePredicateAnnotation(returnToken);}}},{key:\"tsParseBindingListForSignature\",value:function tsParseBindingListForSignature(){var list=_superPropGet(TypeScriptParserMixin,\"parseBindingList\",this,3)([11,41,2]);var _iterator8=_createForOfIteratorHelper(list),_step8;try{for(_iterator8.s();!(_step8=_iterator8.n()).done;){var pattern=_step8.value;var type=pattern.type;if(type===\"AssignmentPattern\"||type===\"TSParameterProperty\"){this.raise(TSErrors.UnsupportedSignatureParameterKind,pattern,{type:type});}}}catch(err){_iterator8.e(err);}finally{_iterator8.f();}return list;}},{key:\"tsParseTypeMemberSemicolon\",value:function tsParseTypeMemberSemicolon(){if(!this.eat(12)&&!this.isLineTerminator()){this.expect(13);}}},{key:\"tsParseSignatureMember\",value:function tsParseSignatureMember(kind,node){this.tsFillSignature(14,node);this.tsParseTypeMemberSemicolon();return this.finishNode(node,kind);}},{key:\"tsIsUnambiguouslyIndexSignature\",value:function tsIsUnambiguouslyIndexSignature(){this.next();if(tokenIsIdentifier(this.state.type)){this.next();return this.match(14);}return false;}},{key:\"tsTryParseIndexSignature\",value:function tsTryParseIndexSignature(node){if(!(this.match(0)&&this.tsLookAhead(this.tsIsUnambiguouslyIndexSignature.bind(this)))){return;}this.expect(0);var id=this.parseIdentifier();id.typeAnnotation=this.tsParseTypeAnnotation();this.resetEndLocation(id);this.expect(3);node.parameters=[id];var type=this.tsTryParseTypeAnnotation();if(type)node.typeAnnotation=type;this.tsParseTypeMemberSemicolon();return this.finishNode(node,\"TSIndexSignature\");}},{key:\"tsParsePropertyOrMethodSignature\",value:function tsParsePropertyOrMethodSignature(node,readonly){if(this.eat(17))node.optional=true;if(this.match(10)||this.match(47)){if(readonly){this.raise(TSErrors.ReadonlyForMethodSignature,node);}var method=node;if(method.kind&&this.match(47)){this.raise(TSErrors.AccessorCannotHaveTypeParameters,this.state.curPosition());}this.tsFillSignature(14,method);this.tsParseTypeMemberSemicolon();var paramsKey=\"parameters\";var returnTypeKey=\"typeAnnotation\";if(method.kind===\"get\"){if(method[paramsKey].length>0){this.raise(Errors.BadGetterArity,this.state.curPosition());if(this.isThisParam(method[paramsKey][0])){this.raise(TSErrors.AccessorCannotDeclareThisParameter,this.state.curPosition());}}}else if(method.kind===\"set\"){if(method[paramsKey].length!==1){this.raise(Errors.BadSetterArity,this.state.curPosition());}else{var firstParameter=method[paramsKey][0];if(this.isThisParam(firstParameter)){this.raise(TSErrors.AccessorCannotDeclareThisParameter,this.state.curPosition());}if(firstParameter.type===\"Identifier\"&&firstParameter.optional){this.raise(TSErrors.SetAccessorCannotHaveOptionalParameter,this.state.curPosition());}if(firstParameter.type===\"RestElement\"){this.raise(TSErrors.SetAccessorCannotHaveRestParameter,this.state.curPosition());}}if(method[returnTypeKey]){this.raise(TSErrors.SetAccessorCannotHaveReturnType,method[returnTypeKey]);}}else{method.kind=\"method\";}return this.finishNode(method,\"TSMethodSignature\");}else{var property=node;if(readonly)property.readonly=true;var type=this.tsTryParseTypeAnnotation();if(type)property.typeAnnotation=type;this.tsParseTypeMemberSemicolon();return this.finishNode(property,\"TSPropertySignature\");}}},{key:\"tsParseTypeMember\",value:function tsParseTypeMember(){var node=this.startNode();if(this.match(10)||this.match(47)){return this.tsParseSignatureMember(\"TSCallSignatureDeclaration\",node);}if(this.match(77)){var id=this.startNode();this.next();if(this.match(10)||this.match(47)){return this.tsParseSignatureMember(\"TSConstructSignatureDeclaration\",node);}else{node.key=this.createIdentifier(id,\"new\");return this.tsParsePropertyOrMethodSignature(node,false);}}this.tsParseModifiers({allowedModifiers:[\"readonly\"],disallowedModifiers:[\"declare\",\"abstract\",\"private\",\"protected\",\"public\",\"static\",\"override\"]},node);var idx=this.tsTryParseIndexSignature(node);if(idx){return idx;}_superPropGet(TypeScriptParserMixin,\"parsePropertyName\",this,3)([node]);if(!node.computed&&node.key.type===\"Identifier\"&&(node.key.name===\"get\"||node.key.name===\"set\")&&this.tsTokenCanFollowModifier()){node.kind=node.key.name;_superPropGet(TypeScriptParserMixin,\"parsePropertyName\",this,3)([node]);if(!this.match(10)&&!this.match(47)){this.unexpected(null,10);}}return this.tsParsePropertyOrMethodSignature(node,!!node.readonly);}},{key:\"tsParseTypeLiteral\",value:function tsParseTypeLiteral(){var node=this.startNode();node.members=this.tsParseObjectTypeMembers();return this.finishNode(node,\"TSTypeLiteral\");}},{key:\"tsParseObjectTypeMembers\",value:function tsParseObjectTypeMembers(){this.expect(5);var members=this.tsParseList(\"TypeMembers\",this.tsParseTypeMember.bind(this));this.expect(8);return members;}},{key:\"tsIsStartOfMappedType\",value:function tsIsStartOfMappedType(){this.next();if(this.eat(53)){return this.isContextual(122);}if(this.isContextual(122)){this.next();}if(!this.match(0)){return false;}this.next();if(!this.tsIsIdentifier()){return false;}this.next();return this.match(58);}},{key:\"tsParseMappedType\",value:function tsParseMappedType(){var node=this.startNode();this.expect(5);if(this.match(53)){node.readonly=this.state.value;this.next();this.expectContextual(122);}else if(this.eatContextual(122)){node.readonly=true;}this.expect(0);{var typeParameter=this.startNode();typeParameter.name=this.tsParseTypeParameterName();typeParameter.constraint=this.tsExpectThenParseType(58);node.typeParameter=this.finishNode(typeParameter,\"TSTypeParameter\");}node.nameType=this.eatContextual(93)?this.tsParseType():null;this.expect(3);if(this.match(53)){node.optional=this.state.value;this.next();this.expect(17);}else if(this.eat(17)){node.optional=true;}node.typeAnnotation=this.tsTryParseType();this.semicolon();this.expect(8);return this.finishNode(node,\"TSMappedType\");}},{key:\"tsParseTupleType\",value:function tsParseTupleType(){var _this27=this;var node=this.startNode();node.elementTypes=this.tsParseBracketedList(\"TupleElementTypes\",this.tsParseTupleElementType.bind(this),true,false);var seenOptionalElement=false;node.elementTypes.forEach(function(elementNode){var type=elementNode.type;if(seenOptionalElement&&type!==\"TSRestType\"&&type!==\"TSOptionalType\"&&!(type===\"TSNamedTupleMember\"&&elementNode.optional)){_this27.raise(TSErrors.OptionalTypeBeforeRequired,elementNode);}seenOptionalElement||(seenOptionalElement=type===\"TSNamedTupleMember\"&&elementNode.optional||type===\"TSOptionalType\");});return this.finishNode(node,\"TSTupleType\");}},{key:\"tsParseTupleElementType\",value:function tsParseTupleElementType(){var restStartLoc=this.state.startLoc;var rest=this.eat(21);var startLoc=this.state.startLoc;var labeled;var label;var optional;var type;var isWord=tokenIsKeywordOrIdentifier(this.state.type);var chAfterWord=isWord?this.lookaheadCharCode():null;if(chAfterWord===58){labeled=true;optional=false;label=this.parseIdentifier(true);this.expect(14);type=this.tsParseType();}else if(chAfterWord===63){optional=true;var wordName=this.state.value;var typeOrLabel=this.tsParseNonArrayType();if(this.lookaheadCharCode()===58){labeled=true;label=this.createIdentifier(this.startNodeAt(startLoc),wordName);this.expect(17);this.expect(14);type=this.tsParseType();}else{labeled=false;type=typeOrLabel;this.expect(17);}}else{type=this.tsParseType();optional=this.eat(17);labeled=this.eat(14);}if(labeled){var labeledNode;if(label){labeledNode=this.startNodeAt(startLoc);labeledNode.optional=optional;labeledNode.label=label;labeledNode.elementType=type;if(this.eat(17)){labeledNode.optional=true;this.raise(TSErrors.TupleOptionalAfterType,this.state.lastTokStartLoc);}}else{labeledNode=this.startNodeAt(startLoc);labeledNode.optional=optional;this.raise(TSErrors.InvalidTupleMemberLabel,type);labeledNode.label=type;labeledNode.elementType=this.tsParseType();}type=this.finishNode(labeledNode,\"TSNamedTupleMember\");}else if(optional){var optionalTypeNode=this.startNodeAt(startLoc);optionalTypeNode.typeAnnotation=type;type=this.finishNode(optionalTypeNode,\"TSOptionalType\");}if(rest){var restNode=this.startNodeAt(restStartLoc);restNode.typeAnnotation=type;type=this.finishNode(restNode,\"TSRestType\");}return type;}},{key:\"tsParseParenthesizedType\",value:function tsParseParenthesizedType(){var node=this.startNode();this.expect(10);node.typeAnnotation=this.tsParseType();this.expect(11);return this.finishNode(node,\"TSParenthesizedType\");}},{key:\"tsParseFunctionOrConstructorType\",value:function tsParseFunctionOrConstructorType(type,abstract){var _this28=this;var node=this.startNode();if(type===\"TSConstructorType\"){node.abstract=!!abstract;if(abstract)this.next();this.next();}this.tsInAllowConditionalTypesContext(function(){return _this28.tsFillSignature(19,node);});return this.finishNode(node,type);}},{key:\"tsParseLiteralTypeNode\",value:function tsParseLiteralTypeNode(){var node=this.startNode();switch(this.state.type){case 135:case 136:case 134:case 85:case 86:node.literal=_superPropGet(TypeScriptParserMixin,\"parseExprAtom\",this,3)([]);break;default:this.unexpected();}return this.finishNode(node,\"TSLiteralType\");}},{key:\"tsParseTemplateLiteralType\",value:function tsParseTemplateLiteralType(){{var node=this.startNode();node.literal=_superPropGet(TypeScriptParserMixin,\"parseTemplate\",this,3)([false]);return this.finishNode(node,\"TSLiteralType\");}}},{key:\"parseTemplateSubstitution\",value:function parseTemplateSubstitution(){if(this.state.inType)return this.tsParseType();return _superPropGet(TypeScriptParserMixin,\"parseTemplateSubstitution\",this,3)([]);}},{key:\"tsParseThisTypeOrThisTypePredicate\",value:function tsParseThisTypeOrThisTypePredicate(){var thisKeyword=this.tsParseThisTypeNode();if(this.isContextual(116)&&!this.hasPrecedingLineBreak()){return this.tsParseThisTypePredicate(thisKeyword);}else{return thisKeyword;}}},{key:\"tsParseNonArrayType\",value:function tsParseNonArrayType(){switch(this.state.type){case 134:case 135:case 136:case 85:case 86:return this.tsParseLiteralTypeNode();case 53:if(this.state.value===\"-\"){var node=this.startNode();var nextToken=this.lookahead();if(nextToken.type!==135&&nextToken.type!==136){this.unexpected();}node.literal=this.parseMaybeUnary();return this.finishNode(node,\"TSLiteralType\");}break;case 78:return this.tsParseThisTypeOrThisTypePredicate();case 87:return this.tsParseTypeQuery();case 83:return this.tsParseImportType();case 5:return this.tsLookAhead(this.tsIsStartOfMappedType.bind(this))?this.tsParseMappedType():this.tsParseTypeLiteral();case 0:return this.tsParseTupleType();case 10:return this.tsParseParenthesizedType();case 25:case 24:return this.tsParseTemplateLiteralType();default:{var type=this.state.type;if(tokenIsIdentifier(type)||type===88||type===84){var nodeType=type===88?\"TSVoidKeyword\":type===84?\"TSNullKeyword\":keywordTypeFromName(this.state.value);if(nodeType!==undefined&&this.lookaheadCharCode()!==46){var _node9=this.startNode();this.next();return this.finishNode(_node9,nodeType);}return this.tsParseTypeReference();}}}this.unexpected();}},{key:\"tsParseArrayTypeOrHigher\",value:function tsParseArrayTypeOrHigher(){var startLoc=this.state.startLoc;var type=this.tsParseNonArrayType();while(!this.hasPrecedingLineBreak()&&this.eat(0)){if(this.match(3)){var node=this.startNodeAt(startLoc);node.elementType=type;this.expect(3);type=this.finishNode(node,\"TSArrayType\");}else{var _node0=this.startNodeAt(startLoc);_node0.objectType=type;_node0.indexType=this.tsParseType();this.expect(3);type=this.finishNode(_node0,\"TSIndexedAccessType\");}}return type;}},{key:\"tsParseTypeOperator\",value:function tsParseTypeOperator(){var node=this.startNode();var operator=this.state.value;this.next();node.operator=operator;node.typeAnnotation=this.tsParseTypeOperatorOrHigher();if(operator===\"readonly\"){this.tsCheckTypeAnnotationForReadOnly(node);}return this.finishNode(node,\"TSTypeOperator\");}},{key:\"tsCheckTypeAnnotationForReadOnly\",value:function tsCheckTypeAnnotationForReadOnly(node){switch(node.typeAnnotation.type){case\"TSTupleType\":case\"TSArrayType\":return;default:this.raise(TSErrors.UnexpectedReadonly,node);}}},{key:\"tsParseInferType\",value:function tsParseInferType(){var _this29=this;var node=this.startNode();this.expectContextual(115);var typeParameter=this.startNode();typeParameter.name=this.tsParseTypeParameterName();typeParameter.constraint=this.tsTryParse(function(){return _this29.tsParseConstraintForInferType();});node.typeParameter=this.finishNode(typeParameter,\"TSTypeParameter\");return this.finishNode(node,\"TSInferType\");}},{key:\"tsParseConstraintForInferType\",value:function tsParseConstraintForInferType(){var _this30=this;if(this.eat(81)){var constraint=this.tsInDisallowConditionalTypesContext(function(){return _this30.tsParseType();});if(this.state.inDisallowConditionalTypesContext||!this.match(17)){return constraint;}}}},{key:\"tsParseTypeOperatorOrHigher\",value:function tsParseTypeOperatorOrHigher(){var _this31=this;var isTypeOperator=tokenIsTSTypeOperator(this.state.type)&&!this.state.containsEsc;return isTypeOperator?this.tsParseTypeOperator():this.isContextual(115)?this.tsParseInferType():this.tsInAllowConditionalTypesContext(function(){return _this31.tsParseArrayTypeOrHigher();});}},{key:\"tsParseUnionOrIntersectionType\",value:function tsParseUnionOrIntersectionType(kind,parseConstituentType,operator){var node=this.startNode();var hasLeadingOperator=this.eat(operator);var types=[];do{types.push(parseConstituentType());}while(this.eat(operator));if(types.length===1&&!hasLeadingOperator){return types[0];}node.types=types;return this.finishNode(node,kind);}},{key:\"tsParseIntersectionTypeOrHigher\",value:function tsParseIntersectionTypeOrHigher(){return this.tsParseUnionOrIntersectionType(\"TSIntersectionType\",this.tsParseTypeOperatorOrHigher.bind(this),45);}},{key:\"tsParseUnionTypeOrHigher\",value:function tsParseUnionTypeOrHigher(){return this.tsParseUnionOrIntersectionType(\"TSUnionType\",this.tsParseIntersectionTypeOrHigher.bind(this),43);}},{key:\"tsIsStartOfFunctionType\",value:function tsIsStartOfFunctionType(){if(this.match(47)){return true;}return this.match(10)&&this.tsLookAhead(this.tsIsUnambiguouslyStartOfFunctionType.bind(this));}},{key:\"tsSkipParameterStart\",value:function tsSkipParameterStart(){if(tokenIsIdentifier(this.state.type)||this.match(78)){this.next();return true;}if(this.match(5)){var errors=this.state.errors;var previousErrorCount=errors.length;try{this.parseObjectLike(8,true);return errors.length===previousErrorCount;}catch(_unused){return false;}}if(this.match(0)){this.next();var _errors=this.state.errors;var _previousErrorCount=_errors.length;try{_superPropGet(TypeScriptParserMixin,\"parseBindingList\",this,3)([3,93,1]);return _errors.length===_previousErrorCount;}catch(_unused2){return false;}}return false;}},{key:\"tsIsUnambiguouslyStartOfFunctionType\",value:function tsIsUnambiguouslyStartOfFunctionType(){this.next();if(this.match(11)||this.match(21)){return true;}if(this.tsSkipParameterStart()){if(this.match(14)||this.match(12)||this.match(17)||this.match(29)){return true;}if(this.match(11)){this.next();if(this.match(19)){return true;}}}return false;}},{key:\"tsParseTypeOrTypePredicateAnnotation\",value:function tsParseTypeOrTypePredicateAnnotation(returnToken){var _this32=this;return this.tsInType(function(){var t=_this32.startNode();_this32.expect(returnToken);var node=_this32.startNode();var asserts=!!_this32.tsTryParse(_this32.tsParseTypePredicateAsserts.bind(_this32));if(asserts&&_this32.match(78)){var thisTypePredicate=_this32.tsParseThisTypeOrThisTypePredicate();if(thisTypePredicate.type===\"TSThisType\"){node.parameterName=thisTypePredicate;node.asserts=true;node.typeAnnotation=null;thisTypePredicate=_this32.finishNode(node,\"TSTypePredicate\");}else{_this32.resetStartLocationFromNode(thisTypePredicate,node);thisTypePredicate.asserts=true;}t.typeAnnotation=thisTypePredicate;return _this32.finishNode(t,\"TSTypeAnnotation\");}var typePredicateVariable=_this32.tsIsIdentifier()&&_this32.tsTryParse(_this32.tsParseTypePredicatePrefix.bind(_this32));if(!typePredicateVariable){if(!asserts){return _this32.tsParseTypeAnnotation(false,t);}node.parameterName=_this32.parseIdentifier();node.asserts=asserts;node.typeAnnotation=null;t.typeAnnotation=_this32.finishNode(node,\"TSTypePredicate\");return _this32.finishNode(t,\"TSTypeAnnotation\");}var type=_this32.tsParseTypeAnnotation(false);node.parameterName=typePredicateVariable;node.typeAnnotation=type;node.asserts=asserts;t.typeAnnotation=_this32.finishNode(node,\"TSTypePredicate\");return _this32.finishNode(t,\"TSTypeAnnotation\");});}},{key:\"tsTryParseTypeOrTypePredicateAnnotation\",value:function tsTryParseTypeOrTypePredicateAnnotation(){if(this.match(14)){return this.tsParseTypeOrTypePredicateAnnotation(14);}}},{key:\"tsTryParseTypeAnnotation\",value:function tsTryParseTypeAnnotation(){if(this.match(14)){return this.tsParseTypeAnnotation();}}},{key:\"tsTryParseType\",value:function tsTryParseType(){return this.tsEatThenParseType(14);}},{key:\"tsParseTypePredicatePrefix\",value:function tsParseTypePredicatePrefix(){var id=this.parseIdentifier();if(this.isContextual(116)&&!this.hasPrecedingLineBreak()){this.next();return id;}}},{key:\"tsParseTypePredicateAsserts\",value:function tsParseTypePredicateAsserts(){if(this.state.type!==109){return false;}var containsEsc=this.state.containsEsc;this.next();if(!tokenIsIdentifier(this.state.type)&&!this.match(78)){return false;}if(containsEsc){this.raise(Errors.InvalidEscapedReservedWord,this.state.lastTokStartLoc,{reservedWord:\"asserts\"});}return true;}},{key:\"tsParseTypeAnnotation\",value:function tsParseTypeAnnotation(){var _this33=this;var eatColon=arguments.length>0&&arguments[0]!==undefined?arguments[0]:true;var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:this.startNode();this.tsInType(function(){if(eatColon)_this33.expect(14);t.typeAnnotation=_this33.tsParseType();});return this.finishNode(t,\"TSTypeAnnotation\");}},{key:\"tsParseType\",value:function tsParseType(){var _this34=this;assert(this.state.inType);var type=this.tsParseNonConditionalType();if(this.state.inDisallowConditionalTypesContext||this.hasPrecedingLineBreak()||!this.eat(81)){return type;}var node=this.startNodeAtNode(type);node.checkType=type;node.extendsType=this.tsInDisallowConditionalTypesContext(function(){return _this34.tsParseNonConditionalType();});this.expect(17);node.trueType=this.tsInAllowConditionalTypesContext(function(){return _this34.tsParseType();});this.expect(14);node.falseType=this.tsInAllowConditionalTypesContext(function(){return _this34.tsParseType();});return this.finishNode(node,\"TSConditionalType\");}},{key:\"isAbstractConstructorSignature\",value:function isAbstractConstructorSignature(){return this.isContextual(124)&&this.isLookaheadContextual(\"new\");}},{key:\"tsParseNonConditionalType\",value:function tsParseNonConditionalType(){if(this.tsIsStartOfFunctionType()){return this.tsParseFunctionOrConstructorType(\"TSFunctionType\");}if(this.match(77)){return this.tsParseFunctionOrConstructorType(\"TSConstructorType\");}else if(this.isAbstractConstructorSignature()){return this.tsParseFunctionOrConstructorType(\"TSConstructorType\",true);}return this.tsParseUnionTypeOrHigher();}},{key:\"tsParseTypeAssertion\",value:function tsParseTypeAssertion(){var _this35=this;if(this.getPluginOption(\"typescript\",\"disallowAmbiguousJSXLike\")){this.raise(TSErrors.ReservedTypeAssertion,this.state.startLoc);}var node=this.startNode();node.typeAnnotation=this.tsInType(function(){_this35.next();return _this35.match(75)?_this35.tsParseTypeReference():_this35.tsParseType();});this.expect(48);node.expression=this.parseMaybeUnary();return this.finishNode(node,\"TSTypeAssertion\");}},{key:\"tsParseHeritageClause\",value:function tsParseHeritageClause(token){var _this36=this;var originalStartLoc=this.state.startLoc;var delimitedList=this.tsParseDelimitedList(\"HeritageClauseElement\",function(){{var node=_this36.startNode();node.expression=_this36.tsParseEntityName(1|2);if(_this36.match(47)){node.typeParameters=_this36.tsParseTypeArguments();}return _this36.finishNode(node,\"TSExpressionWithTypeArguments\");}});if(!delimitedList.length){this.raise(TSErrors.EmptyHeritageClauseType,originalStartLoc,{token:token});}return delimitedList;}},{key:\"tsParseInterfaceDeclaration\",value:function tsParseInterfaceDeclaration(node){var properties=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};if(this.hasFollowingLineBreak())return null;this.expectContextual(129);if(properties.declare)node.declare=true;if(tokenIsIdentifier(this.state.type)){node.id=this.parseIdentifier();this.checkIdentifier(node.id,130);}else{node.id=null;this.raise(TSErrors.MissingInterfaceName,this.state.startLoc);}node.typeParameters=this.tsTryParseTypeParameters(this.tsParseInOutConstModifiers);if(this.eat(81)){node.extends=this.tsParseHeritageClause(\"extends\");}var body=this.startNode();body.body=this.tsInType(this.tsParseObjectTypeMembers.bind(this));node.body=this.finishNode(body,\"TSInterfaceBody\");return this.finishNode(node,\"TSInterfaceDeclaration\");}},{key:\"tsParseTypeAliasDeclaration\",value:function tsParseTypeAliasDeclaration(node){var _this37=this;node.id=this.parseIdentifier();this.checkIdentifier(node.id,2);node.typeAnnotation=this.tsInType(function(){node.typeParameters=_this37.tsTryParseTypeParameters(_this37.tsParseInOutModifiers);_this37.expect(29);if(_this37.isContextual(114)&&_this37.lookaheadCharCode()!==46){var _node1=_this37.startNode();_this37.next();return _this37.finishNode(_node1,\"TSIntrinsicKeyword\");}return _this37.tsParseType();});this.semicolon();return this.finishNode(node,\"TSTypeAliasDeclaration\");}},{key:\"tsInTopLevelContext\",value:function tsInTopLevelContext(cb){if(this.curContext()!==types.brace){var oldContext=this.state.context;this.state.context=[oldContext[0]];try{return cb();}finally{this.state.context=oldContext;}}else{return cb();}}},{key:\"tsInType\",value:function tsInType(cb){var oldInType=this.state.inType;this.state.inType=true;try{return cb();}finally{this.state.inType=oldInType;}}},{key:\"tsInDisallowConditionalTypesContext\",value:function tsInDisallowConditionalTypesContext(cb){var oldInDisallowConditionalTypesContext=this.state.inDisallowConditionalTypesContext;this.state.inDisallowConditionalTypesContext=true;try{return cb();}finally{this.state.inDisallowConditionalTypesContext=oldInDisallowConditionalTypesContext;}}},{key:\"tsInAllowConditionalTypesContext\",value:function tsInAllowConditionalTypesContext(cb){var oldInDisallowConditionalTypesContext=this.state.inDisallowConditionalTypesContext;this.state.inDisallowConditionalTypesContext=false;try{return cb();}finally{this.state.inDisallowConditionalTypesContext=oldInDisallowConditionalTypesContext;}}},{key:\"tsEatThenParseType\",value:function tsEatThenParseType(token){if(this.match(token)){return this.tsNextThenParseType();}}},{key:\"tsExpectThenParseType\",value:function tsExpectThenParseType(token){var _this38=this;return this.tsInType(function(){_this38.expect(token);return _this38.tsParseType();});}},{key:\"tsNextThenParseType\",value:function tsNextThenParseType(){var _this39=this;return this.tsInType(function(){_this39.next();return _this39.tsParseType();});}},{key:\"tsParseEnumMember\",value:function tsParseEnumMember(){var node=this.startNode();node.id=this.match(134)?_superPropGet(TypeScriptParserMixin,\"parseStringLiteral\",this,3)([this.state.value]):this.parseIdentifier(true);if(this.eat(29)){node.initializer=_superPropGet(TypeScriptParserMixin,\"parseMaybeAssignAllowIn\",this,3)([]);}return this.finishNode(node,\"TSEnumMember\");}},{key:\"tsParseEnumDeclaration\",value:function tsParseEnumDeclaration(node){var properties=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};if(properties.const)node.const=true;if(properties.declare)node.declare=true;this.expectContextual(126);node.id=this.parseIdentifier();this.checkIdentifier(node.id,node.const?8971:8459);{this.expect(5);node.members=this.tsParseDelimitedList(\"EnumMembers\",this.tsParseEnumMember.bind(this));this.expect(8);}return this.finishNode(node,\"TSEnumDeclaration\");}},{key:\"tsParseEnumBody\",value:function tsParseEnumBody(){var node=this.startNode();this.expect(5);node.members=this.tsParseDelimitedList(\"EnumMembers\",this.tsParseEnumMember.bind(this));this.expect(8);return this.finishNode(node,\"TSEnumBody\");}},{key:\"tsParseModuleBlock\",value:function tsParseModuleBlock(){var node=this.startNode();this.scope.enter(0);this.expect(5);_superPropGet(TypeScriptParserMixin,\"parseBlockOrModuleBlockBody\",this,3)([node.body=[],undefined,true,8]);this.scope.exit();return this.finishNode(node,\"TSModuleBlock\");}},{key:\"tsParseModuleOrNamespaceDeclaration\",value:function tsParseModuleOrNamespaceDeclaration(node){var nested=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;node.id=this.parseIdentifier();if(!nested){this.checkIdentifier(node.id,1024);}if(this.eat(16)){var inner=this.startNode();this.tsParseModuleOrNamespaceDeclaration(inner,true);node.body=inner;}else{this.scope.enter(1024);this.prodParam.enter(0);node.body=this.tsParseModuleBlock();this.prodParam.exit();this.scope.exit();}return this.finishNode(node,\"TSModuleDeclaration\");}},{key:\"tsParseAmbientExternalModuleDeclaration\",value:function tsParseAmbientExternalModuleDeclaration(node){if(this.isContextual(112)){node.kind=\"global\";{node.global=true;}node.id=this.parseIdentifier();}else if(this.match(134)){node.kind=\"module\";node.id=_superPropGet(TypeScriptParserMixin,\"parseStringLiteral\",this,3)([this.state.value]);}else{this.unexpected();}if(this.match(5)){this.scope.enter(1024);this.prodParam.enter(0);node.body=this.tsParseModuleBlock();this.prodParam.exit();this.scope.exit();}else{this.semicolon();}return this.finishNode(node,\"TSModuleDeclaration\");}},{key:\"tsParseImportEqualsDeclaration\",value:function tsParseImportEqualsDeclaration(node,maybeDefaultIdentifier,isExport){{node.isExport=isExport||false;}node.id=maybeDefaultIdentifier||this.parseIdentifier();this.checkIdentifier(node.id,4096);this.expect(29);var moduleReference=this.tsParseModuleReference();if(node.importKind===\"type\"&&moduleReference.type!==\"TSExternalModuleReference\"){this.raise(TSErrors.ImportAliasHasImportType,moduleReference);}node.moduleReference=moduleReference;this.semicolon();return this.finishNode(node,\"TSImportEqualsDeclaration\");}},{key:\"tsIsExternalModuleReference\",value:function tsIsExternalModuleReference(){return this.isContextual(119)&&this.lookaheadCharCode()===40;}},{key:\"tsParseModuleReference\",value:function tsParseModuleReference(){return this.tsIsExternalModuleReference()?this.tsParseExternalModuleReference():this.tsParseEntityName(0);}},{key:\"tsParseExternalModuleReference\",value:function tsParseExternalModuleReference(){var node=this.startNode();this.expectContextual(119);this.expect(10);if(!this.match(134)){this.unexpected();}node.expression=_superPropGet(TypeScriptParserMixin,\"parseExprAtom\",this,3)([]);this.expect(11);this.sawUnambiguousESM=true;return this.finishNode(node,\"TSExternalModuleReference\");}},{key:\"tsLookAhead\",value:function tsLookAhead(f){var state=this.state.clone();var res=f();this.state=state;return res;}},{key:\"tsTryParseAndCatch\",value:function tsTryParseAndCatch(f){var result=this.tryParse(function(abort){return f()||abort();});if(result.aborted||!result.node)return;if(result.error)this.state=result.failState;return result.node;}},{key:\"tsTryParse\",value:function tsTryParse(f){var state=this.state.clone();var result=f();if(result!==undefined&&result!==false){return result;}this.state=state;}},{key:\"tsTryParseDeclare\",value:function tsTryParseDeclare(node){var _this40=this;if(this.isLineTerminator()){return;}var startType=this.state.type;return this.tsInAmbientContext(function(){switch(startType){case 68:node.declare=true;return _superPropGet(TypeScriptParserMixin,\"parseFunctionStatement\",_this40,3)([node,false,false]);case 80:node.declare=true;return _this40.parseClass(node,true,false);case 126:return _this40.tsParseEnumDeclaration(node,{declare:true});case 112:return _this40.tsParseAmbientExternalModuleDeclaration(node);case 100:if(_this40.state.containsEsc){return;}case 75:case 74:if(!_this40.match(75)||!_this40.isLookaheadContextual(\"enum\")){node.declare=true;return _this40.parseVarStatement(node,_this40.state.value,true);}_this40.expect(75);return _this40.tsParseEnumDeclaration(node,{const:true,declare:true});case 107:if(_this40.isUsing()){_this40.raise(TSErrors.InvalidModifierOnUsingDeclaration,_this40.state.startLoc,\"declare\");node.declare=true;return _this40.parseVarStatement(node,\"using\",true);}break;case 96:if(_this40.isAwaitUsing()){_this40.raise(TSErrors.InvalidModifierOnAwaitUsingDeclaration,_this40.state.startLoc,\"declare\");node.declare=true;_this40.next();return _this40.parseVarStatement(node,\"await using\",true);}break;case 129:{var result=_this40.tsParseInterfaceDeclaration(node,{declare:true});if(result)return result;}default:if(tokenIsIdentifier(startType)){return _this40.tsParseDeclaration(node,_this40.state.value,true,null);}}});}},{key:\"tsTryParseExportDeclaration\",value:function tsTryParseExportDeclaration(){return this.tsParseDeclaration(this.startNode(),this.state.value,true,null);}},{key:\"tsParseExpressionStatement\",value:function tsParseExpressionStatement(node,expr,decorators){switch(expr.name){case\"declare\":{var declaration=this.tsTryParseDeclare(node);if(declaration){declaration.declare=true;}return declaration;}case\"global\":if(this.match(5)){this.scope.enter(1024);this.prodParam.enter(0);var mod=node;mod.kind=\"global\";{node.global=true;}mod.id=expr;mod.body=this.tsParseModuleBlock();this.scope.exit();this.prodParam.exit();return this.finishNode(mod,\"TSModuleDeclaration\");}break;default:return this.tsParseDeclaration(node,expr.name,false,decorators);}}},{key:\"tsParseDeclaration\",value:function tsParseDeclaration(node,value,next,decorators){switch(value){case\"abstract\":if(this.tsCheckLineTerminator(next)&&(this.match(80)||tokenIsIdentifier(this.state.type))){return this.tsParseAbstractDeclaration(node,decorators);}break;case\"module\":if(this.tsCheckLineTerminator(next)){if(this.match(134)){return this.tsParseAmbientExternalModuleDeclaration(node);}else if(tokenIsIdentifier(this.state.type)){node.kind=\"module\";return this.tsParseModuleOrNamespaceDeclaration(node);}}break;case\"namespace\":if(this.tsCheckLineTerminator(next)&&tokenIsIdentifier(this.state.type)){node.kind=\"namespace\";return this.tsParseModuleOrNamespaceDeclaration(node);}break;case\"type\":if(this.tsCheckLineTerminator(next)&&tokenIsIdentifier(this.state.type)){return this.tsParseTypeAliasDeclaration(node);}break;}}},{key:\"tsCheckLineTerminator\",value:function tsCheckLineTerminator(next){if(next){if(this.hasFollowingLineBreak())return false;this.next();return true;}return!this.isLineTerminator();}},{key:\"tsTryParseGenericAsyncArrowFunction\",value:function tsTryParseGenericAsyncArrowFunction(startLoc){var _this41=this;if(!this.match(47))return;var oldMaybeInArrowParameters=this.state.maybeInArrowParameters;this.state.maybeInArrowParameters=true;var res=this.tsTryParseAndCatch(function(){var node=_this41.startNodeAt(startLoc);node.typeParameters=_this41.tsParseTypeParameters(_this41.tsParseConstModifier);_superPropGet(TypeScriptParserMixin,\"parseFunctionParams\",_this41,3)([node]);node.returnType=_this41.tsTryParseTypeOrTypePredicateAnnotation();_this41.expect(19);return node;});this.state.maybeInArrowParameters=oldMaybeInArrowParameters;if(!res)return;return _superPropGet(TypeScriptParserMixin,\"parseArrowExpression\",this,3)([res,null,true]);}},{key:\"tsParseTypeArgumentsInExpression\",value:function tsParseTypeArgumentsInExpression(){if(this.reScan_lt()!==47)return;return this.tsParseTypeArguments();}},{key:\"tsParseTypeArguments\",value:function tsParseTypeArguments(){var _this42=this;var node=this.startNode();node.params=this.tsInType(function(){return _this42.tsInTopLevelContext(function(){_this42.expect(47);return _this42.tsParseDelimitedList(\"TypeParametersOrArguments\",_this42.tsParseType.bind(_this42));});});if(node.params.length===0){this.raise(TSErrors.EmptyTypeArguments,node);}else if(!this.state.inType&&this.curContext()===types.brace){this.reScan_lt_gt();}this.expect(48);return this.finishNode(node,\"TSTypeParameterInstantiation\");}},{key:\"tsIsDeclarationStart\",value:function tsIsDeclarationStart(){return tokenIsTSDeclarationStart(this.state.type);}},{key:\"isExportDefaultSpecifier\",value:function isExportDefaultSpecifier(){if(this.tsIsDeclarationStart())return false;return _superPropGet(TypeScriptParserMixin,\"isExportDefaultSpecifier\",this,3)([]);}},{key:\"parseBindingElement\",value:function parseBindingElement(flags,decorators){var startLoc=decorators.length?decorators[0].loc.start:this.state.startLoc;var modified={};this.tsParseModifiers({allowedModifiers:[\"public\",\"private\",\"protected\",\"override\",\"readonly\"]},modified);var accessibility=modified.accessibility;var override=modified.override;var readonly=modified.readonly;if(!(flags&4)&&(accessibility||readonly||override)){this.raise(TSErrors.UnexpectedParameterModifier,startLoc);}var left=this.parseMaybeDefault();if(flags&2){this.parseFunctionParamType(left);}var elt=this.parseMaybeDefault(left.loc.start,left);if(accessibility||readonly||override){var pp=this.startNodeAt(startLoc);if(decorators.length){pp.decorators=decorators;}if(accessibility)pp.accessibility=accessibility;if(readonly)pp.readonly=readonly;if(override)pp.override=override;if(elt.type!==\"Identifier\"&&elt.type!==\"AssignmentPattern\"){this.raise(TSErrors.UnsupportedParameterPropertyKind,pp);}pp.parameter=elt;return this.finishNode(pp,\"TSParameterProperty\");}if(decorators.length){left.decorators=decorators;}return elt;}},{key:\"isSimpleParameter\",value:function isSimpleParameter(node){return node.type===\"TSParameterProperty\"&&_superPropGet(TypeScriptParserMixin,\"isSimpleParameter\",this,3)([node.parameter])||_superPropGet(TypeScriptParserMixin,\"isSimpleParameter\",this,3)([node]);}},{key:\"tsDisallowOptionalPattern\",value:function tsDisallowOptionalPattern(node){var _iterator9=_createForOfIteratorHelper(node.params),_step9;try{for(_iterator9.s();!(_step9=_iterator9.n()).done;){var param=_step9.value;if(param.type!==\"Identifier\"&&param.optional&&!this.state.isAmbientContext){this.raise(TSErrors.PatternIsOptional,param);}}}catch(err){_iterator9.e(err);}finally{_iterator9.f();}}},{key:\"setArrowFunctionParameters\",value:function setArrowFunctionParameters(node,params,trailingCommaLoc){_superPropGet(TypeScriptParserMixin,\"setArrowFunctionParameters\",this,3)([node,params,trailingCommaLoc]);this.tsDisallowOptionalPattern(node);}},{key:\"parseFunctionBodyAndFinish\",value:function parseFunctionBodyAndFinish(node,type){var isMethod=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;if(this.match(14)){node.returnType=this.tsParseTypeOrTypePredicateAnnotation(14);}var bodilessType=type===\"FunctionDeclaration\"?\"TSDeclareFunction\":type===\"ClassMethod\"||type===\"ClassPrivateMethod\"?\"TSDeclareMethod\":undefined;if(bodilessType&&!this.match(5)&&this.isLineTerminator()){return this.finishNode(node,bodilessType);}if(bodilessType===\"TSDeclareFunction\"&&this.state.isAmbientContext){this.raise(TSErrors.DeclareFunctionHasImplementation,node);if(node.declare){return _superPropGet(TypeScriptParserMixin,\"parseFunctionBodyAndFinish\",this,3)([node,bodilessType,isMethod]);}}this.tsDisallowOptionalPattern(node);return _superPropGet(TypeScriptParserMixin,\"parseFunctionBodyAndFinish\",this,3)([node,type,isMethod]);}},{key:\"registerFunctionStatementId\",value:function registerFunctionStatementId(node){if(!node.body&&node.id){this.checkIdentifier(node.id,1024);}else{_superPropGet(TypeScriptParserMixin,\"registerFunctionStatementId\",this,3)([node]);}}},{key:\"tsCheckForInvalidTypeCasts\",value:function tsCheckForInvalidTypeCasts(items){var _this43=this;items.forEach(function(node){if((node==null?void 0:node.type)===\"TSTypeCastExpression\"){_this43.raise(TSErrors.UnexpectedTypeAnnotation,node.typeAnnotation);}});}},{key:\"toReferencedList\",value:function toReferencedList(exprList,isInParens){this.tsCheckForInvalidTypeCasts(exprList);return exprList;}},{key:\"parseArrayLike\",value:function parseArrayLike(close,canBePattern,isTuple,refExpressionErrors){var node=_superPropGet(TypeScriptParserMixin,\"parseArrayLike\",this,3)([close,canBePattern,isTuple,refExpressionErrors]);if(node.type===\"ArrayExpression\"){this.tsCheckForInvalidTypeCasts(node.elements);}return node;}},{key:\"parseSubscript\",value:function parseSubscript(base,startLoc,noCalls,state){var _this44=this;if(!this.hasPrecedingLineBreak()&&this.match(35)){this.state.canStartJSXElement=false;this.next();var nonNullExpression=this.startNodeAt(startLoc);nonNullExpression.expression=base;return this.finishNode(nonNullExpression,\"TSNonNullExpression\");}var isOptionalCall=false;if(this.match(18)&&this.lookaheadCharCode()===60){if(noCalls){state.stop=true;return base;}state.optionalChainMember=isOptionalCall=true;this.next();}if(this.match(47)||this.match(51)){var missingParenErrorLoc;var result=this.tsTryParseAndCatch(function(){if(!noCalls&&_this44.atPossibleAsyncArrow(base)){var asyncArrowFn=_this44.tsTryParseGenericAsyncArrowFunction(startLoc);if(asyncArrowFn){return asyncArrowFn;}}var typeArguments=_this44.tsParseTypeArgumentsInExpression();if(!typeArguments)return;if(isOptionalCall&&!_this44.match(10)){missingParenErrorLoc=_this44.state.curPosition();return;}if(tokenIsTemplate(_this44.state.type)){var _result=_superPropGet(TypeScriptParserMixin,\"parseTaggedTemplateExpression\",_this44,3)([base,startLoc,state]);{_result.typeParameters=typeArguments;}return _result;}if(!noCalls&&_this44.eat(10)){var _node10=_this44.startNodeAt(startLoc);_node10.callee=base;_node10.arguments=_this44.parseCallExpressionArguments();_this44.tsCheckForInvalidTypeCasts(_node10.arguments);{_node10.typeParameters=typeArguments;}if(state.optionalChainMember){_node10.optional=isOptionalCall;}return _this44.finishCallExpression(_node10,state.optionalChainMember);}var tokenType=_this44.state.type;if(tokenType===48||tokenType===52||tokenType!==10&&tokenCanStartExpression(tokenType)&&!_this44.hasPrecedingLineBreak()){return;}var node=_this44.startNodeAt(startLoc);node.expression=base;{node.typeParameters=typeArguments;}return _this44.finishNode(node,\"TSInstantiationExpression\");});if(missingParenErrorLoc){this.unexpected(missingParenErrorLoc,10);}if(result){if(result.type===\"TSInstantiationExpression\"){if(this.match(16)||this.match(18)&&this.lookaheadCharCode()!==40){this.raise(TSErrors.InvalidPropertyAccessAfterInstantiationExpression,this.state.startLoc);}if(!this.match(16)&&!this.match(18)){result.expression=_superPropGet(TypeScriptParserMixin,\"stopParseSubscript\",this,3)([base,state]);}}return result;}}return _superPropGet(TypeScriptParserMixin,\"parseSubscript\",this,3)([base,startLoc,noCalls,state]);}},{key:\"parseNewCallee\",value:function parseNewCallee(node){var _callee$extra;_superPropGet(TypeScriptParserMixin,\"parseNewCallee\",this,3)([node]);var callee=node.callee;if(callee.type===\"TSInstantiationExpression\"&&!((_callee$extra=callee.extra)!=null&&_callee$extra.parenthesized)){{node.typeParameters=callee.typeParameters;}node.callee=callee.expression;}}},{key:\"parseExprOp\",value:function parseExprOp(left,leftStartLoc,minPrec){var _this45=this;var isSatisfies;if(tokenOperatorPrecedence(58)>minPrec&&!this.hasPrecedingLineBreak()&&(this.isContextual(93)||(isSatisfies=this.isContextual(120)))){var node=this.startNodeAt(leftStartLoc);node.expression=left;node.typeAnnotation=this.tsInType(function(){_this45.next();if(_this45.match(75)){if(isSatisfies){_this45.raise(Errors.UnexpectedKeyword,_this45.state.startLoc,{keyword:\"const\"});}return _this45.tsParseTypeReference();}return _this45.tsParseType();});this.finishNode(node,isSatisfies?\"TSSatisfiesExpression\":\"TSAsExpression\");this.reScan_lt_gt();return this.parseExprOp(node,leftStartLoc,minPrec);}return _superPropGet(TypeScriptParserMixin,\"parseExprOp\",this,3)([left,leftStartLoc,minPrec]);}},{key:\"checkReservedWord\",value:function checkReservedWord(word,startLoc,checkKeywords,isBinding){if(!this.state.isAmbientContext){_superPropGet(TypeScriptParserMixin,\"checkReservedWord\",this,3)([word,startLoc,checkKeywords,isBinding]);}}},{key:\"checkImportReflection\",value:function checkImportReflection(node){_superPropGet(TypeScriptParserMixin,\"checkImportReflection\",this,3)([node]);if(node.module&&node.importKind!==\"value\"){this.raise(TSErrors.ImportReflectionHasImportType,node.specifiers[0].loc.start);}}},{key:\"checkDuplicateExports\",value:function checkDuplicateExports(){}},{key:\"isPotentialImportPhase\",value:function isPotentialImportPhase(isExport){if(_superPropGet(TypeScriptParserMixin,\"isPotentialImportPhase\",this,3)([isExport]))return true;if(this.isContextual(130)){var ch=this.lookaheadCharCode();return isExport?ch===123||ch===42:ch!==61;}return!isExport&&this.isContextual(87);}},{key:\"applyImportPhase\",value:function applyImportPhase(node,isExport,phase,loc){_superPropGet(TypeScriptParserMixin,\"applyImportPhase\",this,3)([node,isExport,phase,loc]);if(isExport){node.exportKind=phase===\"type\"?\"type\":\"value\";}else{node.importKind=phase===\"type\"||phase===\"typeof\"?phase:\"value\";}}},{key:\"parseImport\",value:function parseImport(node){if(this.match(134)){node.importKind=\"value\";return _superPropGet(TypeScriptParserMixin,\"parseImport\",this,3)([node]);}var importNode;if(tokenIsIdentifier(this.state.type)&&this.lookaheadCharCode()===61){node.importKind=\"value\";return this.tsParseImportEqualsDeclaration(node);}else if(this.isContextual(130)){var maybeDefaultIdentifier=this.parseMaybeImportPhase(node,false);if(this.lookaheadCharCode()===61){return this.tsParseImportEqualsDeclaration(node,maybeDefaultIdentifier);}else{importNode=_superPropGet(TypeScriptParserMixin,\"parseImportSpecifiersAndAfter\",this,3)([node,maybeDefaultIdentifier]);}}else{importNode=_superPropGet(TypeScriptParserMixin,\"parseImport\",this,3)([node]);}if(importNode.importKind===\"type\"&&importNode.specifiers.length>1&&importNode.specifiers[0].type===\"ImportDefaultSpecifier\"){this.raise(TSErrors.TypeImportCannotSpecifyDefaultAndNamed,importNode);}return importNode;}},{key:\"parseExport\",value:function parseExport(node,decorators){if(this.match(83)){var nodeImportEquals=node;this.next();var maybeDefaultIdentifier=null;if(this.isContextual(130)&&this.isPotentialImportPhase(false)){maybeDefaultIdentifier=this.parseMaybeImportPhase(nodeImportEquals,false);}else{nodeImportEquals.importKind=\"value\";}var declaration=this.tsParseImportEqualsDeclaration(nodeImportEquals,maybeDefaultIdentifier,true);{return declaration;}}else if(this.eat(29)){var assign=node;assign.expression=_superPropGet(TypeScriptParserMixin,\"parseExpression\",this,3)([]);this.semicolon();this.sawUnambiguousESM=true;return this.finishNode(assign,\"TSExportAssignment\");}else if(this.eatContextual(93)){var decl=node;this.expectContextual(128);decl.id=this.parseIdentifier();this.semicolon();return this.finishNode(decl,\"TSNamespaceExportDeclaration\");}else{return _superPropGet(TypeScriptParserMixin,\"parseExport\",this,3)([node,decorators]);}}},{key:\"isAbstractClass\",value:function isAbstractClass(){return this.isContextual(124)&&this.isLookaheadContextual(\"class\");}},{key:\"parseExportDefaultExpression\",value:function parseExportDefaultExpression(){if(this.isAbstractClass()){var cls=this.startNode();this.next();cls.abstract=true;return this.parseClass(cls,true,true);}if(this.match(129)){var result=this.tsParseInterfaceDeclaration(this.startNode());if(result)return result;}return _superPropGet(TypeScriptParserMixin,\"parseExportDefaultExpression\",this,3)([]);}},{key:\"parseVarStatement\",value:function parseVarStatement(node,kind){var allowMissingInitializer=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;var isAmbientContext=this.state.isAmbientContext;var declaration=_superPropGet(TypeScriptParserMixin,\"parseVarStatement\",this,3)([node,kind,allowMissingInitializer||isAmbientContext]);if(!isAmbientContext)return declaration;if(!node.declare&&(kind===\"using\"||kind===\"await using\")){this.raiseOverwrite(TSErrors.UsingDeclarationInAmbientContext,node,kind);return declaration;}var _iterator0=_createForOfIteratorHelper(declaration.declarations),_step0;try{for(_iterator0.s();!(_step0=_iterator0.n()).done;){var _step0$value=_step0.value,id=_step0$value.id,init=_step0$value.init;if(!init)continue;if(kind===\"var\"||kind===\"let\"||!!id.typeAnnotation){this.raise(TSErrors.InitializerNotAllowedInAmbientContext,init);}else if(!isValidAmbientConstInitializer(init,this.hasPlugin(\"estree\"))){this.raise(TSErrors.ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference,init);}}}catch(err){_iterator0.e(err);}finally{_iterator0.f();}return declaration;}},{key:\"parseStatementContent\",value:function parseStatementContent(flags,decorators){if(this.match(75)&&this.isLookaheadContextual(\"enum\")){var node=this.startNode();this.expect(75);return this.tsParseEnumDeclaration(node,{const:true});}if(this.isContextual(126)){return this.tsParseEnumDeclaration(this.startNode());}if(this.isContextual(129)){var result=this.tsParseInterfaceDeclaration(this.startNode());if(result)return result;}return _superPropGet(TypeScriptParserMixin,\"parseStatementContent\",this,3)([flags,decorators]);}},{key:\"parseAccessModifier\",value:function parseAccessModifier(){return this.tsParseModifier([\"public\",\"protected\",\"private\"]);}},{key:\"tsHasSomeModifiers\",value:function tsHasSomeModifiers(member,modifiers){return modifiers.some(function(modifier){if(tsIsAccessModifier(modifier)){return member.accessibility===modifier;}return!!member[modifier];});}},{key:\"tsIsStartOfStaticBlocks\",value:function tsIsStartOfStaticBlocks(){return this.isContextual(106)&&this.lookaheadCharCode()===123;}},{key:\"parseClassMember\",value:function parseClassMember(classBody,member,state){var _this46=this;var modifiers=[\"declare\",\"private\",\"public\",\"protected\",\"override\",\"abstract\",\"readonly\",\"static\"];this.tsParseModifiers({allowedModifiers:modifiers,disallowedModifiers:[\"in\",\"out\"],stopOnStartOfClassStaticBlock:true,errorTemplate:TSErrors.InvalidModifierOnTypeParameterPositions},member);var callParseClassMemberWithIsStatic=function callParseClassMemberWithIsStatic(){if(_this46.tsIsStartOfStaticBlocks()){_this46.next();_this46.next();if(_this46.tsHasSomeModifiers(member,modifiers)){_this46.raise(TSErrors.StaticBlockCannotHaveModifier,_this46.state.curPosition());}_superPropGet(TypeScriptParserMixin,\"parseClassStaticBlock\",_this46,3)([classBody,member]);}else{_this46.parseClassMemberWithIsStatic(classBody,member,state,!!member.static);}};if(member.declare){this.tsInAmbientContext(callParseClassMemberWithIsStatic);}else{callParseClassMemberWithIsStatic();}}},{key:\"parseClassMemberWithIsStatic\",value:function parseClassMemberWithIsStatic(classBody,member,state,isStatic){var idx=this.tsTryParseIndexSignature(member);if(idx){classBody.body.push(idx);if(member.abstract){this.raise(TSErrors.IndexSignatureHasAbstract,member);}if(member.accessibility){this.raise(TSErrors.IndexSignatureHasAccessibility,member,{modifier:member.accessibility});}if(member.declare){this.raise(TSErrors.IndexSignatureHasDeclare,member);}if(member.override){this.raise(TSErrors.IndexSignatureHasOverride,member);}return;}if(!this.state.inAbstractClass&&member.abstract){this.raise(TSErrors.NonAbstractClassHasAbstractMethod,member);}if(member.override){if(!state.hadSuperClass){this.raise(TSErrors.OverrideNotInSubClass,member);}}_superPropGet(TypeScriptParserMixin,\"parseClassMemberWithIsStatic\",this,3)([classBody,member,state,isStatic]);}},{key:\"parsePostMemberNameModifiers\",value:function parsePostMemberNameModifiers(methodOrProp){var optional=this.eat(17);if(optional)methodOrProp.optional=true;if(methodOrProp.readonly&&this.match(10)){this.raise(TSErrors.ClassMethodHasReadonly,methodOrProp);}if(methodOrProp.declare&&this.match(10)){this.raise(TSErrors.ClassMethodHasDeclare,methodOrProp);}}},{key:\"parseExpressionStatement\",value:function parseExpressionStatement(node,expr,decorators){var decl=expr.type===\"Identifier\"?this.tsParseExpressionStatement(node,expr,decorators):undefined;return decl||_superPropGet(TypeScriptParserMixin,\"parseExpressionStatement\",this,3)([node,expr,decorators]);}},{key:\"shouldParseExportDeclaration\",value:function shouldParseExportDeclaration(){if(this.tsIsDeclarationStart())return true;return _superPropGet(TypeScriptParserMixin,\"shouldParseExportDeclaration\",this,3)([]);}},{key:\"parseConditional\",value:function parseConditional(expr,startLoc,refExpressionErrors){if(!this.match(17))return expr;if(this.state.maybeInArrowParameters){var nextCh=this.lookaheadCharCode();if(nextCh===44||nextCh===61||nextCh===58||nextCh===41){this.setOptionalParametersError(refExpressionErrors);return expr;}}return _superPropGet(TypeScriptParserMixin,\"parseConditional\",this,3)([expr,startLoc,refExpressionErrors]);}},{key:\"parseParenItem\",value:function parseParenItem(node,startLoc){var newNode=_superPropGet(TypeScriptParserMixin,\"parseParenItem\",this,3)([node,startLoc]);if(this.eat(17)){newNode.optional=true;this.resetEndLocation(node);}if(this.match(14)){var typeCastNode=this.startNodeAt(startLoc);typeCastNode.expression=node;typeCastNode.typeAnnotation=this.tsParseTypeAnnotation();return this.finishNode(typeCastNode,\"TSTypeCastExpression\");}return node;}},{key:\"parseExportDeclaration\",value:function parseExportDeclaration(node){var _this47=this;if(!this.state.isAmbientContext&&this.isContextual(125)){return this.tsInAmbientContext(function(){return _this47.parseExportDeclaration(node);});}var startLoc=this.state.startLoc;var isDeclare=this.eatContextual(125);if(isDeclare&&(this.isContextual(125)||!this.shouldParseExportDeclaration())){throw this.raise(TSErrors.ExpectedAmbientAfterExportDeclare,this.state.startLoc);}var isIdentifier=tokenIsIdentifier(this.state.type);var declaration=isIdentifier&&this.tsTryParseExportDeclaration()||_superPropGet(TypeScriptParserMixin,\"parseExportDeclaration\",this,3)([node]);if(!declaration)return null;if(declaration.type===\"TSInterfaceDeclaration\"||declaration.type===\"TSTypeAliasDeclaration\"||isDeclare){node.exportKind=\"type\";}if(isDeclare&&declaration.type!==\"TSImportEqualsDeclaration\"){this.resetStartLocation(declaration,startLoc);declaration.declare=true;}return declaration;}},{key:\"parseClassId\",value:function parseClassId(node,isStatement,optionalId,bindingType){if((!isStatement||optionalId)&&this.isContextual(113)){return;}_superPropGet(TypeScriptParserMixin,\"parseClassId\",this,3)([node,isStatement,optionalId,node.declare?1024:8331]);var typeParameters=this.tsTryParseTypeParameters(this.tsParseInOutConstModifiers);if(typeParameters)node.typeParameters=typeParameters;}},{key:\"parseClassPropertyAnnotation\",value:function parseClassPropertyAnnotation(node){if(!node.optional){if(this.eat(35)){node.definite=true;}else if(this.eat(17)){node.optional=true;}}var type=this.tsTryParseTypeAnnotation();if(type)node.typeAnnotation=type;}},{key:\"parseClassProperty\",value:function parseClassProperty(node){this.parseClassPropertyAnnotation(node);if(this.state.isAmbientContext&&!(node.readonly&&!node.typeAnnotation)&&this.match(29)){this.raise(TSErrors.DeclareClassFieldHasInitializer,this.state.startLoc);}if(node.abstract&&this.match(29)){var key=node.key;this.raise(TSErrors.AbstractPropertyHasInitializer,this.state.startLoc,{propertyName:key.type===\"Identifier\"&&!node.computed?key.name:\"[\".concat(this.input.slice(this.offsetToSourcePos(key.start),this.offsetToSourcePos(key.end)),\"]\")});}return _superPropGet(TypeScriptParserMixin,\"parseClassProperty\",this,3)([node]);}},{key:\"parseClassPrivateProperty\",value:function parseClassPrivateProperty(node){if(node.abstract){this.raise(TSErrors.PrivateElementHasAbstract,node);}if(node.accessibility){this.raise(TSErrors.PrivateElementHasAccessibility,node,{modifier:node.accessibility});}this.parseClassPropertyAnnotation(node);return _superPropGet(TypeScriptParserMixin,\"parseClassPrivateProperty\",this,3)([node]);}},{key:\"parseClassAccessorProperty\",value:function parseClassAccessorProperty(node){this.parseClassPropertyAnnotation(node);if(node.optional){this.raise(TSErrors.AccessorCannotBeOptional,node);}return _superPropGet(TypeScriptParserMixin,\"parseClassAccessorProperty\",this,3)([node]);}},{key:\"pushClassMethod\",value:function pushClassMethod(classBody,method,isGenerator,isAsync,isConstructor,allowsDirectSuper){var typeParameters=this.tsTryParseTypeParameters(this.tsParseConstModifier);if(typeParameters&&isConstructor){this.raise(TSErrors.ConstructorHasTypeParameters,typeParameters);}var _method$declare=method.declare,declare=_method$declare===void 0?false:_method$declare,kind=method.kind;if(declare&&(kind===\"get\"||kind===\"set\")){this.raise(TSErrors.DeclareAccessor,method,{kind:kind});}if(typeParameters)method.typeParameters=typeParameters;_superPropGet(TypeScriptParserMixin,\"pushClassMethod\",this,3)([classBody,method,isGenerator,isAsync,isConstructor,allowsDirectSuper]);}},{key:\"pushClassPrivateMethod\",value:function pushClassPrivateMethod(classBody,method,isGenerator,isAsync){var typeParameters=this.tsTryParseTypeParameters(this.tsParseConstModifier);if(typeParameters)method.typeParameters=typeParameters;_superPropGet(TypeScriptParserMixin,\"pushClassPrivateMethod\",this,3)([classBody,method,isGenerator,isAsync]);}},{key:\"declareClassPrivateMethodInScope\",value:function declareClassPrivateMethodInScope(node,kind){if(node.type===\"TSDeclareMethod\")return;if(node.type===\"MethodDefinition\"&&node.value.body==null){return;}_superPropGet(TypeScriptParserMixin,\"declareClassPrivateMethodInScope\",this,3)([node,kind]);}},{key:\"parseClassSuper\",value:function parseClassSuper(node){_superPropGet(TypeScriptParserMixin,\"parseClassSuper\",this,3)([node]);if(node.superClass&&(this.match(47)||this.match(51))){{node.superTypeParameters=this.tsParseTypeArgumentsInExpression();}}if(this.eatContextual(113)){node.implements=this.tsParseHeritageClause(\"implements\");}}},{key:\"parseObjPropValue\",value:function parseObjPropValue(prop,startLoc,isGenerator,isAsync,isPattern,isAccessor,refExpressionErrors){var typeParameters=this.tsTryParseTypeParameters(this.tsParseConstModifier);if(typeParameters)prop.typeParameters=typeParameters;return _superPropGet(TypeScriptParserMixin,\"parseObjPropValue\",this,3)([prop,startLoc,isGenerator,isAsync,isPattern,isAccessor,refExpressionErrors]);}},{key:\"parseFunctionParams\",value:function parseFunctionParams(node,isConstructor){var typeParameters=this.tsTryParseTypeParameters(this.tsParseConstModifier);if(typeParameters)node.typeParameters=typeParameters;_superPropGet(TypeScriptParserMixin,\"parseFunctionParams\",this,3)([node,isConstructor]);}},{key:\"parseVarId\",value:function parseVarId(decl,kind){_superPropGet(TypeScriptParserMixin,\"parseVarId\",this,3)([decl,kind]);if(decl.id.type===\"Identifier\"&&!this.hasPrecedingLineBreak()&&this.eat(35)){decl.definite=true;}var type=this.tsTryParseTypeAnnotation();if(type){decl.id.typeAnnotation=type;this.resetEndLocation(decl.id);}}},{key:\"parseAsyncArrowFromCallExpression\",value:function parseAsyncArrowFromCallExpression(node,call){if(this.match(14)){node.returnType=this.tsParseTypeAnnotation();}return _superPropGet(TypeScriptParserMixin,\"parseAsyncArrowFromCallExpression\",this,3)([node,call]);}},{key:\"parseMaybeAssign\",value:function parseMaybeAssign(refExpressionErrors,afterLeftParse){var _this48=this;var _jsx,_jsx2,_typeCast,_jsx3,_typeCast2;var state;var jsx;var typeCast;if(this.hasPlugin(\"jsx\")&&(this.match(143)||this.match(47))){state=this.state.clone();jsx=this.tryParse(function(){return _superPropGet(TypeScriptParserMixin,\"parseMaybeAssign\",_this48,3)([refExpressionErrors,afterLeftParse]);},state);if(!jsx.error)return jsx.node;var context=this.state.context;var currentContext=context[context.length-1];if(currentContext===types.j_oTag||currentContext===types.j_expr){context.pop();}}if(!((_jsx=jsx)!=null&&_jsx.error)&&!this.match(47)){return _superPropGet(TypeScriptParserMixin,\"parseMaybeAssign\",this,3)([refExpressionErrors,afterLeftParse]);}if(!state||state===this.state)state=this.state.clone();var typeParameters;var arrow=this.tryParse(function(abort){var _expr$extra,_typeParameters;typeParameters=_this48.tsParseTypeParameters(_this48.tsParseConstModifier);var expr=_superPropGet(TypeScriptParserMixin,\"parseMaybeAssign\",_this48,3)([refExpressionErrors,afterLeftParse]);if(expr.type!==\"ArrowFunctionExpression\"||(_expr$extra=expr.extra)!=null&&_expr$extra.parenthesized){abort();}if(((_typeParameters=typeParameters)==null?void 0:_typeParameters.params.length)!==0){_this48.resetStartLocationFromNode(expr,typeParameters);}expr.typeParameters=typeParameters;return expr;},state);if(!arrow.error&&!arrow.aborted){if(typeParameters)this.reportReservedArrowTypeParam(typeParameters);return arrow.node;}if(!jsx){assert(!this.hasPlugin(\"jsx\"));typeCast=this.tryParse(function(){return _superPropGet(TypeScriptParserMixin,\"parseMaybeAssign\",_this48,3)([refExpressionErrors,afterLeftParse]);},state);if(!typeCast.error)return typeCast.node;}if((_jsx2=jsx)!=null&&_jsx2.node){this.state=jsx.failState;return jsx.node;}if(arrow.node){this.state=arrow.failState;if(typeParameters)this.reportReservedArrowTypeParam(typeParameters);return arrow.node;}if((_typeCast=typeCast)!=null&&_typeCast.node){this.state=typeCast.failState;return typeCast.node;}throw((_jsx3=jsx)==null?void 0:_jsx3.error)||arrow.error||((_typeCast2=typeCast)==null?void 0:_typeCast2.error);}},{key:\"reportReservedArrowTypeParam\",value:function reportReservedArrowTypeParam(node){var _node$extra2;if(node.params.length===1&&!node.params[0].constraint&&!((_node$extra2=node.extra)!=null&&_node$extra2.trailingComma)&&this.getPluginOption(\"typescript\",\"disallowAmbiguousJSXLike\")){this.raise(TSErrors.ReservedArrowTypeParam,node);}}},{key:\"parseMaybeUnary\",value:function parseMaybeUnary(refExpressionErrors,sawUnary){if(!this.hasPlugin(\"jsx\")&&this.match(47)){return this.tsParseTypeAssertion();}return _superPropGet(TypeScriptParserMixin,\"parseMaybeUnary\",this,3)([refExpressionErrors,sawUnary]);}},{key:\"parseArrow\",value:function parseArrow(node){var _this49=this;if(this.match(14)){var result=this.tryParse(function(abort){var returnType=_this49.tsParseTypeOrTypePredicateAnnotation(14);if(_this49.canInsertSemicolon()||!_this49.match(19))abort();return returnType;});if(result.aborted)return;if(!result.thrown){if(result.error)this.state=result.failState;node.returnType=result.node;}}return _superPropGet(TypeScriptParserMixin,\"parseArrow\",this,3)([node]);}},{key:\"parseFunctionParamType\",value:function parseFunctionParamType(param){if(this.eat(17)){param.optional=true;}var type=this.tsTryParseTypeAnnotation();if(type)param.typeAnnotation=type;this.resetEndLocation(param);return param;}},{key:\"isAssignable\",value:function isAssignable(node,isBinding){switch(node.type){case\"TSTypeCastExpression\":return this.isAssignable(node.expression,isBinding);case\"TSParameterProperty\":return true;default:return _superPropGet(TypeScriptParserMixin,\"isAssignable\",this,3)([node,isBinding]);}}},{key:\"toAssignable\",value:function toAssignable(node){var isLHS=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;switch(node.type){case\"ParenthesizedExpression\":this.toAssignableParenthesizedExpression(node,isLHS);break;case\"TSAsExpression\":case\"TSSatisfiesExpression\":case\"TSNonNullExpression\":case\"TSTypeAssertion\":if(isLHS){this.expressionScope.recordArrowParameterBindingError(TSErrors.UnexpectedTypeCastInParameter,node);}else{this.raise(TSErrors.UnexpectedTypeCastInParameter,node);}this.toAssignable(node.expression,isLHS);break;case\"AssignmentExpression\":if(!isLHS&&node.left.type===\"TSTypeCastExpression\"){node.left=this.typeCastToParameter(node.left);}default:_superPropGet(TypeScriptParserMixin,\"toAssignable\",this,3)([node,isLHS]);}}},{key:\"toAssignableParenthesizedExpression\",value:function toAssignableParenthesizedExpression(node,isLHS){switch(node.expression.type){case\"TSAsExpression\":case\"TSSatisfiesExpression\":case\"TSNonNullExpression\":case\"TSTypeAssertion\":case\"ParenthesizedExpression\":this.toAssignable(node.expression,isLHS);break;default:_superPropGet(TypeScriptParserMixin,\"toAssignable\",this,3)([node,isLHS]);}}},{key:\"checkToRestConversion\",value:function checkToRestConversion(node,allowPattern){switch(node.type){case\"TSAsExpression\":case\"TSSatisfiesExpression\":case\"TSTypeAssertion\":case\"TSNonNullExpression\":this.checkToRestConversion(node.expression,false);break;default:_superPropGet(TypeScriptParserMixin,\"checkToRestConversion\",this,3)([node,allowPattern]);}}},{key:\"isValidLVal\",value:function isValidLVal(type,isUnparenthesizedInAssign,binding){switch(type){case\"TSTypeCastExpression\":return true;case\"TSParameterProperty\":return\"parameter\";case\"TSNonNullExpression\":return\"expression\";case\"TSAsExpression\":case\"TSSatisfiesExpression\":case\"TSTypeAssertion\":return(binding!==64||!isUnparenthesizedInAssign)&&[\"expression\",true];default:return _superPropGet(TypeScriptParserMixin,\"isValidLVal\",this,3)([type,isUnparenthesizedInAssign,binding]);}}},{key:\"parseBindingAtom\",value:function parseBindingAtom(){if(this.state.type===78){return this.parseIdentifier(true);}return _superPropGet(TypeScriptParserMixin,\"parseBindingAtom\",this,3)([]);}},{key:\"parseMaybeDecoratorArguments\",value:function parseMaybeDecoratorArguments(expr,startLoc){if(this.match(47)||this.match(51)){var typeArguments=this.tsParseTypeArgumentsInExpression();if(this.match(10)){var call=_superPropGet(TypeScriptParserMixin,\"parseMaybeDecoratorArguments\",this,3)([expr,startLoc]);{call.typeParameters=typeArguments;}return call;}this.unexpected(null,10);}return _superPropGet(TypeScriptParserMixin,\"parseMaybeDecoratorArguments\",this,3)([expr,startLoc]);}},{key:\"checkCommaAfterRest\",value:function checkCommaAfterRest(close){if(this.state.isAmbientContext&&this.match(12)&&this.lookaheadCharCode()===close){this.next();return false;}return _superPropGet(TypeScriptParserMixin,\"checkCommaAfterRest\",this,3)([close]);}},{key:\"isClassMethod\",value:function isClassMethod(){return this.match(47)||_superPropGet(TypeScriptParserMixin,\"isClassMethod\",this,3)([]);}},{key:\"isClassProperty\",value:function isClassProperty(){return this.match(35)||this.match(14)||_superPropGet(TypeScriptParserMixin,\"isClassProperty\",this,3)([]);}},{key:\"parseMaybeDefault\",value:function parseMaybeDefault(startLoc,left){var node=_superPropGet(TypeScriptParserMixin,\"parseMaybeDefault\",this,3)([startLoc,left]);if(node.type===\"AssignmentPattern\"&&node.typeAnnotation&&node.right.start<node.typeAnnotation.start){this.raise(TSErrors.TypeAnnotationAfterAssign,node.typeAnnotation);}return node;}},{key:\"getTokenFromCode\",value:function getTokenFromCode(code){if(this.state.inType){if(code===62){this.finishOp(48,1);return;}if(code===60){this.finishOp(47,1);return;}}_superPropGet(TypeScriptParserMixin,\"getTokenFromCode\",this,3)([code]);}},{key:\"reScan_lt_gt\",value:function reScan_lt_gt(){var type=this.state.type;if(type===47){this.state.pos-=1;this.readToken_lt();}else if(type===48){this.state.pos-=1;this.readToken_gt();}}},{key:\"reScan_lt\",value:function reScan_lt(){var type=this.state.type;if(type===51){this.state.pos-=2;this.finishOp(47,1);return 47;}return type;}},{key:\"toAssignableListItem\",value:function toAssignableListItem(exprList,index,isLHS){var node=exprList[index];if(node.type===\"TSTypeCastExpression\"){exprList[index]=this.typeCastToParameter(node);}_superPropGet(TypeScriptParserMixin,\"toAssignableListItem\",this,3)([exprList,index,isLHS]);}},{key:\"typeCastToParameter\",value:function typeCastToParameter(node){node.expression.typeAnnotation=node.typeAnnotation;this.resetEndLocation(node.expression,node.typeAnnotation.loc.end);return node.expression;}},{key:\"shouldParseArrow\",value:function shouldParseArrow(params){var _this50=this;if(this.match(14)){return params.every(function(expr){return _this50.isAssignable(expr,true);});}return _superPropGet(TypeScriptParserMixin,\"shouldParseArrow\",this,3)([params]);}},{key:\"shouldParseAsyncArrow\",value:function shouldParseAsyncArrow(){return this.match(14)||_superPropGet(TypeScriptParserMixin,\"shouldParseAsyncArrow\",this,3)([]);}},{key:\"canHaveLeadingDecorator\",value:function canHaveLeadingDecorator(){return _superPropGet(TypeScriptParserMixin,\"canHaveLeadingDecorator\",this,3)([])||this.isAbstractClass();}},{key:\"jsxParseOpeningElementAfterName\",value:function jsxParseOpeningElementAfterName(node){var _this51=this;if(this.match(47)||this.match(51)){var typeArguments=this.tsTryParseAndCatch(function(){return _this51.tsParseTypeArgumentsInExpression();});if(typeArguments){{node.typeParameters=typeArguments;}}}return _superPropGet(TypeScriptParserMixin,\"jsxParseOpeningElementAfterName\",this,3)([node]);}},{key:\"getGetterSetterExpectedParamCount\",value:function getGetterSetterExpectedParamCount(method){var baseCount=_superPropGet(TypeScriptParserMixin,\"getGetterSetterExpectedParamCount\",this,3)([method]);var params=this.getObjectOrClassMethodParams(method);var firstParam=params[0];var hasContextParam=firstParam&&this.isThisParam(firstParam);return hasContextParam?baseCount+1:baseCount;}},{key:\"parseCatchClauseParam\",value:function parseCatchClauseParam(){var param=_superPropGet(TypeScriptParserMixin,\"parseCatchClauseParam\",this,3)([]);var type=this.tsTryParseTypeAnnotation();if(type){param.typeAnnotation=type;this.resetEndLocation(param);}return param;}},{key:\"tsInAmbientContext\",value:function tsInAmbientContext(cb){var _this$state3=this.state,oldIsAmbientContext=_this$state3.isAmbientContext,oldStrict=_this$state3.strict;this.state.isAmbientContext=true;this.state.strict=false;try{return cb();}finally{this.state.isAmbientContext=oldIsAmbientContext;this.state.strict=oldStrict;}}},{key:\"parseClass\",value:function parseClass(node,isStatement,optionalId){var oldInAbstractClass=this.state.inAbstractClass;this.state.inAbstractClass=!!node.abstract;try{return _superPropGet(TypeScriptParserMixin,\"parseClass\",this,3)([node,isStatement,optionalId]);}finally{this.state.inAbstractClass=oldInAbstractClass;}}},{key:\"tsParseAbstractDeclaration\",value:function tsParseAbstractDeclaration(node,decorators){if(this.match(80)){node.abstract=true;return this.maybeTakeDecorators(decorators,this.parseClass(node,true,false));}else if(this.isContextual(129)){if(!this.hasFollowingLineBreak()){node.abstract=true;this.raise(TSErrors.NonClassMethodPropertyHasAbstractModifier,node);return this.tsParseInterfaceDeclaration(node);}}else{this.unexpected(null,80);}}},{key:\"parseMethod\",value:function parseMethod(node,isGenerator,isAsync,isConstructor,allowDirectSuper,type,inClassScope){var method=_superPropGet(TypeScriptParserMixin,\"parseMethod\",this,3)([node,isGenerator,isAsync,isConstructor,allowDirectSuper,type,inClassScope]);if(method.abstract||method.type===\"TSAbstractMethodDefinition\"){var hasEstreePlugin=this.hasPlugin(\"estree\");var methodFn=hasEstreePlugin?method.value:method;if(methodFn.body){var key=method.key;this.raise(TSErrors.AbstractMethodHasImplementation,method,{methodName:key.type===\"Identifier\"&&!method.computed?key.name:\"[\".concat(this.input.slice(this.offsetToSourcePos(key.start),this.offsetToSourcePos(key.end)),\"]\")});}}return method;}},{key:\"tsParseTypeParameterName\",value:function tsParseTypeParameterName(){var typeName=this.parseIdentifier();return typeName.name;}},{key:\"shouldParseAsAmbientContext\",value:function shouldParseAsAmbientContext(){return!!this.getPluginOption(\"typescript\",\"dts\");}},{key:\"parse\",value:function parse(){if(this.shouldParseAsAmbientContext()){this.state.isAmbientContext=true;}return _superPropGet(TypeScriptParserMixin,\"parse\",this,3)([]);}},{key:\"getExpression\",value:function getExpression(){if(this.shouldParseAsAmbientContext()){this.state.isAmbientContext=true;}return _superPropGet(TypeScriptParserMixin,\"getExpression\",this,3)([]);}},{key:\"parseExportSpecifier\",value:function parseExportSpecifier(node,isString,isInTypeExport,isMaybeTypeOnly){if(!isString&&isMaybeTypeOnly){this.parseTypeOnlyImportExportSpecifier(node,false,isInTypeExport);return this.finishNode(node,\"ExportSpecifier\");}node.exportKind=\"value\";return _superPropGet(TypeScriptParserMixin,\"parseExportSpecifier\",this,3)([node,isString,isInTypeExport,isMaybeTypeOnly]);}},{key:\"parseImportSpecifier\",value:function parseImportSpecifier(specifier,importedIsString,isInTypeOnlyImport,isMaybeTypeOnly,bindingType){if(!importedIsString&&isMaybeTypeOnly){this.parseTypeOnlyImportExportSpecifier(specifier,true,isInTypeOnlyImport);return this.finishNode(specifier,\"ImportSpecifier\");}specifier.importKind=\"value\";return _superPropGet(TypeScriptParserMixin,\"parseImportSpecifier\",this,3)([specifier,importedIsString,isInTypeOnlyImport,isMaybeTypeOnly,isInTypeOnlyImport?4098:4096]);}},{key:\"parseTypeOnlyImportExportSpecifier\",value:function parseTypeOnlyImportExportSpecifier(node,isImport,isInTypeOnlyImportExport){var leftOfAsKey=isImport?\"imported\":\"local\";var rightOfAsKey=isImport?\"local\":\"exported\";var leftOfAs=node[leftOfAsKey];var rightOfAs;var hasTypeSpecifier=false;var canParseAsKeyword=true;var loc=leftOfAs.loc.start;if(this.isContextual(93)){var firstAs=this.parseIdentifier();if(this.isContextual(93)){var secondAs=this.parseIdentifier();if(tokenIsKeywordOrIdentifier(this.state.type)){hasTypeSpecifier=true;leftOfAs=firstAs;rightOfAs=isImport?this.parseIdentifier():this.parseModuleExportName();canParseAsKeyword=false;}else{rightOfAs=secondAs;canParseAsKeyword=false;}}else if(tokenIsKeywordOrIdentifier(this.state.type)){canParseAsKeyword=false;rightOfAs=isImport?this.parseIdentifier():this.parseModuleExportName();}else{hasTypeSpecifier=true;leftOfAs=firstAs;}}else if(tokenIsKeywordOrIdentifier(this.state.type)){hasTypeSpecifier=true;if(isImport){leftOfAs=this.parseIdentifier(true);if(!this.isContextual(93)){this.checkReservedWord(leftOfAs.name,leftOfAs.loc.start,true,true);}}else{leftOfAs=this.parseModuleExportName();}}if(hasTypeSpecifier&&isInTypeOnlyImportExport){this.raise(isImport?TSErrors.TypeModifierIsUsedInTypeImports:TSErrors.TypeModifierIsUsedInTypeExports,loc);}node[leftOfAsKey]=leftOfAs;node[rightOfAsKey]=rightOfAs;var kindKey=isImport?\"importKind\":\"exportKind\";node[kindKey]=hasTypeSpecifier?\"type\":\"value\";if(canParseAsKeyword&&this.eatContextual(93)){node[rightOfAsKey]=isImport?this.parseIdentifier():this.parseModuleExportName();}if(!node[rightOfAsKey]){node[rightOfAsKey]=this.cloneIdentifier(node[leftOfAsKey]);}if(isImport){this.checkIdentifier(node[rightOfAsKey],hasTypeSpecifier?4098:4096);}}},{key:\"fillOptionalPropertiesForTSESLint\",value:function fillOptionalPropertiesForTSESLint(node){var _node$directive,_node$decorators,_node$optional,_node$typeAnnotation,_node$accessibility,_node$decorators2,_node$override,_node$readonly,_node$static,_node$declare,_node$returnType,_node$typeParameters,_node$optional2,_node$optional3,_node$accessibility2,_node$readonly2,_node$static2,_node$declare2,_node$definite,_node$readonly3,_node$typeAnnotation2,_node$accessibility3,_node$decorators3,_node$override2,_node$optional4,_node$id,_node$abstract,_node$declare3,_node$decorators4,_node$implements,_node$superTypeArgume,_node$typeParameters2,_node$declare4,_node$definite2,_node$const,_node$declare5,_node$computed,_node$qualifier,_node$options,_node$declare6,_node$extends,_node$declare7,_node$global,_node$const2,_node$in,_node$out;switch(node.type){case\"ExpressionStatement\":(_node$directive=node.directive)!=null?_node$directive:node.directive=undefined;return;case\"RestElement\":node.value=undefined;case\"Identifier\":case\"ArrayPattern\":case\"AssignmentPattern\":case\"ObjectPattern\":(_node$decorators=node.decorators)!=null?_node$decorators:node.decorators=[];(_node$optional=node.optional)!=null?_node$optional:node.optional=false;(_node$typeAnnotation=node.typeAnnotation)!=null?_node$typeAnnotation:node.typeAnnotation=undefined;return;case\"TSParameterProperty\":(_node$accessibility=node.accessibility)!=null?_node$accessibility:node.accessibility=undefined;(_node$decorators2=node.decorators)!=null?_node$decorators2:node.decorators=[];(_node$override=node.override)!=null?_node$override:node.override=false;(_node$readonly=node.readonly)!=null?_node$readonly:node.readonly=false;(_node$static=node.static)!=null?_node$static:node.static=false;return;case\"TSEmptyBodyFunctionExpression\":node.body=null;case\"TSDeclareFunction\":case\"FunctionDeclaration\":case\"FunctionExpression\":case\"ClassMethod\":case\"ClassPrivateMethod\":(_node$declare=node.declare)!=null?_node$declare:node.declare=false;(_node$returnType=node.returnType)!=null?_node$returnType:node.returnType=undefined;(_node$typeParameters=node.typeParameters)!=null?_node$typeParameters:node.typeParameters=undefined;return;case\"Property\":(_node$optional2=node.optional)!=null?_node$optional2:node.optional=false;return;case\"TSMethodSignature\":case\"TSPropertySignature\":(_node$optional3=node.optional)!=null?_node$optional3:node.optional=false;case\"TSIndexSignature\":(_node$accessibility2=node.accessibility)!=null?_node$accessibility2:node.accessibility=undefined;(_node$readonly2=node.readonly)!=null?_node$readonly2:node.readonly=false;(_node$static2=node.static)!=null?_node$static2:node.static=false;return;case\"TSAbstractPropertyDefinition\":case\"PropertyDefinition\":case\"TSAbstractAccessorProperty\":case\"AccessorProperty\":(_node$declare2=node.declare)!=null?_node$declare2:node.declare=false;(_node$definite=node.definite)!=null?_node$definite:node.definite=false;(_node$readonly3=node.readonly)!=null?_node$readonly3:node.readonly=false;(_node$typeAnnotation2=node.typeAnnotation)!=null?_node$typeAnnotation2:node.typeAnnotation=undefined;case\"TSAbstractMethodDefinition\":case\"MethodDefinition\":(_node$accessibility3=node.accessibility)!=null?_node$accessibility3:node.accessibility=undefined;(_node$decorators3=node.decorators)!=null?_node$decorators3:node.decorators=[];(_node$override2=node.override)!=null?_node$override2:node.override=false;(_node$optional4=node.optional)!=null?_node$optional4:node.optional=false;return;case\"ClassExpression\":(_node$id=node.id)!=null?_node$id:node.id=null;case\"ClassDeclaration\":(_node$abstract=node.abstract)!=null?_node$abstract:node.abstract=false;(_node$declare3=node.declare)!=null?_node$declare3:node.declare=false;(_node$decorators4=node.decorators)!=null?_node$decorators4:node.decorators=[];(_node$implements=node.implements)!=null?_node$implements:node.implements=[];(_node$superTypeArgume=node.superTypeArguments)!=null?_node$superTypeArgume:node.superTypeArguments=undefined;(_node$typeParameters2=node.typeParameters)!=null?_node$typeParameters2:node.typeParameters=undefined;return;case\"TSTypeAliasDeclaration\":case\"VariableDeclaration\":(_node$declare4=node.declare)!=null?_node$declare4:node.declare=false;return;case\"VariableDeclarator\":(_node$definite2=node.definite)!=null?_node$definite2:node.definite=false;return;case\"TSEnumDeclaration\":(_node$const=node.const)!=null?_node$const:node.const=false;(_node$declare5=node.declare)!=null?_node$declare5:node.declare=false;return;case\"TSEnumMember\":(_node$computed=node.computed)!=null?_node$computed:node.computed=false;return;case\"TSImportType\":(_node$qualifier=node.qualifier)!=null?_node$qualifier:node.qualifier=null;(_node$options=node.options)!=null?_node$options:node.options=null;return;case\"TSInterfaceDeclaration\":(_node$declare6=node.declare)!=null?_node$declare6:node.declare=false;(_node$extends=node.extends)!=null?_node$extends:node.extends=[];return;case\"TSModuleDeclaration\":(_node$declare7=node.declare)!=null?_node$declare7:node.declare=false;(_node$global=node.global)!=null?_node$global:node.global=node.kind===\"global\";return;case\"TSTypeParameter\":(_node$const2=node.const)!=null?_node$const2:node.const=false;(_node$in=node.in)!=null?_node$in:node.in=false;(_node$out=node.out)!=null?_node$out:node.out=false;return;}}}]);}(superClass);};function isPossiblyLiteralEnum(expression){if(expression.type!==\"MemberExpression\")return false;var computed=expression.computed,property=expression.property;if(computed&&property.type!==\"StringLiteral\"&&(property.type!==\"TemplateLiteral\"||property.expressions.length>0)){return false;}return isUncomputedMemberExpressionChain(expression.object);}function isValidAmbientConstInitializer(expression,estree){var _expression$extra;var type=expression.type;if((_expression$extra=expression.extra)!=null&&_expression$extra.parenthesized){return false;}if(estree){if(type===\"Literal\"){var value=expression.value;if(typeof value===\"string\"||typeof value===\"boolean\"){return true;}}}else{if(type===\"StringLiteral\"||type===\"BooleanLiteral\"){return true;}}if(isNumber(expression,estree)||isNegativeNumber(expression,estree)){return true;}if(type===\"TemplateLiteral\"&&expression.expressions.length===0){return true;}if(isPossiblyLiteralEnum(expression)){return true;}return false;}function isNumber(expression,estree){if(estree){return expression.type===\"Literal\"&&(typeof expression.value===\"number\"||\"bigint\"in expression);}return expression.type===\"NumericLiteral\"||expression.type===\"BigIntLiteral\";}function isNegativeNumber(expression,estree){if(expression.type===\"UnaryExpression\"){var operator=expression.operator,argument=expression.argument;if(operator===\"-\"&&isNumber(argument,estree)){return true;}}return false;}function isUncomputedMemberExpressionChain(expression){if(expression.type===\"Identifier\")return true;if(expression.type!==\"MemberExpression\"||expression.computed){return false;}return isUncomputedMemberExpressionChain(expression.object);}var PlaceholderErrors=ParseErrorEnum(_templateObject5||(_templateObject5=_taggedTemplateLiteral([\"placeholders\"])))({ClassNameIsRequired:\"A class name is required.\",UnexpectedSpace:\"Unexpected space in placeholder.\"});var placeholders=function placeholders(superClass){return/*#__PURE__*/function(_superClass5){function PlaceholdersParserMixin(){_classCallCheck(this,PlaceholdersParserMixin);return _callSuper(this,PlaceholdersParserMixin,arguments);}_inherits(PlaceholdersParserMixin,_superClass5);return _createClass(PlaceholdersParserMixin,[{key:\"parsePlaceholder\",value:function parsePlaceholder(expectedNode){if(this.match(133)){var node=this.startNode();this.next();this.assertNoSpace();node.name=_superPropGet(PlaceholdersParserMixin,\"parseIdentifier\",this,3)([true]);this.assertNoSpace();this.expect(133);return this.finishPlaceholder(node,expectedNode);}}},{key:\"finishPlaceholder\",value:function finishPlaceholder(node,expectedNode){var placeholder=node;if(!placeholder.expectedNode||!placeholder.type){placeholder=this.finishNode(placeholder,\"Placeholder\");}placeholder.expectedNode=expectedNode;return placeholder;}},{key:\"getTokenFromCode\",value:function getTokenFromCode(code){if(code===37&&this.input.charCodeAt(this.state.pos+1)===37){this.finishOp(133,2);}else{_superPropGet(PlaceholdersParserMixin,\"getTokenFromCode\",this,3)([code]);}}},{key:\"parseExprAtom\",value:function parseExprAtom(refExpressionErrors){return this.parsePlaceholder(\"Expression\")||_superPropGet(PlaceholdersParserMixin,\"parseExprAtom\",this,3)([refExpressionErrors]);}},{key:\"parseIdentifier\",value:function parseIdentifier(liberal){return this.parsePlaceholder(\"Identifier\")||_superPropGet(PlaceholdersParserMixin,\"parseIdentifier\",this,3)([liberal]);}},{key:\"checkReservedWord\",value:function checkReservedWord(word,startLoc,checkKeywords,isBinding){if(word!==undefined){_superPropGet(PlaceholdersParserMixin,\"checkReservedWord\",this,3)([word,startLoc,checkKeywords,isBinding]);}}},{key:\"cloneIdentifier\",value:function cloneIdentifier(node){var cloned=_superPropGet(PlaceholdersParserMixin,\"cloneIdentifier\",this,3)([node]);if(cloned.type===\"Placeholder\"){cloned.expectedNode=node.expectedNode;}return cloned;}},{key:\"cloneStringLiteral\",value:function cloneStringLiteral(node){if(node.type===\"Placeholder\"){return this.cloneIdentifier(node);}return _superPropGet(PlaceholdersParserMixin,\"cloneStringLiteral\",this,3)([node]);}},{key:\"parseBindingAtom\",value:function parseBindingAtom(){return this.parsePlaceholder(\"Pattern\")||_superPropGet(PlaceholdersParserMixin,\"parseBindingAtom\",this,3)([]);}},{key:\"isValidLVal\",value:function isValidLVal(type,isParenthesized,binding){return type===\"Placeholder\"||_superPropGet(PlaceholdersParserMixin,\"isValidLVal\",this,3)([type,isParenthesized,binding]);}},{key:\"toAssignable\",value:function toAssignable(node,isLHS){if(node&&node.type===\"Placeholder\"&&node.expectedNode===\"Expression\"){node.expectedNode=\"Pattern\";}else{_superPropGet(PlaceholdersParserMixin,\"toAssignable\",this,3)([node,isLHS]);}}},{key:\"chStartsBindingIdentifier\",value:function chStartsBindingIdentifier(ch,pos){if(_superPropGet(PlaceholdersParserMixin,\"chStartsBindingIdentifier\",this,3)([ch,pos])){return true;}var next=this.nextTokenStart();if(this.input.charCodeAt(next)===37&&this.input.charCodeAt(next+1)===37){return true;}return false;}},{key:\"verifyBreakContinue\",value:function verifyBreakContinue(node,isBreak){if(node.label&&node.label.type===\"Placeholder\")return;_superPropGet(PlaceholdersParserMixin,\"verifyBreakContinue\",this,3)([node,isBreak]);}},{key:\"parseExpressionStatement\",value:function parseExpressionStatement(node,expr){var _expr$extra;if(expr.type!==\"Placeholder\"||(_expr$extra=expr.extra)!=null&&_expr$extra.parenthesized){return _superPropGet(PlaceholdersParserMixin,\"parseExpressionStatement\",this,3)([node,expr]);}if(this.match(14)){var stmt=node;stmt.label=this.finishPlaceholder(expr,\"Identifier\");this.next();stmt.body=_superPropGet(PlaceholdersParserMixin,\"parseStatementOrSloppyAnnexBFunctionDeclaration\",this,3)([]);return this.finishNode(stmt,\"LabeledStatement\");}this.semicolon();var stmtPlaceholder=node;stmtPlaceholder.name=expr.name;return this.finishPlaceholder(stmtPlaceholder,\"Statement\");}},{key:\"parseBlock\",value:function parseBlock(allowDirectives,createNewLexicalScope,afterBlockParse){return this.parsePlaceholder(\"BlockStatement\")||_superPropGet(PlaceholdersParserMixin,\"parseBlock\",this,3)([allowDirectives,createNewLexicalScope,afterBlockParse]);}},{key:\"parseFunctionId\",value:function parseFunctionId(requireId){return this.parsePlaceholder(\"Identifier\")||_superPropGet(PlaceholdersParserMixin,\"parseFunctionId\",this,3)([requireId]);}},{key:\"parseClass\",value:function parseClass(node,isStatement,optionalId){var type=isStatement?\"ClassDeclaration\":\"ClassExpression\";this.next();var oldStrict=this.state.strict;var placeholder=this.parsePlaceholder(\"Identifier\");if(placeholder){if(this.match(81)||this.match(133)||this.match(5)){node.id=placeholder;}else if(optionalId||!isStatement){node.id=null;node.body=this.finishPlaceholder(placeholder,\"ClassBody\");return this.finishNode(node,type);}else{throw this.raise(PlaceholderErrors.ClassNameIsRequired,this.state.startLoc);}}else{this.parseClassId(node,isStatement,optionalId);}_superPropGet(PlaceholdersParserMixin,\"parseClassSuper\",this,3)([node]);node.body=this.parsePlaceholder(\"ClassBody\")||_superPropGet(PlaceholdersParserMixin,\"parseClassBody\",this,3)([!!node.superClass,oldStrict]);return this.finishNode(node,type);}},{key:\"parseExport\",value:function parseExport(node,decorators){var placeholder=this.parsePlaceholder(\"Identifier\");if(!placeholder)return _superPropGet(PlaceholdersParserMixin,\"parseExport\",this,3)([node,decorators]);var node2=node;if(!this.isContextual(98)&&!this.match(12)){node2.specifiers=[];node2.source=null;node2.declaration=this.finishPlaceholder(placeholder,\"Declaration\");return this.finishNode(node2,\"ExportNamedDeclaration\");}this.expectPlugin(\"exportDefaultFrom\");var specifier=this.startNode();specifier.exported=placeholder;node2.specifiers=[this.finishNode(specifier,\"ExportDefaultSpecifier\")];return _superPropGet(PlaceholdersParserMixin,\"parseExport\",this,3)([node2,decorators]);}},{key:\"isExportDefaultSpecifier\",value:function isExportDefaultSpecifier(){if(this.match(65)){var next=this.nextTokenStart();if(this.isUnparsedContextual(next,\"from\")){if(this.input.startsWith(tokenLabelName(133),this.nextTokenStartSince(next+4))){return true;}}}return _superPropGet(PlaceholdersParserMixin,\"isExportDefaultSpecifier\",this,3)([]);}},{key:\"maybeParseExportDefaultSpecifier\",value:function maybeParseExportDefaultSpecifier(node,maybeDefaultIdentifier){var _specifiers;if((_specifiers=node.specifiers)!=null&&_specifiers.length){return true;}return _superPropGet(PlaceholdersParserMixin,\"maybeParseExportDefaultSpecifier\",this,3)([node,maybeDefaultIdentifier]);}},{key:\"checkExport\",value:function checkExport(node){var specifiers=node.specifiers;if(specifiers!=null&&specifiers.length){node.specifiers=specifiers.filter(function(node){return node.exported.type===\"Placeholder\";});}_superPropGet(PlaceholdersParserMixin,\"checkExport\",this,3)([node]);node.specifiers=specifiers;}},{key:\"parseImport\",value:function parseImport(node){var placeholder=this.parsePlaceholder(\"Identifier\");if(!placeholder)return _superPropGet(PlaceholdersParserMixin,\"parseImport\",this,3)([node]);node.specifiers=[];if(!this.isContextual(98)&&!this.match(12)){node.source=this.finishPlaceholder(placeholder,\"StringLiteral\");this.semicolon();return this.finishNode(node,\"ImportDeclaration\");}var specifier=this.startNodeAtNode(placeholder);specifier.local=placeholder;node.specifiers.push(this.finishNode(specifier,\"ImportDefaultSpecifier\"));if(this.eat(12)){var hasStarImport=this.maybeParseStarImportSpecifier(node);if(!hasStarImport)this.parseNamedImportSpecifiers(node);}this.expectContextual(98);node.source=this.parseImportSource();this.semicolon();return this.finishNode(node,\"ImportDeclaration\");}},{key:\"parseImportSource\",value:function parseImportSource(){return this.parsePlaceholder(\"StringLiteral\")||_superPropGet(PlaceholdersParserMixin,\"parseImportSource\",this,3)([]);}},{key:\"assertNoSpace\",value:function assertNoSpace(){if(this.state.start>this.offsetToSourcePos(this.state.lastTokEndLoc.index)){this.raise(PlaceholderErrors.UnexpectedSpace,this.state.lastTokEndLoc);}}}]);}(superClass);};var v8intrinsic=function v8intrinsic(superClass){return/*#__PURE__*/function(_superClass6){function V8IntrinsicMixin(){_classCallCheck(this,V8IntrinsicMixin);return _callSuper(this,V8IntrinsicMixin,arguments);}_inherits(V8IntrinsicMixin,_superClass6);return _createClass(V8IntrinsicMixin,[{key:\"parseV8Intrinsic\",value:function parseV8Intrinsic(){if(this.match(54)){var v8IntrinsicStartLoc=this.state.startLoc;var node=this.startNode();this.next();if(tokenIsIdentifier(this.state.type)){var name=this.parseIdentifierName();var identifier=this.createIdentifier(node,name);this.castNodeTo(identifier,\"V8IntrinsicIdentifier\");if(this.match(10)){return identifier;}}this.unexpected(v8IntrinsicStartLoc);}}},{key:\"parseExprAtom\",value:function parseExprAtom(refExpressionErrors){return this.parseV8Intrinsic()||_superPropGet(V8IntrinsicMixin,\"parseExprAtom\",this,3)([refExpressionErrors]);}}]);}(superClass);};var PIPELINE_PROPOSALS=[\"minimal\",\"fsharp\",\"hack\",\"smart\"];var TOPIC_TOKENS=[\"^^\",\"@@\",\"^\",\"%\",\"#\"];function validatePlugins(pluginsMap){if(pluginsMap.has(\"decorators\")){if(pluginsMap.has(\"decorators-legacy\")){throw new Error(\"Cannot use the decorators and decorators-legacy plugin together\");}var decoratorsBeforeExport=pluginsMap.get(\"decorators\").decoratorsBeforeExport;if(decoratorsBeforeExport!=null&&typeof decoratorsBeforeExport!==\"boolean\"){throw new Error(\"'decoratorsBeforeExport' must be a boolean, if specified.\");}var allowCallParenthesized=pluginsMap.get(\"decorators\").allowCallParenthesized;if(allowCallParenthesized!=null&&typeof allowCallParenthesized!==\"boolean\"){throw new Error(\"'allowCallParenthesized' must be a boolean.\");}}if(pluginsMap.has(\"flow\")&&pluginsMap.has(\"typescript\")){throw new Error(\"Cannot combine flow and typescript plugins.\");}if(pluginsMap.has(\"placeholders\")&&pluginsMap.has(\"v8intrinsic\")){throw new Error(\"Cannot combine placeholders and v8intrinsic plugins.\");}if(pluginsMap.has(\"pipelineOperator\")){var _pluginsMap$get2;var proposal=pluginsMap.get(\"pipelineOperator\").proposal;if(!PIPELINE_PROPOSALS.includes(proposal)){var proposalList=PIPELINE_PROPOSALS.map(function(p){return\"\\\"\".concat(p,\"\\\"\");}).join(\", \");throw new Error(\"\\\"pipelineOperator\\\" requires \\\"proposal\\\" option whose value must be one of: \".concat(proposalList,\".\"));}if(proposal===\"hack\"){if(pluginsMap.has(\"placeholders\")){throw new Error(\"Cannot combine placeholders plugin and Hack-style pipes.\");}if(pluginsMap.has(\"v8intrinsic\")){throw new Error(\"Cannot combine v8intrinsic plugin and Hack-style pipes.\");}var topicToken=pluginsMap.get(\"pipelineOperator\").topicToken;if(!TOPIC_TOKENS.includes(topicToken)){var tokenList=TOPIC_TOKENS.map(function(t){return\"\\\"\".concat(t,\"\\\"\");}).join(\", \");throw new Error(\"\\\"pipelineOperator\\\" in \\\"proposal\\\": \\\"hack\\\" mode also requires a \\\"topicToken\\\" option whose value must be one of: \".concat(tokenList,\".\"));}{var _pluginsMap$get;if(topicToken===\"#\"&&((_pluginsMap$get=pluginsMap.get(\"recordAndTuple\"))==null?void 0:_pluginsMap$get.syntaxType)===\"hash\"){throw new Error(\"Plugin conflict between `[\\\"pipelineOperator\\\", { proposal: \\\"hack\\\", topicToken: \\\"#\\\" }]` and `\".concat(JSON.stringify([\"recordAndTuple\",pluginsMap.get(\"recordAndTuple\")]),\"`.\"));}}}else if(proposal===\"smart\"&&((_pluginsMap$get2=pluginsMap.get(\"recordAndTuple\"))==null?void 0:_pluginsMap$get2.syntaxType)===\"hash\"){throw new Error(\"Plugin conflict between `[\\\"pipelineOperator\\\", { proposal: \\\"smart\\\" }]` and `\".concat(JSON.stringify([\"recordAndTuple\",pluginsMap.get(\"recordAndTuple\")]),\"`.\"));}}if(pluginsMap.has(\"moduleAttributes\")){{if(pluginsMap.has(\"deprecatedImportAssert\")||pluginsMap.has(\"importAssertions\")){throw new Error(\"Cannot combine importAssertions, deprecatedImportAssert and moduleAttributes plugins.\");}var moduleAttributesVersionPluginOption=pluginsMap.get(\"moduleAttributes\").version;if(moduleAttributesVersionPluginOption!==\"may-2020\"){throw new Error(\"The 'moduleAttributes' plugin requires a 'version' option,\"+\" representing the last proposal update. Currently, the\"+\" only supported value is 'may-2020'.\");}}}if(pluginsMap.has(\"importAssertions\")){if(pluginsMap.has(\"deprecatedImportAssert\")){throw new Error(\"Cannot combine importAssertions and deprecatedImportAssert plugins.\");}}if(!pluginsMap.has(\"deprecatedImportAssert\")&&pluginsMap.has(\"importAttributes\")&&pluginsMap.get(\"importAttributes\").deprecatedAssertSyntax){{pluginsMap.set(\"deprecatedImportAssert\",{});}}if(pluginsMap.has(\"recordAndTuple\")){{var syntaxType=pluginsMap.get(\"recordAndTuple\").syntaxType;if(syntaxType!=null){var RECORD_AND_TUPLE_SYNTAX_TYPES=[\"hash\",\"bar\"];if(!RECORD_AND_TUPLE_SYNTAX_TYPES.includes(syntaxType)){throw new Error(\"The 'syntaxType' option of the 'recordAndTuple' plugin must be one of: \"+RECORD_AND_TUPLE_SYNTAX_TYPES.map(function(p){return\"'\".concat(p,\"'\");}).join(\", \"));}}}}if(pluginsMap.has(\"asyncDoExpressions\")&&!pluginsMap.has(\"doExpressions\")){var error=new Error(\"'asyncDoExpressions' requires 'doExpressions', please add 'doExpressions' to parser plugins.\");error.missingPlugins=\"doExpressions\";throw error;}if(pluginsMap.has(\"optionalChainingAssign\")&&pluginsMap.get(\"optionalChainingAssign\").version!==\"2023-07\"){throw new Error(\"The 'optionalChainingAssign' plugin requires a 'version' option,\"+\" representing the last proposal update. Currently, the\"+\" only supported value is '2023-07'.\");}if(pluginsMap.has(\"discardBinding\")&&pluginsMap.get(\"discardBinding\").syntaxType!==\"void\"){throw new Error(\"The 'discardBinding' plugin requires a 'syntaxType' option. Currently the only supported value is 'void'.\");}}var mixinPlugins={estree:estree,jsx:jsx,flow:flow,typescript:typescript,v8intrinsic:v8intrinsic,placeholders:placeholders};var mixinPluginNames=Object.keys(mixinPlugins);var ExpressionParser=/*#__PURE__*/function(_LValParser){function ExpressionParser(){_classCallCheck(this,ExpressionParser);return _callSuper(this,ExpressionParser,arguments);}_inherits(ExpressionParser,_LValParser);return _createClass(ExpressionParser,[{key:\"checkProto\",value:function checkProto(prop,isRecord,sawProto,refExpressionErrors){if(prop.type===\"SpreadElement\"||this.isObjectMethod(prop)||prop.computed||prop.shorthand){return sawProto;}var key=prop.key;var name=key.type===\"Identifier\"?key.name:key.value;if(name===\"__proto__\"){if(isRecord){this.raise(Errors.RecordNoProto,key);return true;}if(sawProto){if(refExpressionErrors){if(refExpressionErrors.doubleProtoLoc===null){refExpressionErrors.doubleProtoLoc=key.loc.start;}}else{this.raise(Errors.DuplicateProto,key);}}return true;}return sawProto;}},{key:\"shouldExitDescending\",value:function shouldExitDescending(expr,potentialArrowAt){return expr.type===\"ArrowFunctionExpression\"&&this.offsetToSourcePos(expr.start)===potentialArrowAt;}},{key:\"getExpression\",value:function getExpression(){this.enterInitialScopes();this.nextToken();if(this.match(140)){throw this.raise(Errors.ParseExpressionEmptyInput,this.state.startLoc);}var expr=this.parseExpression();if(!this.match(140)){throw this.raise(Errors.ParseExpressionExpectsEOF,this.state.startLoc,{unexpected:this.input.codePointAt(this.state.start)});}this.finalizeRemainingComments();expr.comments=this.comments;expr.errors=this.state.errors;if(this.optionFlags&256){expr.tokens=this.tokens;}return expr;}},{key:\"parseExpression\",value:function parseExpression(disallowIn,refExpressionErrors){var _this52=this;if(disallowIn){return this.disallowInAnd(function(){return _this52.parseExpressionBase(refExpressionErrors);});}return this.allowInAnd(function(){return _this52.parseExpressionBase(refExpressionErrors);});}},{key:\"parseExpressionBase\",value:function parseExpressionBase(refExpressionErrors){var startLoc=this.state.startLoc;var expr=this.parseMaybeAssign(refExpressionErrors);if(this.match(12)){var node=this.startNodeAt(startLoc);node.expressions=[expr];while(this.eat(12)){node.expressions.push(this.parseMaybeAssign(refExpressionErrors));}this.toReferencedList(node.expressions);return this.finishNode(node,\"SequenceExpression\");}return expr;}},{key:\"parseMaybeAssignDisallowIn\",value:function parseMaybeAssignDisallowIn(refExpressionErrors,afterLeftParse){var _this53=this;return this.disallowInAnd(function(){return _this53.parseMaybeAssign(refExpressionErrors,afterLeftParse);});}},{key:\"parseMaybeAssignAllowIn\",value:function parseMaybeAssignAllowIn(refExpressionErrors,afterLeftParse){var _this54=this;return this.allowInAnd(function(){return _this54.parseMaybeAssign(refExpressionErrors,afterLeftParse);});}},{key:\"setOptionalParametersError\",value:function setOptionalParametersError(refExpressionErrors){refExpressionErrors.optionalParametersLoc=this.state.startLoc;}},{key:\"parseMaybeAssign\",value:function parseMaybeAssign(refExpressionErrors,afterLeftParse){var startLoc=this.state.startLoc;var isYield=this.isContextual(108);if(isYield){if(this.prodParam.hasYield){this.next();var _left=this.parseYield(startLoc);if(afterLeftParse){_left=afterLeftParse.call(this,_left,startLoc);}return _left;}}var ownExpressionErrors;if(refExpressionErrors){ownExpressionErrors=false;}else{refExpressionErrors=new ExpressionErrors();ownExpressionErrors=true;}var type=this.state.type;if(type===10||tokenIsIdentifier(type)){this.state.potentialArrowAt=this.state.start;}var left=this.parseMaybeConditional(refExpressionErrors);if(afterLeftParse){left=afterLeftParse.call(this,left,startLoc);}if(tokenIsAssignment(this.state.type)){var node=this.startNodeAt(startLoc);var operator=this.state.value;node.operator=operator;if(this.match(29)){this.toAssignable(left,true);node.left=left;var startIndex=startLoc.index;if(refExpressionErrors.doubleProtoLoc!=null&&refExpressionErrors.doubleProtoLoc.index>=startIndex){refExpressionErrors.doubleProtoLoc=null;}if(refExpressionErrors.shorthandAssignLoc!=null&&refExpressionErrors.shorthandAssignLoc.index>=startIndex){refExpressionErrors.shorthandAssignLoc=null;}if(refExpressionErrors.privateKeyLoc!=null&&refExpressionErrors.privateKeyLoc.index>=startIndex){this.checkDestructuringPrivate(refExpressionErrors);refExpressionErrors.privateKeyLoc=null;}if(refExpressionErrors.voidPatternLoc!=null&&refExpressionErrors.voidPatternLoc.index>=startIndex){refExpressionErrors.voidPatternLoc=null;}}else{node.left=left;}this.next();node.right=this.parseMaybeAssign();this.checkLVal(left,this.finishNode(node,\"AssignmentExpression\"));return node;}else if(ownExpressionErrors){this.checkExpressionErrors(refExpressionErrors,true);}if(isYield){var _type=this.state.type;var _startsExpr=this.hasPlugin(\"v8intrinsic\")?tokenCanStartExpression(_type):tokenCanStartExpression(_type)&&!this.match(54);if(_startsExpr&&!this.isAmbiguousPrefixOrIdentifier()){this.raiseOverwrite(Errors.YieldNotInGeneratorFunction,startLoc);return this.parseYield(startLoc);}}return left;}},{key:\"parseMaybeConditional\",value:function parseMaybeConditional(refExpressionErrors){var startLoc=this.state.startLoc;var potentialArrowAt=this.state.potentialArrowAt;var expr=this.parseExprOps(refExpressionErrors);if(this.shouldExitDescending(expr,potentialArrowAt)){return expr;}return this.parseConditional(expr,startLoc,refExpressionErrors);}},{key:\"parseConditional\",value:function parseConditional(expr,startLoc,refExpressionErrors){if(this.eat(17)){var node=this.startNodeAt(startLoc);node.test=expr;node.consequent=this.parseMaybeAssignAllowIn();this.expect(14);node.alternate=this.parseMaybeAssign();return this.finishNode(node,\"ConditionalExpression\");}return expr;}},{key:\"parseMaybeUnaryOrPrivate\",value:function parseMaybeUnaryOrPrivate(refExpressionErrors){return this.match(139)?this.parsePrivateName():this.parseMaybeUnary(refExpressionErrors);}},{key:\"parseExprOps\",value:function parseExprOps(refExpressionErrors){var startLoc=this.state.startLoc;var potentialArrowAt=this.state.potentialArrowAt;var expr=this.parseMaybeUnaryOrPrivate(refExpressionErrors);if(this.shouldExitDescending(expr,potentialArrowAt)){return expr;}return this.parseExprOp(expr,startLoc,-1);}},{key:\"parseExprOp\",value:function parseExprOp(left,leftStartLoc,minPrec){if(this.isPrivateName(left)){var value=this.getPrivateNameSV(left);if(minPrec>=tokenOperatorPrecedence(58)||!this.prodParam.hasIn||!this.match(58)){this.raise(Errors.PrivateInExpectedIn,left,{identifierName:value});}this.classScope.usePrivateName(value,left.loc.start);}var op=this.state.type;if(tokenIsOperator(op)&&(this.prodParam.hasIn||!this.match(58))){var prec=tokenOperatorPrecedence(op);if(prec>minPrec){if(op===39){this.expectPlugin(\"pipelineOperator\");if(this.state.inFSharpPipelineDirectBody){return left;}this.checkPipelineAtInfixOperator(left,leftStartLoc);}var node=this.startNodeAt(leftStartLoc);node.left=left;node.operator=this.state.value;var logical=op===41||op===42;var coalesce=op===40;if(coalesce){prec=tokenOperatorPrecedence(42);}this.next();if(op===39&&this.hasPlugin([\"pipelineOperator\",{proposal:\"minimal\"}])){if(this.state.type===96&&this.prodParam.hasAwait){throw this.raise(Errors.UnexpectedAwaitAfterPipelineBody,this.state.startLoc);}}node.right=this.parseExprOpRightExpr(op,prec);var finishedNode=this.finishNode(node,logical||coalesce?\"LogicalExpression\":\"BinaryExpression\");var nextOp=this.state.type;if(coalesce&&(nextOp===41||nextOp===42)||logical&&nextOp===40){throw this.raise(Errors.MixingCoalesceWithLogical,this.state.startLoc);}return this.parseExprOp(finishedNode,leftStartLoc,minPrec);}}return left;}},{key:\"parseExprOpRightExpr\",value:function parseExprOpRightExpr(op,prec){var _this55=this;var startLoc=this.state.startLoc;switch(op){case 39:switch(this.getPluginOption(\"pipelineOperator\",\"proposal\")){case\"hack\":return this.withTopicBindingContext(function(){return _this55.parseHackPipeBody();});case\"fsharp\":return this.withSoloAwaitPermittingContext(function(){return _this55.parseFSharpPipelineBody(prec);});}if(this.getPluginOption(\"pipelineOperator\",\"proposal\")===\"smart\"){return this.withTopicBindingContext(function(){if(_this55.prodParam.hasYield&&_this55.isContextual(108)){throw _this55.raise(Errors.PipeBodyIsTighter,_this55.state.startLoc);}return _this55.parseSmartPipelineBodyInStyle(_this55.parseExprOpBaseRightExpr(op,prec),startLoc);});}default:return this.parseExprOpBaseRightExpr(op,prec);}}},{key:\"parseExprOpBaseRightExpr\",value:function parseExprOpBaseRightExpr(op,prec){var startLoc=this.state.startLoc;return this.parseExprOp(this.parseMaybeUnaryOrPrivate(),startLoc,tokenIsRightAssociative(op)?prec-1:prec);}},{key:\"parseHackPipeBody\",value:function parseHackPipeBody(){var _body$extra;var startLoc=this.state.startLoc;var body=this.parseMaybeAssign();var requiredParentheses=UnparenthesizedPipeBodyDescriptions.has(body.type);if(requiredParentheses&&!((_body$extra=body.extra)!=null&&_body$extra.parenthesized)){this.raise(Errors.PipeUnparenthesizedBody,startLoc,{type:body.type});}if(!this.topicReferenceWasUsedInCurrentContext()){this.raise(Errors.PipeTopicUnused,startLoc);}return body;}},{key:\"checkExponentialAfterUnary\",value:function checkExponentialAfterUnary(node){if(this.match(57)){this.raise(Errors.UnexpectedTokenUnaryExponentiation,node.argument);}}},{key:\"parseMaybeUnary\",value:function parseMaybeUnary(refExpressionErrors,sawUnary){var startLoc=this.state.startLoc;var isAwait=this.isContextual(96);if(isAwait&&this.recordAwaitIfAllowed()){this.next();var _expr=this.parseAwait(startLoc);if(!sawUnary)this.checkExponentialAfterUnary(_expr);return _expr;}var update=this.match(34);var node=this.startNode();if(tokenIsPrefix(this.state.type)){node.operator=this.state.value;node.prefix=true;if(this.match(72)){this.expectPlugin(\"throwExpressions\");}var isDelete=this.match(89);this.next();node.argument=this.parseMaybeUnary(null,true);this.checkExpressionErrors(refExpressionErrors,true);if(this.state.strict&&isDelete){var arg=node.argument;if(arg.type===\"Identifier\"){this.raise(Errors.StrictDelete,node);}else if(this.hasPropertyAsPrivateName(arg)){this.raise(Errors.DeletePrivateField,node);}}if(!update){if(!sawUnary){this.checkExponentialAfterUnary(node);}return this.finishNode(node,\"UnaryExpression\");}}var expr=this.parseUpdate(node,update,refExpressionErrors);if(isAwait){var type=this.state.type;var _startsExpr2=this.hasPlugin(\"v8intrinsic\")?tokenCanStartExpression(type):tokenCanStartExpression(type)&&!this.match(54);if(_startsExpr2&&!this.isAmbiguousPrefixOrIdentifier()){this.raiseOverwrite(Errors.AwaitNotInAsyncContext,startLoc);return this.parseAwait(startLoc);}}return expr;}},{key:\"parseUpdate\",value:function parseUpdate(node,update,refExpressionErrors){if(update){var updateExpressionNode=node;this.checkLVal(updateExpressionNode.argument,this.finishNode(updateExpressionNode,\"UpdateExpression\"));return node;}var startLoc=this.state.startLoc;var expr=this.parseExprSubscripts(refExpressionErrors);if(this.checkExpressionErrors(refExpressionErrors,false))return expr;while(tokenIsPostfix(this.state.type)&&!this.canInsertSemicolon()){var _node11=this.startNodeAt(startLoc);_node11.operator=this.state.value;_node11.prefix=false;_node11.argument=expr;this.next();this.checkLVal(expr,expr=this.finishNode(_node11,\"UpdateExpression\"));}return expr;}},{key:\"parseExprSubscripts\",value:function parseExprSubscripts(refExpressionErrors){var startLoc=this.state.startLoc;var potentialArrowAt=this.state.potentialArrowAt;var expr=this.parseExprAtom(refExpressionErrors);if(this.shouldExitDescending(expr,potentialArrowAt)){return expr;}return this.parseSubscripts(expr,startLoc);}},{key:\"parseSubscripts\",value:function parseSubscripts(base,startLoc,noCalls){var state={optionalChainMember:false,maybeAsyncArrow:this.atPossibleAsyncArrow(base),stop:false};do{base=this.parseSubscript(base,startLoc,noCalls,state);state.maybeAsyncArrow=false;}while(!state.stop);return base;}},{key:\"parseSubscript\",value:function parseSubscript(base,startLoc,noCalls,state){var type=this.state.type;if(!noCalls&&type===15){return this.parseBind(base,startLoc,noCalls,state);}else if(tokenIsTemplate(type)){return this.parseTaggedTemplateExpression(base,startLoc,state);}var optional=false;if(type===18){if(noCalls){this.raise(Errors.OptionalChainingNoNew,this.state.startLoc);if(this.lookaheadCharCode()===40){return this.stopParseSubscript(base,state);}}state.optionalChainMember=optional=true;this.next();}if(!noCalls&&this.match(10)){return this.parseCoverCallAndAsyncArrowHead(base,startLoc,state,optional);}else{var computed=this.eat(0);if(computed||optional||this.eat(16)){return this.parseMember(base,startLoc,state,computed,optional);}else{return this.stopParseSubscript(base,state);}}}},{key:\"stopParseSubscript\",value:function stopParseSubscript(base,state){state.stop=true;return base;}},{key:\"parseMember\",value:function parseMember(base,startLoc,state,computed,optional){var node=this.startNodeAt(startLoc);node.object=base;node.computed=computed;if(computed){node.property=this.parseExpression();this.expect(3);}else if(this.match(139)){if(base.type===\"Super\"){this.raise(Errors.SuperPrivateField,startLoc);}this.classScope.usePrivateName(this.state.value,this.state.startLoc);node.property=this.parsePrivateName();}else{node.property=this.parseIdentifier(true);}if(state.optionalChainMember){node.optional=optional;return this.finishNode(node,\"OptionalMemberExpression\");}else{return this.finishNode(node,\"MemberExpression\");}}},{key:\"parseBind\",value:function parseBind(base,startLoc,noCalls,state){var node=this.startNodeAt(startLoc);node.object=base;this.next();node.callee=this.parseNoCallExpr();state.stop=true;return this.parseSubscripts(this.finishNode(node,\"BindExpression\"),startLoc,noCalls);}},{key:\"parseCoverCallAndAsyncArrowHead\",value:function parseCoverCallAndAsyncArrowHead(base,startLoc,state,optional){var oldMaybeInArrowParameters=this.state.maybeInArrowParameters;var refExpressionErrors=null;this.state.maybeInArrowParameters=true;this.next();var node=this.startNodeAt(startLoc);node.callee=base;var maybeAsyncArrow=state.maybeAsyncArrow,optionalChainMember=state.optionalChainMember;if(maybeAsyncArrow){this.expressionScope.enter(newAsyncArrowScope());refExpressionErrors=new ExpressionErrors();}if(optionalChainMember){node.optional=optional;}if(optional){node.arguments=this.parseCallExpressionArguments();}else{node.arguments=this.parseCallExpressionArguments(base.type!==\"Super\",node,refExpressionErrors);}var finishedNode=this.finishCallExpression(node,optionalChainMember);if(maybeAsyncArrow&&this.shouldParseAsyncArrow()&&!optional){state.stop=true;this.checkDestructuringPrivate(refExpressionErrors);this.expressionScope.validateAsPattern();this.expressionScope.exit();finishedNode=this.parseAsyncArrowFromCallExpression(this.startNodeAt(startLoc),finishedNode);}else{if(maybeAsyncArrow){this.checkExpressionErrors(refExpressionErrors,true);this.expressionScope.exit();}this.toReferencedArguments(finishedNode);}this.state.maybeInArrowParameters=oldMaybeInArrowParameters;return finishedNode;}},{key:\"toReferencedArguments\",value:function toReferencedArguments(node,isParenthesizedExpr){this.toReferencedListDeep(node.arguments,isParenthesizedExpr);}},{key:\"parseTaggedTemplateExpression\",value:function parseTaggedTemplateExpression(base,startLoc,state){var node=this.startNodeAt(startLoc);node.tag=base;node.quasi=this.parseTemplate(true);if(state.optionalChainMember){this.raise(Errors.OptionalChainingNoTemplate,startLoc);}return this.finishNode(node,\"TaggedTemplateExpression\");}},{key:\"atPossibleAsyncArrow\",value:function atPossibleAsyncArrow(base){return base.type===\"Identifier\"&&base.name===\"async\"&&this.state.lastTokEndLoc.index===base.end&&!this.canInsertSemicolon()&&base.end-base.start===5&&this.offsetToSourcePos(base.start)===this.state.potentialArrowAt;}},{key:\"finishCallExpression\",value:function finishCallExpression(node,optional){if(node.callee.type===\"Import\"){if(node.arguments.length===0||node.arguments.length>2){this.raise(Errors.ImportCallArity,node);}else{var _iterator1=_createForOfIteratorHelper(node.arguments),_step1;try{for(_iterator1.s();!(_step1=_iterator1.n()).done;){var arg=_step1.value;if(arg.type===\"SpreadElement\"){this.raise(Errors.ImportCallSpreadArgument,arg);}}}catch(err){_iterator1.e(err);}finally{_iterator1.f();}}}return this.finishNode(node,optional?\"OptionalCallExpression\":\"CallExpression\");}},{key:\"parseCallExpressionArguments\",value:function parseCallExpressionArguments(allowPlaceholder,nodeForExtra,refExpressionErrors){var elts=[];var first=true;var oldInFSharpPipelineDirectBody=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=false;while(!this.eat(11)){if(first){first=false;}else{this.expect(12);if(this.match(11)){if(nodeForExtra){this.addTrailingCommaExtraToNode(nodeForExtra);}this.next();break;}}elts.push(this.parseExprListItem(11,false,refExpressionErrors,allowPlaceholder));}this.state.inFSharpPipelineDirectBody=oldInFSharpPipelineDirectBody;return elts;}},{key:\"shouldParseAsyncArrow\",value:function shouldParseAsyncArrow(){return this.match(19)&&!this.canInsertSemicolon();}},{key:\"parseAsyncArrowFromCallExpression\",value:function parseAsyncArrowFromCallExpression(node,call){var _call$extra;this.resetPreviousNodeTrailingComments(call);this.expect(19);this.parseArrowExpression(node,call.arguments,true,(_call$extra=call.extra)==null?void 0:_call$extra.trailingCommaLoc);if(call.innerComments){setInnerComments(node,call.innerComments);}if(call.callee.trailingComments){setInnerComments(node,call.callee.trailingComments);}return node;}},{key:\"parseNoCallExpr\",value:function parseNoCallExpr(){var startLoc=this.state.startLoc;return this.parseSubscripts(this.parseExprAtom(),startLoc,true);}},{key:\"parseExprAtom\",value:function parseExprAtom(refExpressionErrors){var node;var decorators=null;var type=this.state.type;switch(type){case 79:return this.parseSuper();case 83:node=this.startNode();this.next();if(this.match(16)){return this.parseImportMetaPropertyOrPhaseCall(node);}if(this.match(10)){if(this.optionFlags&512){return this.parseImportCall(node);}else{return this.finishNode(node,\"Import\");}}else{this.raise(Errors.UnsupportedImport,this.state.lastTokStartLoc);return this.finishNode(node,\"Import\");}case 78:node=this.startNode();this.next();return this.finishNode(node,\"ThisExpression\");case 90:{return this.parseDo(this.startNode(),false);}case 56:case 31:{this.readRegexp();return this.parseRegExpLiteral(this.state.value);}case 135:return this.parseNumericLiteral(this.state.value);case 136:return this.parseBigIntLiteral(this.state.value);case 134:return this.parseStringLiteral(this.state.value);case 84:return this.parseNullLiteral();case 85:return this.parseBooleanLiteral(true);case 86:return this.parseBooleanLiteral(false);case 10:{var canBeArrow=this.state.potentialArrowAt===this.state.start;return this.parseParenAndDistinguishExpression(canBeArrow);}case 0:{return this.parseArrayLike(3,true,false,refExpressionErrors);}case 5:{return this.parseObjectLike(8,false,false,refExpressionErrors);}case 68:return this.parseFunctionOrFunctionSent();case 26:decorators=this.parseDecorators();case 80:return this.parseClass(this.maybeTakeDecorators(decorators,this.startNode()),false);case 77:return this.parseNewOrNewTarget();case 25:case 24:return this.parseTemplate(false);case 15:{node=this.startNode();this.next();node.object=null;var callee=node.callee=this.parseNoCallExpr();if(callee.type===\"MemberExpression\"){return this.finishNode(node,\"BindExpression\");}else{throw this.raise(Errors.UnsupportedBind,callee);}}case 139:{this.raise(Errors.PrivateInExpectedIn,this.state.startLoc,{identifierName:this.state.value});return this.parsePrivateName();}case 33:{return this.parseTopicReferenceThenEqualsSign(54,\"%\");}case 32:{return this.parseTopicReferenceThenEqualsSign(44,\"^\");}case 37:case 38:{return this.parseTopicReference(\"hack\");}case 44:case 54:case 27:{var pipeProposal=this.getPluginOption(\"pipelineOperator\",\"proposal\");if(pipeProposal){return this.parseTopicReference(pipeProposal);}this.unexpected();break;}case 47:{var lookaheadCh=this.input.codePointAt(this.nextTokenStart());if(isIdentifierStart(lookaheadCh)||lookaheadCh===62){this.expectOnePlugin([\"jsx\",\"flow\",\"typescript\"]);}else{this.unexpected();}break;}default:{if(type===137){return this.parseDecimalLiteral(this.state.value);}else if(type===2||type===1){return this.parseArrayLike(this.state.type===2?4:3,false,true);}else if(type===6||type===7){return this.parseObjectLike(this.state.type===6?9:8,false,true);}}if(tokenIsIdentifier(type)){if(this.isContextual(127)&&this.lookaheadInLineCharCode()===123){return this.parseModuleExpression();}var _canBeArrow=this.state.potentialArrowAt===this.state.start;var containsEsc=this.state.containsEsc;var id=this.parseIdentifier();if(!containsEsc&&id.name===\"async\"&&!this.canInsertSemicolon()){var _type2=this.state.type;if(_type2===68){this.resetPreviousNodeTrailingComments(id);this.next();return this.parseAsyncFunctionExpression(this.startNodeAtNode(id));}else if(tokenIsIdentifier(_type2)){if(this.lookaheadCharCode()===61){return this.parseAsyncArrowUnaryFunction(this.startNodeAtNode(id));}else{return id;}}else if(_type2===90){this.resetPreviousNodeTrailingComments(id);return this.parseDo(this.startNodeAtNode(id),true);}}if(_canBeArrow&&this.match(19)&&!this.canInsertSemicolon()){this.next();return this.parseArrowExpression(this.startNodeAtNode(id),[id],false);}return id;}else{this.unexpected();}}}},{key:\"parseTopicReferenceThenEqualsSign\",value:function parseTopicReferenceThenEqualsSign(topicTokenType,topicTokenValue){var pipeProposal=this.getPluginOption(\"pipelineOperator\",\"proposal\");if(pipeProposal){this.state.type=topicTokenType;this.state.value=topicTokenValue;this.state.pos--;this.state.end--;this.state.endLoc=createPositionWithColumnOffset(this.state.endLoc,-1);return this.parseTopicReference(pipeProposal);}else{this.unexpected();}}},{key:\"parseTopicReference\",value:function parseTopicReference(pipeProposal){var node=this.startNode();var startLoc=this.state.startLoc;var tokenType=this.state.type;this.next();return this.finishTopicReference(node,startLoc,pipeProposal,tokenType);}},{key:\"finishTopicReference\",value:function finishTopicReference(node,startLoc,pipeProposal,tokenType){if(this.testTopicReferenceConfiguration(pipeProposal,startLoc,tokenType)){if(pipeProposal===\"hack\"){if(!this.topicReferenceIsAllowedInCurrentContext()){this.raise(Errors.PipeTopicUnbound,startLoc);}this.registerTopicReference();return this.finishNode(node,\"TopicReference\");}else{if(!this.topicReferenceIsAllowedInCurrentContext()){this.raise(Errors.PrimaryTopicNotAllowed,startLoc);}this.registerTopicReference();return this.finishNode(node,\"PipelinePrimaryTopicReference\");}}else{throw this.raise(Errors.PipeTopicUnconfiguredToken,startLoc,{token:tokenLabelName(tokenType)});}}},{key:\"testTopicReferenceConfiguration\",value:function testTopicReferenceConfiguration(pipeProposal,startLoc,tokenType){switch(pipeProposal){case\"hack\":{return this.hasPlugin([\"pipelineOperator\",{topicToken:tokenLabelName(tokenType)}]);}case\"smart\":return tokenType===27;default:throw this.raise(Errors.PipeTopicRequiresHackPipes,startLoc);}}},{key:\"parseAsyncArrowUnaryFunction\",value:function parseAsyncArrowUnaryFunction(node){this.prodParam.enter(functionFlags(true,this.prodParam.hasYield));var params=[this.parseIdentifier()];this.prodParam.exit();if(this.hasPrecedingLineBreak()){this.raise(Errors.LineTerminatorBeforeArrow,this.state.curPosition());}this.expect(19);return this.parseArrowExpression(node,params,true);}},{key:\"parseDo\",value:function parseDo(node,isAsync){this.expectPlugin(\"doExpressions\");if(isAsync){this.expectPlugin(\"asyncDoExpressions\");}node.async=isAsync;this.next();var oldLabels=this.state.labels;this.state.labels=[];if(isAsync){this.prodParam.enter(2);node.body=this.parseBlock();this.prodParam.exit();}else{node.body=this.parseBlock();}this.state.labels=oldLabels;return this.finishNode(node,\"DoExpression\");}},{key:\"parseSuper\",value:function parseSuper(){var node=this.startNode();this.next();if(this.match(10)&&!this.scope.allowDirectSuper&&!(this.optionFlags&16)){this.raise(Errors.SuperNotAllowed,node);}else if(!this.scope.allowSuper&&!(this.optionFlags&16)){this.raise(Errors.UnexpectedSuper,node);}if(!this.match(10)&&!this.match(0)&&!this.match(16)){this.raise(Errors.UnsupportedSuper,node);}return this.finishNode(node,\"Super\");}},{key:\"parsePrivateName\",value:function parsePrivateName(){var node=this.startNode();var id=this.startNodeAt(createPositionWithColumnOffset(this.state.startLoc,1));var name=this.state.value;this.next();node.id=this.createIdentifier(id,name);return this.finishNode(node,\"PrivateName\");}},{key:\"parseFunctionOrFunctionSent\",value:function parseFunctionOrFunctionSent(){var node=this.startNode();this.next();if(this.prodParam.hasYield&&this.match(16)){var meta=this.createIdentifier(this.startNodeAtNode(node),\"function\");this.next();if(this.match(103)){this.expectPlugin(\"functionSent\");}else if(!this.hasPlugin(\"functionSent\")){this.unexpected();}return this.parseMetaProperty(node,meta,\"sent\");}return this.parseFunction(node);}},{key:\"parseMetaProperty\",value:function parseMetaProperty(node,meta,propertyName){node.meta=meta;var containsEsc=this.state.containsEsc;node.property=this.parseIdentifier(true);if(node.property.name!==propertyName||containsEsc){this.raise(Errors.UnsupportedMetaProperty,node.property,{target:meta.name,onlyValidPropertyName:propertyName});}return this.finishNode(node,\"MetaProperty\");}},{key:\"parseImportMetaPropertyOrPhaseCall\",value:function parseImportMetaPropertyOrPhaseCall(node){this.next();if(this.isContextual(105)||this.isContextual(97)){var isSource=this.isContextual(105);this.expectPlugin(isSource?\"sourcePhaseImports\":\"deferredImportEvaluation\");this.next();node.phase=isSource?\"source\":\"defer\";return this.parseImportCall(node);}else{var id=this.createIdentifierAt(this.startNodeAtNode(node),\"import\",this.state.lastTokStartLoc);if(this.isContextual(101)){if(!this.inModule){this.raise(Errors.ImportMetaOutsideModule,id);}this.sawUnambiguousESM=true;}return this.parseMetaProperty(node,id,\"meta\");}}},{key:\"parseLiteralAtNode\",value:function parseLiteralAtNode(value,type,node){this.addExtra(node,\"rawValue\",value);this.addExtra(node,\"raw\",this.input.slice(this.offsetToSourcePos(node.start),this.state.end));node.value=value;this.next();return this.finishNode(node,type);}},{key:\"parseLiteral\",value:function parseLiteral(value,type){var node=this.startNode();return this.parseLiteralAtNode(value,type,node);}},{key:\"parseStringLiteral\",value:function parseStringLiteral(value){return this.parseLiteral(value,\"StringLiteral\");}},{key:\"parseNumericLiteral\",value:function parseNumericLiteral(value){return this.parseLiteral(value,\"NumericLiteral\");}},{key:\"parseBigIntLiteral\",value:function parseBigIntLiteral(value){{return this.parseLiteral(value,\"BigIntLiteral\");}}},{key:\"parseDecimalLiteral\",value:function parseDecimalLiteral(value){return this.parseLiteral(value,\"DecimalLiteral\");}},{key:\"parseRegExpLiteral\",value:function parseRegExpLiteral(value){var node=this.startNode();this.addExtra(node,\"raw\",this.input.slice(this.offsetToSourcePos(node.start),this.state.end));node.pattern=value.pattern;node.flags=value.flags;this.next();return this.finishNode(node,\"RegExpLiteral\");}},{key:\"parseBooleanLiteral\",value:function parseBooleanLiteral(value){var node=this.startNode();node.value=value;this.next();return this.finishNode(node,\"BooleanLiteral\");}},{key:\"parseNullLiteral\",value:function parseNullLiteral(){var node=this.startNode();this.next();return this.finishNode(node,\"NullLiteral\");}},{key:\"parseParenAndDistinguishExpression\",value:function parseParenAndDistinguishExpression(canBeArrow){var startLoc=this.state.startLoc;var val;this.next();this.expressionScope.enter(newArrowHeadScope());var oldMaybeInArrowParameters=this.state.maybeInArrowParameters;var oldInFSharpPipelineDirectBody=this.state.inFSharpPipelineDirectBody;this.state.maybeInArrowParameters=true;this.state.inFSharpPipelineDirectBody=false;var innerStartLoc=this.state.startLoc;var exprList=[];var refExpressionErrors=new ExpressionErrors();var first=true;var spreadStartLoc;var optionalCommaStartLoc;while(!this.match(11)){if(first){first=false;}else{this.expect(12,refExpressionErrors.optionalParametersLoc===null?null:refExpressionErrors.optionalParametersLoc);if(this.match(11)){optionalCommaStartLoc=this.state.startLoc;break;}}if(this.match(21)){var spreadNodeStartLoc=this.state.startLoc;spreadStartLoc=this.state.startLoc;exprList.push(this.parseParenItem(this.parseRestBinding(),spreadNodeStartLoc));if(!this.checkCommaAfterRest(41)){break;}}else{exprList.push(this.parseMaybeAssignAllowInOrVoidPattern(11,refExpressionErrors,this.parseParenItem));}}var innerEndLoc=this.state.lastTokEndLoc;this.expect(11);this.state.maybeInArrowParameters=oldMaybeInArrowParameters;this.state.inFSharpPipelineDirectBody=oldInFSharpPipelineDirectBody;var arrowNode=this.startNodeAt(startLoc);if(canBeArrow&&this.shouldParseArrow(exprList)&&(arrowNode=this.parseArrow(arrowNode))){this.checkDestructuringPrivate(refExpressionErrors);this.expressionScope.validateAsPattern();this.expressionScope.exit();this.parseArrowExpression(arrowNode,exprList,false);return arrowNode;}this.expressionScope.exit();if(!exprList.length){this.unexpected(this.state.lastTokStartLoc);}if(optionalCommaStartLoc)this.unexpected(optionalCommaStartLoc);if(spreadStartLoc)this.unexpected(spreadStartLoc);this.checkExpressionErrors(refExpressionErrors,true);this.toReferencedListDeep(exprList,true);if(exprList.length>1){val=this.startNodeAt(innerStartLoc);val.expressions=exprList;this.finishNode(val,\"SequenceExpression\");this.resetEndLocation(val,innerEndLoc);}else{val=exprList[0];}return this.wrapParenthesis(startLoc,val);}},{key:\"wrapParenthesis\",value:function wrapParenthesis(startLoc,expression){if(!(this.optionFlags&1024)){this.addExtra(expression,\"parenthesized\",true);this.addExtra(expression,\"parenStart\",startLoc.index);this.takeSurroundingComments(expression,startLoc.index,this.state.lastTokEndLoc.index);return expression;}var parenExpression=this.startNodeAt(startLoc);parenExpression.expression=expression;return this.finishNode(parenExpression,\"ParenthesizedExpression\");}},{key:\"shouldParseArrow\",value:function shouldParseArrow(params){return!this.canInsertSemicolon();}},{key:\"parseArrow\",value:function parseArrow(node){if(this.eat(19)){return node;}}},{key:\"parseParenItem\",value:function parseParenItem(node,startLoc){return node;}},{key:\"parseNewOrNewTarget\",value:function parseNewOrNewTarget(){var node=this.startNode();this.next();if(this.match(16)){var meta=this.createIdentifier(this.startNodeAtNode(node),\"new\");this.next();var metaProp=this.parseMetaProperty(node,meta,\"target\");if(!this.scope.allowNewTarget){this.raise(Errors.UnexpectedNewTarget,metaProp);}return metaProp;}return this.parseNew(node);}},{key:\"parseNew\",value:function parseNew(node){this.parseNewCallee(node);if(this.eat(10)){var args=this.parseExprList(11);this.toReferencedList(args);node.arguments=args;}else{node.arguments=[];}return this.finishNode(node,\"NewExpression\");}},{key:\"parseNewCallee\",value:function parseNewCallee(node){var isImport=this.match(83);var callee=this.parseNoCallExpr();node.callee=callee;if(isImport&&(callee.type===\"Import\"||callee.type===\"ImportExpression\")){this.raise(Errors.ImportCallNotNewExpression,callee);}}},{key:\"parseTemplateElement\",value:function parseTemplateElement(isTagged){var _this$state4=this.state,start=_this$state4.start,startLoc=_this$state4.startLoc,end=_this$state4.end,value=_this$state4.value;var elemStart=start+1;var elem=this.startNodeAt(createPositionWithColumnOffset(startLoc,1));if(value===null){if(!isTagged){this.raise(Errors.InvalidEscapeSequenceTemplate,createPositionWithColumnOffset(this.state.firstInvalidTemplateEscapePos,1));}}var isTail=this.match(24);var endOffset=isTail?-1:-2;var elemEnd=end+endOffset;elem.value={raw:this.input.slice(elemStart,elemEnd).replace(/\\r\\n?/g,\"\\n\"),cooked:value===null?null:value.slice(1,endOffset)};elem.tail=isTail;this.next();var finishedNode=this.finishNode(elem,\"TemplateElement\");this.resetEndLocation(finishedNode,createPositionWithColumnOffset(this.state.lastTokEndLoc,endOffset));return finishedNode;}},{key:\"parseTemplate\",value:function parseTemplate(isTagged){var node=this.startNode();var curElt=this.parseTemplateElement(isTagged);var quasis=[curElt];var substitutions=[];while(!curElt.tail){substitutions.push(this.parseTemplateSubstitution());this.readTemplateContinuation();quasis.push(curElt=this.parseTemplateElement(isTagged));}node.expressions=substitutions;node.quasis=quasis;return this.finishNode(node,\"TemplateLiteral\");}},{key:\"parseTemplateSubstitution\",value:function parseTemplateSubstitution(){return this.parseExpression();}},{key:\"parseObjectLike\",value:function parseObjectLike(close,isPattern,isRecord,refExpressionErrors){if(isRecord){this.expectPlugin(\"recordAndTuple\");}var oldInFSharpPipelineDirectBody=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=false;var sawProto=false;var first=true;var node=this.startNode();node.properties=[];this.next();while(!this.match(close)){if(first){first=false;}else{this.expect(12);if(this.match(close)){this.addTrailingCommaExtraToNode(node);break;}}var prop=void 0;if(isPattern){prop=this.parseBindingProperty();}else{prop=this.parsePropertyDefinition(refExpressionErrors);sawProto=this.checkProto(prop,isRecord,sawProto,refExpressionErrors);}if(isRecord&&!this.isObjectProperty(prop)&&prop.type!==\"SpreadElement\"){this.raise(Errors.InvalidRecordProperty,prop);}{if(prop.shorthand){this.addExtra(prop,\"shorthand\",true);}}node.properties.push(prop);}this.next();this.state.inFSharpPipelineDirectBody=oldInFSharpPipelineDirectBody;var type=\"ObjectExpression\";if(isPattern){type=\"ObjectPattern\";}else if(isRecord){type=\"RecordExpression\";}return this.finishNode(node,type);}},{key:\"addTrailingCommaExtraToNode\",value:function addTrailingCommaExtraToNode(node){this.addExtra(node,\"trailingComma\",this.state.lastTokStartLoc.index);this.addExtra(node,\"trailingCommaLoc\",this.state.lastTokStartLoc,false);}},{key:\"maybeAsyncOrAccessorProp\",value:function maybeAsyncOrAccessorProp(prop){return!prop.computed&&prop.key.type===\"Identifier\"&&(this.isLiteralPropertyName()||this.match(0)||this.match(55));}},{key:\"parsePropertyDefinition\",value:function parsePropertyDefinition(refExpressionErrors){var decorators=[];if(this.match(26)){if(this.hasPlugin(\"decorators\")){this.raise(Errors.UnsupportedPropertyDecorator,this.state.startLoc);}while(this.match(26)){decorators.push(this.parseDecorator());}}var prop=this.startNode();var isAsync=false;var isAccessor=false;var startLoc;if(this.match(21)){if(decorators.length)this.unexpected();return this.parseSpread();}if(decorators.length){prop.decorators=decorators;decorators=[];}prop.method=false;if(refExpressionErrors){startLoc=this.state.startLoc;}var isGenerator=this.eat(55);this.parsePropertyNamePrefixOperator(prop);var containsEsc=this.state.containsEsc;this.parsePropertyName(prop,refExpressionErrors);if(!isGenerator&&!containsEsc&&this.maybeAsyncOrAccessorProp(prop)){var key=prop.key;var keyName=key.name;if(keyName===\"async\"&&!this.hasPrecedingLineBreak()){isAsync=true;this.resetPreviousNodeTrailingComments(key);isGenerator=this.eat(55);this.parsePropertyName(prop);}if(keyName===\"get\"||keyName===\"set\"){isAccessor=true;this.resetPreviousNodeTrailingComments(key);prop.kind=keyName;if(this.match(55)){isGenerator=true;this.raise(Errors.AccessorIsGenerator,this.state.curPosition(),{kind:keyName});this.next();}this.parsePropertyName(prop);}}return this.parseObjPropValue(prop,startLoc,isGenerator,isAsync,false,isAccessor,refExpressionErrors);}},{key:\"getGetterSetterExpectedParamCount\",value:function getGetterSetterExpectedParamCount(method){return method.kind===\"get\"?0:1;}},{key:\"getObjectOrClassMethodParams\",value:function getObjectOrClassMethodParams(method){return method.params;}},{key:\"checkGetterSetterParams\",value:function checkGetterSetterParams(method){var _params;var paramCount=this.getGetterSetterExpectedParamCount(method);var params=this.getObjectOrClassMethodParams(method);if(params.length!==paramCount){this.raise(method.kind===\"get\"?Errors.BadGetterArity:Errors.BadSetterArity,method);}if(method.kind===\"set\"&&((_params=params[params.length-1])==null?void 0:_params.type)===\"RestElement\"){this.raise(Errors.BadSetterRestParameter,method);}}},{key:\"parseObjectMethod\",value:function parseObjectMethod(prop,isGenerator,isAsync,isPattern,isAccessor){if(isAccessor){var finishedProp=this.parseMethod(prop,isGenerator,false,false,false,\"ObjectMethod\");this.checkGetterSetterParams(finishedProp);return finishedProp;}if(isAsync||isGenerator||this.match(10)){if(isPattern)this.unexpected();prop.kind=\"method\";prop.method=true;return this.parseMethod(prop,isGenerator,isAsync,false,false,\"ObjectMethod\");}}},{key:\"parseObjectProperty\",value:function parseObjectProperty(prop,startLoc,isPattern,refExpressionErrors){prop.shorthand=false;if(this.eat(14)){prop.value=isPattern?this.parseMaybeDefault(this.state.startLoc):this.parseMaybeAssignAllowInOrVoidPattern(8,refExpressionErrors);return this.finishObjectProperty(prop);}if(!prop.computed&&prop.key.type===\"Identifier\"){this.checkReservedWord(prop.key.name,prop.key.loc.start,true,false);if(isPattern){prop.value=this.parseMaybeDefault(startLoc,this.cloneIdentifier(prop.key));}else if(this.match(29)){var shorthandAssignLoc=this.state.startLoc;if(refExpressionErrors!=null){if(refExpressionErrors.shorthandAssignLoc===null){refExpressionErrors.shorthandAssignLoc=shorthandAssignLoc;}}else{this.raise(Errors.InvalidCoverInitializedName,shorthandAssignLoc);}prop.value=this.parseMaybeDefault(startLoc,this.cloneIdentifier(prop.key));}else{prop.value=this.cloneIdentifier(prop.key);}prop.shorthand=true;return this.finishObjectProperty(prop);}}},{key:\"finishObjectProperty\",value:function finishObjectProperty(node){return this.finishNode(node,\"ObjectProperty\");}},{key:\"parseObjPropValue\",value:function parseObjPropValue(prop,startLoc,isGenerator,isAsync,isPattern,isAccessor,refExpressionErrors){var node=this.parseObjectMethod(prop,isGenerator,isAsync,isPattern,isAccessor)||this.parseObjectProperty(prop,startLoc,isPattern,refExpressionErrors);if(!node)this.unexpected();return node;}},{key:\"parsePropertyName\",value:function parsePropertyName(prop,refExpressionErrors){if(this.eat(0)){prop.computed=true;prop.key=this.parseMaybeAssignAllowIn();this.expect(3);}else{var _this$state5=this.state,type=_this$state5.type,value=_this$state5.value;var key;if(tokenIsKeywordOrIdentifier(type)){key=this.parseIdentifier(true);}else{switch(type){case 135:key=this.parseNumericLiteral(value);break;case 134:key=this.parseStringLiteral(value);break;case 136:key=this.parseBigIntLiteral(value);break;case 139:{var privateKeyLoc=this.state.startLoc;if(refExpressionErrors!=null){if(refExpressionErrors.privateKeyLoc===null){refExpressionErrors.privateKeyLoc=privateKeyLoc;}}else{this.raise(Errors.UnexpectedPrivateField,privateKeyLoc);}key=this.parsePrivateName();break;}default:if(type===137){key=this.parseDecimalLiteral(value);break;}this.unexpected();}}prop.key=key;if(type!==139){prop.computed=false;}}}},{key:\"initFunction\",value:function initFunction(node,isAsync){node.id=null;node.generator=false;node.async=isAsync;}},{key:\"parseMethod\",value:function parseMethod(node,isGenerator,isAsync,isConstructor,allowDirectSuper,type){var inClassScope=arguments.length>6&&arguments[6]!==undefined?arguments[6]:false;this.initFunction(node,isAsync);node.generator=isGenerator;this.scope.enter(514|16|(inClassScope?576:0)|(allowDirectSuper?32:0));this.prodParam.enter(functionFlags(isAsync,node.generator));this.parseFunctionParams(node,isConstructor);var finishedNode=this.parseFunctionBodyAndFinish(node,type,true);this.prodParam.exit();this.scope.exit();return finishedNode;}},{key:\"parseArrayLike\",value:function parseArrayLike(close,canBePattern,isTuple,refExpressionErrors){if(isTuple){this.expectPlugin(\"recordAndTuple\");}var oldInFSharpPipelineDirectBody=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=false;var node=this.startNode();this.next();node.elements=this.parseExprList(close,!isTuple,refExpressionErrors,node);this.state.inFSharpPipelineDirectBody=oldInFSharpPipelineDirectBody;return this.finishNode(node,isTuple?\"TupleExpression\":\"ArrayExpression\");}},{key:\"parseArrowExpression\",value:function parseArrowExpression(node,params,isAsync,trailingCommaLoc){this.scope.enter(514|4);var flags=functionFlags(isAsync,false);if(!this.match(5)&&this.prodParam.hasIn){flags|=8;}this.prodParam.enter(flags);this.initFunction(node,isAsync);var oldMaybeInArrowParameters=this.state.maybeInArrowParameters;if(params){this.state.maybeInArrowParameters=true;this.setArrowFunctionParameters(node,params,trailingCommaLoc);}this.state.maybeInArrowParameters=false;this.parseFunctionBody(node,true);this.prodParam.exit();this.scope.exit();this.state.maybeInArrowParameters=oldMaybeInArrowParameters;return this.finishNode(node,\"ArrowFunctionExpression\");}},{key:\"setArrowFunctionParameters\",value:function setArrowFunctionParameters(node,params,trailingCommaLoc){this.toAssignableList(params,trailingCommaLoc,false);node.params=params;}},{key:\"parseFunctionBodyAndFinish\",value:function parseFunctionBodyAndFinish(node,type){var isMethod=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;this.parseFunctionBody(node,false,isMethod);return this.finishNode(node,type);}},{key:\"parseFunctionBody\",value:function parseFunctionBody(node,allowExpression){var _this56=this;var isMethod=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;var isExpression=allowExpression&&!this.match(5);this.expressionScope.enter(newExpressionScope());if(isExpression){node.body=this.parseMaybeAssign();this.checkParams(node,false,allowExpression,false);}else{var oldStrict=this.state.strict;var oldLabels=this.state.labels;this.state.labels=[];this.prodParam.enter(this.prodParam.currentFlags()|4);node.body=this.parseBlock(true,false,function(hasStrictModeDirective){var nonSimple=!_this56.isSimpleParamList(node.params);if(hasStrictModeDirective&&nonSimple){_this56.raise(Errors.IllegalLanguageModeDirective,(node.kind===\"method\"||node.kind===\"constructor\")&&!!node.key?node.key.loc.end:node);}var strictModeChanged=!oldStrict&&_this56.state.strict;_this56.checkParams(node,!_this56.state.strict&&!allowExpression&&!isMethod&&!nonSimple,allowExpression,strictModeChanged);if(_this56.state.strict&&node.id){_this56.checkIdentifier(node.id,65,strictModeChanged);}});this.prodParam.exit();this.state.labels=oldLabels;}this.expressionScope.exit();}},{key:\"isSimpleParameter\",value:function isSimpleParameter(node){return node.type===\"Identifier\";}},{key:\"isSimpleParamList\",value:function isSimpleParamList(params){for(var i=0,len=params.length;i<len;i++){if(!this.isSimpleParameter(params[i]))return false;}return true;}},{key:\"checkParams\",value:function checkParams(node,allowDuplicates,isArrowFunction){var strictModeChanged=arguments.length>3&&arguments[3]!==undefined?arguments[3]:true;var checkClashes=!allowDuplicates&&new Set();var formalParameters={type:\"FormalParameters\"};var _iterator10=_createForOfIteratorHelper(node.params),_step10;try{for(_iterator10.s();!(_step10=_iterator10.n()).done;){var param=_step10.value;this.checkLVal(param,formalParameters,5,checkClashes,strictModeChanged);}}catch(err){_iterator10.e(err);}finally{_iterator10.f();}}},{key:\"parseExprList\",value:function parseExprList(close,allowEmpty,refExpressionErrors,nodeForExtra){var elts=[];var first=true;while(!this.eat(close)){if(first){first=false;}else{this.expect(12);if(this.match(close)){if(nodeForExtra){this.addTrailingCommaExtraToNode(nodeForExtra);}this.next();break;}}elts.push(this.parseExprListItem(close,allowEmpty,refExpressionErrors));}return elts;}},{key:\"parseExprListItem\",value:function parseExprListItem(close,allowEmpty,refExpressionErrors,allowPlaceholder){var elt;if(this.match(12)){if(!allowEmpty){this.raise(Errors.UnexpectedToken,this.state.curPosition(),{unexpected:\",\"});}elt=null;}else if(this.match(21)){var spreadNodeStartLoc=this.state.startLoc;elt=this.parseParenItem(this.parseSpread(refExpressionErrors),spreadNodeStartLoc);}else if(this.match(17)){this.expectPlugin(\"partialApplication\");if(!allowPlaceholder){this.raise(Errors.UnexpectedArgumentPlaceholder,this.state.startLoc);}var node=this.startNode();this.next();elt=this.finishNode(node,\"ArgumentPlaceholder\");}else{elt=this.parseMaybeAssignAllowInOrVoidPattern(close,refExpressionErrors,this.parseParenItem);}return elt;}},{key:\"parseIdentifier\",value:function parseIdentifier(liberal){var node=this.startNode();var name=this.parseIdentifierName(liberal);return this.createIdentifier(node,name);}},{key:\"createIdentifier\",value:function createIdentifier(node,name){node.name=name;node.loc.identifierName=name;return this.finishNode(node,\"Identifier\");}},{key:\"createIdentifierAt\",value:function createIdentifierAt(node,name,endLoc){node.name=name;node.loc.identifierName=name;return this.finishNodeAt(node,\"Identifier\",endLoc);}},{key:\"parseIdentifierName\",value:function parseIdentifierName(liberal){var name;var _this$state6=this.state,startLoc=_this$state6.startLoc,type=_this$state6.type;if(tokenIsKeywordOrIdentifier(type)){name=this.state.value;}else{this.unexpected();}var tokenIsKeyword=tokenKeywordOrIdentifierIsKeyword(type);if(liberal){if(tokenIsKeyword){this.replaceToken(132);}}else{this.checkReservedWord(name,startLoc,tokenIsKeyword,false);}this.next();return name;}},{key:\"checkReservedWord\",value:function checkReservedWord(word,startLoc,checkKeywords,isBinding){if(word.length>10){return;}if(!canBeReservedWord(word)){return;}if(checkKeywords&&isKeyword(word)){this.raise(Errors.UnexpectedKeyword,startLoc,{keyword:word});return;}var reservedTest=!this.state.strict?isReservedWord:isBinding?isStrictBindReservedWord:isStrictReservedWord;if(reservedTest(word,this.inModule)){this.raise(Errors.UnexpectedReservedWord,startLoc,{reservedWord:word});return;}else if(word===\"yield\"){if(this.prodParam.hasYield){this.raise(Errors.YieldBindingIdentifier,startLoc);return;}}else if(word===\"await\"){if(this.prodParam.hasAwait){this.raise(Errors.AwaitBindingIdentifier,startLoc);return;}if(this.scope.inStaticBlock){this.raise(Errors.AwaitBindingIdentifierInStaticBlock,startLoc);return;}this.expressionScope.recordAsyncArrowParametersError(startLoc);}else if(word===\"arguments\"){if(this.scope.inClassAndNotInNonArrowFunction){this.raise(Errors.ArgumentsInClass,startLoc);return;}}}},{key:\"recordAwaitIfAllowed\",value:function recordAwaitIfAllowed(){var isAwaitAllowed=this.prodParam.hasAwait;if(isAwaitAllowed&&!this.scope.inFunction){this.state.hasTopLevelAwait=true;}return isAwaitAllowed;}},{key:\"parseAwait\",value:function parseAwait(startLoc){var node=this.startNodeAt(startLoc);this.expressionScope.recordParameterInitializerError(Errors.AwaitExpressionFormalParameter,node);if(this.eat(55)){this.raise(Errors.ObsoleteAwaitStar,node);}if(!this.scope.inFunction&&!(this.optionFlags&1)){if(this.isAmbiguousPrefixOrIdentifier()){this.ambiguousScriptDifferentAst=true;}else{this.sawUnambiguousESM=true;}}if(!this.state.soloAwait){node.argument=this.parseMaybeUnary(null,true);}return this.finishNode(node,\"AwaitExpression\");}},{key:\"isAmbiguousPrefixOrIdentifier\",value:function isAmbiguousPrefixOrIdentifier(){if(this.hasPrecedingLineBreak())return true;var type=this.state.type;return type===53||type===10||type===0||tokenIsTemplate(type)||type===102&&!this.state.containsEsc||type===138||type===56||this.hasPlugin(\"v8intrinsic\")&&type===54;}},{key:\"parseYield\",value:function parseYield(startLoc){var node=this.startNodeAt(startLoc);this.expressionScope.recordParameterInitializerError(Errors.YieldInParameter,node);var delegating=false;var argument=null;if(!this.hasPrecedingLineBreak()){delegating=this.eat(55);switch(this.state.type){case 13:case 140:case 8:case 11:case 3:case 9:case 14:case 12:if(!delegating)break;default:argument=this.parseMaybeAssign();}}node.delegate=delegating;node.argument=argument;return this.finishNode(node,\"YieldExpression\");}},{key:\"parseImportCall\",value:function parseImportCall(node){this.next();node.source=this.parseMaybeAssignAllowIn();node.options=null;if(this.eat(12)){if(!this.match(11)){node.options=this.parseMaybeAssignAllowIn();if(this.eat(12)){this.addTrailingCommaExtraToNode(node.options);if(!this.match(11)){do{this.parseMaybeAssignAllowIn();}while(this.eat(12)&&!this.match(11));this.raise(Errors.ImportCallArity,node);}}}else{this.addTrailingCommaExtraToNode(node.source);}}this.expect(11);return this.finishNode(node,\"ImportExpression\");}},{key:\"checkPipelineAtInfixOperator\",value:function checkPipelineAtInfixOperator(left,leftStartLoc){if(this.hasPlugin([\"pipelineOperator\",{proposal:\"smart\"}])){if(left.type===\"SequenceExpression\"){this.raise(Errors.PipelineHeadSequenceExpression,leftStartLoc);}}}},{key:\"parseSmartPipelineBodyInStyle\",value:function parseSmartPipelineBodyInStyle(childExpr,startLoc){if(this.isSimpleReference(childExpr)){var bodyNode=this.startNodeAt(startLoc);bodyNode.callee=childExpr;return this.finishNode(bodyNode,\"PipelineBareFunction\");}else{var _bodyNode2=this.startNodeAt(startLoc);this.checkSmartPipeTopicBodyEarlyErrors(startLoc);_bodyNode2.expression=childExpr;return this.finishNode(_bodyNode2,\"PipelineTopicExpression\");}}},{key:\"isSimpleReference\",value:function isSimpleReference(expression){switch(expression.type){case\"MemberExpression\":return!expression.computed&&this.isSimpleReference(expression.object);case\"Identifier\":return true;default:return false;}}},{key:\"checkSmartPipeTopicBodyEarlyErrors\",value:function checkSmartPipeTopicBodyEarlyErrors(startLoc){if(this.match(19)){throw this.raise(Errors.PipelineBodyNoArrow,this.state.startLoc);}if(!this.topicReferenceWasUsedInCurrentContext()){this.raise(Errors.PipelineTopicUnused,startLoc);}}},{key:\"withTopicBindingContext\",value:function withTopicBindingContext(callback){var outerContextTopicState=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:1,maxTopicIndex:null};try{return callback();}finally{this.state.topicContext=outerContextTopicState;}}},{key:\"withSmartMixTopicForbiddingContext\",value:function withSmartMixTopicForbiddingContext(callback){if(this.hasPlugin([\"pipelineOperator\",{proposal:\"smart\"}])){var outerContextTopicState=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null};try{return callback();}finally{this.state.topicContext=outerContextTopicState;}}else{return callback();}}},{key:\"withSoloAwaitPermittingContext\",value:function withSoloAwaitPermittingContext(callback){var outerContextSoloAwaitState=this.state.soloAwait;this.state.soloAwait=true;try{return callback();}finally{this.state.soloAwait=outerContextSoloAwaitState;}}},{key:\"allowInAnd\",value:function allowInAnd(callback){var flags=this.prodParam.currentFlags();var prodParamToSet=8&~flags;if(prodParamToSet){this.prodParam.enter(flags|8);try{return callback();}finally{this.prodParam.exit();}}return callback();}},{key:\"disallowInAnd\",value:function disallowInAnd(callback){var flags=this.prodParam.currentFlags();var prodParamToClear=8&flags;if(prodParamToClear){this.prodParam.enter(flags&~8);try{return callback();}finally{this.prodParam.exit();}}return callback();}},{key:\"registerTopicReference\",value:function registerTopicReference(){this.state.topicContext.maxTopicIndex=0;}},{key:\"topicReferenceIsAllowedInCurrentContext\",value:function topicReferenceIsAllowedInCurrentContext(){return this.state.topicContext.maxNumOfResolvableTopics>=1;}},{key:\"topicReferenceWasUsedInCurrentContext\",value:function topicReferenceWasUsedInCurrentContext(){return this.state.topicContext.maxTopicIndex!=null&&this.state.topicContext.maxTopicIndex>=0;}},{key:\"parseFSharpPipelineBody\",value:function parseFSharpPipelineBody(prec){var startLoc=this.state.startLoc;this.state.potentialArrowAt=this.state.start;var oldInFSharpPipelineDirectBody=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=true;var ret=this.parseExprOp(this.parseMaybeUnaryOrPrivate(),startLoc,prec);this.state.inFSharpPipelineDirectBody=oldInFSharpPipelineDirectBody;return ret;}},{key:\"parseModuleExpression\",value:function parseModuleExpression(){this.expectPlugin(\"moduleBlocks\");var node=this.startNode();this.next();if(!this.match(5)){this.unexpected(null,5);}var program=this.startNodeAt(this.state.endLoc);this.next();var revertScopes=this.initializeScopes(true);this.enterInitialScopes();try{node.body=this.parseProgram(program,8,\"module\");}finally{revertScopes();}return this.finishNode(node,\"ModuleExpression\");}},{key:\"parseVoidPattern\",value:function parseVoidPattern(refExpressionErrors){this.expectPlugin(\"discardBinding\");var node=this.startNode();if(refExpressionErrors!=null){refExpressionErrors.voidPatternLoc=this.state.startLoc;}this.next();return this.finishNode(node,\"VoidPattern\");}},{key:\"parseMaybeAssignAllowInOrVoidPattern\",value:function parseMaybeAssignAllowInOrVoidPattern(close,refExpressionErrors,afterLeftParse){if(refExpressionErrors!=null&&this.match(88)){var nextCode=this.lookaheadCharCode();if(nextCode===44||nextCode===(close===3?93:close===8?125:41)||nextCode===61){return this.parseMaybeDefault(this.state.startLoc,this.parseVoidPattern(refExpressionErrors));}}return this.parseMaybeAssignAllowIn(refExpressionErrors,afterLeftParse);}},{key:\"parsePropertyNamePrefixOperator\",value:function parsePropertyNamePrefixOperator(prop){}}]);}(LValParser);var loopLabel={kind:1},switchLabel={kind:2};var loneSurrogate=/(?:[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])/;var keywordRelationalOperator=new RegExp(\"in(?:stanceof)?\",\"y\");function babel7CompatTokens(tokens,input,startIndex){for(var i=0;i<tokens.length;i++){var token=tokens[i];var type=token.type;if(typeof type===\"number\"){{if(type===139){var loc=token.loc,start=token.start,value=token.value,end=token.end;var hashEndPos=start+1;var hashEndLoc=createPositionWithColumnOffset(loc.start,1);tokens.splice(i,1,new Token({type:getExportedToken(27),value:\"#\",start:start,end:hashEndPos,startLoc:loc.start,endLoc:hashEndLoc}),new Token({type:getExportedToken(132),value:value,start:hashEndPos,end:end,startLoc:hashEndLoc,endLoc:loc.end}));i++;continue;}if(tokenIsTemplate(type)){var _loc=token.loc,_start=token.start,_value=token.value,_end=token.end;var backquoteEnd=_start+1;var backquoteEndLoc=createPositionWithColumnOffset(_loc.start,1);var startToken=void 0;if(input.charCodeAt(_start-startIndex)===96){startToken=new Token({type:getExportedToken(22),value:\"`\",start:_start,end:backquoteEnd,startLoc:_loc.start,endLoc:backquoteEndLoc});}else{startToken=new Token({type:getExportedToken(8),value:\"}\",start:_start,end:backquoteEnd,startLoc:_loc.start,endLoc:backquoteEndLoc});}var templateValue=void 0,templateElementEnd=void 0,templateElementEndLoc=void 0,endToken=void 0;if(type===24){templateElementEnd=_end-1;templateElementEndLoc=createPositionWithColumnOffset(_loc.end,-1);templateValue=_value===null?null:_value.slice(1,-1);endToken=new Token({type:getExportedToken(22),value:\"`\",start:templateElementEnd,end:_end,startLoc:templateElementEndLoc,endLoc:_loc.end});}else{templateElementEnd=_end-2;templateElementEndLoc=createPositionWithColumnOffset(_loc.end,-2);templateValue=_value===null?null:_value.slice(1,-2);endToken=new Token({type:getExportedToken(23),value:\"${\",start:templateElementEnd,end:_end,startLoc:templateElementEndLoc,endLoc:_loc.end});}tokens.splice(i,1,startToken,new Token({type:getExportedToken(20),value:templateValue,start:backquoteEnd,end:templateElementEnd,startLoc:backquoteEndLoc,endLoc:templateElementEndLoc}),endToken);i+=2;continue;}}token.type=getExportedToken(type);}}return tokens;}var StatementParser=/*#__PURE__*/function(_ExpressionParser){function StatementParser(){_classCallCheck(this,StatementParser);return _callSuper(this,StatementParser,arguments);}_inherits(StatementParser,_ExpressionParser);return _createClass(StatementParser,[{key:\"parseTopLevel\",value:function parseTopLevel(file,program){file.program=this.parseProgram(program,140,this.options.sourceType===\"module\"?\"module\":\"script\");file.comments=this.comments;if(this.optionFlags&256){file.tokens=babel7CompatTokens(this.tokens,this.input,this.startIndex);}return this.finishNode(file,\"File\");}},{key:\"parseProgram\",value:function parseProgram(program,end,sourceType){program.sourceType=sourceType;program.interpreter=this.parseInterpreterDirective();this.parseBlockBody(program,true,true,end);if(this.inModule){if(!(this.optionFlags&64)&&this.scope.undefinedExports.size>0){for(var _i5=0,_Array$from2=Array.from(this.scope.undefinedExports);_i5<_Array$from2.length;_i5++){var _Array$from2$_i=_slicedToArray(_Array$from2[_i5],2),localName=_Array$from2$_i[0],at=_Array$from2$_i[1];this.raise(Errors.ModuleExportUndefined,at,{localName:localName});}}this.addExtra(program,\"topLevelAwait\",this.state.hasTopLevelAwait);}var finishedProgram;if(end===140){finishedProgram=this.finishNode(program,\"Program\");}else{finishedProgram=this.finishNodeAt(program,\"Program\",createPositionWithColumnOffset(this.state.startLoc,-1));}return finishedProgram;}},{key:\"stmtToDirective\",value:function stmtToDirective(stmt){var directive=this.castNodeTo(stmt,\"Directive\");var directiveLiteral=this.castNodeTo(stmt.expression,\"DirectiveLiteral\");var expressionValue=directiveLiteral.value;var raw=this.input.slice(this.offsetToSourcePos(directiveLiteral.start),this.offsetToSourcePos(directiveLiteral.end));var val=directiveLiteral.value=raw.slice(1,-1);this.addExtra(directiveLiteral,\"raw\",raw);this.addExtra(directiveLiteral,\"rawValue\",val);this.addExtra(directiveLiteral,\"expressionValue\",expressionValue);directive.value=directiveLiteral;delete stmt.expression;return directive;}},{key:\"parseInterpreterDirective\",value:function parseInterpreterDirective(){if(!this.match(28)){return null;}var node=this.startNode();node.value=this.state.value;this.next();return this.finishNode(node,\"InterpreterDirective\");}},{key:\"isLet\",value:function isLet(){if(!this.isContextual(100)){return false;}return this.hasFollowingBindingAtom();}},{key:\"isUsing\",value:function isUsing(){if(!this.isContextual(107)){return false;}var next=this.nextTokenInLineStart();var nextCh=this.codePointAtPos(next);return this.chStartsBindingIdentifier(nextCh,next);}},{key:\"isForUsing\",value:function isForUsing(){if(!this.isContextual(107)){return false;}var next=this.nextTokenInLineStart();var nextCh=this.codePointAtPos(next);if(this.isUnparsedContextual(next,\"of\")){var nextCharAfterOf=this.lookaheadCharCodeSince(next+2);if(nextCharAfterOf!==61&&nextCharAfterOf!==58&&nextCharAfterOf!==59){return false;}}if(this.chStartsBindingIdentifier(nextCh,next)||this.isUnparsedContextual(next,\"void\")){return true;}return false;}},{key:\"isAwaitUsing\",value:function isAwaitUsing(){if(!this.isContextual(96)){return false;}var next=this.nextTokenInLineStart();if(this.isUnparsedContextual(next,\"using\")){next=this.nextTokenInLineStartSince(next+5);var nextCh=this.codePointAtPos(next);if(this.chStartsBindingIdentifier(nextCh,next)){return true;}}return false;}},{key:\"chStartsBindingIdentifier\",value:function chStartsBindingIdentifier(ch,pos){if(isIdentifierStart(ch)){keywordRelationalOperator.lastIndex=pos;if(keywordRelationalOperator.test(this.input)){var endCh=this.codePointAtPos(keywordRelationalOperator.lastIndex);if(!isIdentifierChar(endCh)&&endCh!==92){return false;}}return true;}else if(ch===92){return true;}else{return false;}}},{key:\"chStartsBindingPattern\",value:function chStartsBindingPattern(ch){return ch===91||ch===123;}},{key:\"hasFollowingBindingAtom\",value:function hasFollowingBindingAtom(){var next=this.nextTokenStart();var nextCh=this.codePointAtPos(next);return this.chStartsBindingPattern(nextCh)||this.chStartsBindingIdentifier(nextCh,next);}},{key:\"hasInLineFollowingBindingIdentifierOrBrace\",value:function hasInLineFollowingBindingIdentifierOrBrace(){var next=this.nextTokenInLineStart();var nextCh=this.codePointAtPos(next);return nextCh===123||this.chStartsBindingIdentifier(nextCh,next);}},{key:\"allowsUsing\",value:function allowsUsing(){return(this.scope.inModule||!this.scope.inTopLevel)&&!this.scope.inBareCaseStatement;}},{key:\"parseModuleItem\",value:function parseModuleItem(){return this.parseStatementLike(1|2|4|8);}},{key:\"parseStatementListItem\",value:function parseStatementListItem(){return this.parseStatementLike(2|4|(!this.options.annexB||this.state.strict?0:8));}},{key:\"parseStatementOrSloppyAnnexBFunctionDeclaration\",value:function parseStatementOrSloppyAnnexBFunctionDeclaration(){var allowLabeledFunction=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var flags=0;if(this.options.annexB&&!this.state.strict){flags|=4;if(allowLabeledFunction){flags|=8;}}return this.parseStatementLike(flags);}},{key:\"parseStatement\",value:function parseStatement(){return this.parseStatementLike(0);}},{key:\"parseStatementLike\",value:function parseStatementLike(flags){var decorators=null;if(this.match(26)){decorators=this.parseDecorators(true);}return this.parseStatementContent(flags,decorators);}},{key:\"parseStatementContent\",value:function parseStatementContent(flags,decorators){var startType=this.state.type;var node=this.startNode();var allowDeclaration=!!(flags&2);var allowFunctionDeclaration=!!(flags&4);var topLevel=flags&1;switch(startType){case 60:return this.parseBreakContinueStatement(node,true);case 63:return this.parseBreakContinueStatement(node,false);case 64:return this.parseDebuggerStatement(node);case 90:return this.parseDoWhileStatement(node);case 91:return this.parseForStatement(node);case 68:if(this.lookaheadCharCode()===46)break;if(!allowFunctionDeclaration){this.raise(this.state.strict?Errors.StrictFunction:this.options.annexB?Errors.SloppyFunctionAnnexB:Errors.SloppyFunction,this.state.startLoc);}return this.parseFunctionStatement(node,false,!allowDeclaration&&allowFunctionDeclaration);case 80:if(!allowDeclaration)this.unexpected();return this.parseClass(this.maybeTakeDecorators(decorators,node),true);case 69:return this.parseIfStatement(node);case 70:return this.parseReturnStatement(node);case 71:return this.parseSwitchStatement(node);case 72:return this.parseThrowStatement(node);case 73:return this.parseTryStatement(node);case 96:if(this.isAwaitUsing()){if(!this.allowsUsing()){this.raise(Errors.UnexpectedUsingDeclaration,node);}else if(!allowDeclaration){this.raise(Errors.UnexpectedLexicalDeclaration,node);}else if(!this.recordAwaitIfAllowed()){this.raise(Errors.AwaitUsingNotInAsyncContext,node);}this.next();return this.parseVarStatement(node,\"await using\");}break;case 107:if(this.state.containsEsc||!this.hasInLineFollowingBindingIdentifierOrBrace()){break;}if(!this.allowsUsing()){this.raise(Errors.UnexpectedUsingDeclaration,this.state.startLoc);}else if(!allowDeclaration){this.raise(Errors.UnexpectedLexicalDeclaration,this.state.startLoc);}return this.parseVarStatement(node,\"using\");case 100:{if(this.state.containsEsc){break;}var next=this.nextTokenStart();var nextCh=this.codePointAtPos(next);if(nextCh!==91){if(!allowDeclaration&&this.hasFollowingLineBreak())break;if(!this.chStartsBindingIdentifier(nextCh,next)&&nextCh!==123){break;}}}case 75:{if(!allowDeclaration){this.raise(Errors.UnexpectedLexicalDeclaration,this.state.startLoc);}}case 74:{var kind=this.state.value;return this.parseVarStatement(node,kind);}case 92:return this.parseWhileStatement(node);case 76:return this.parseWithStatement(node);case 5:return this.parseBlock();case 13:return this.parseEmptyStatement(node);case 83:{var nextTokenCharCode=this.lookaheadCharCode();if(nextTokenCharCode===40||nextTokenCharCode===46){break;}}case 82:{if(!(this.optionFlags&8)&&!topLevel){this.raise(Errors.UnexpectedImportExport,this.state.startLoc);}this.next();var result;if(startType===83){result=this.parseImport(node);}else{result=this.parseExport(node,decorators);}this.assertModuleNodeAllowed(result);return result;}default:{if(this.isAsyncFunction()){if(!allowDeclaration){this.raise(Errors.AsyncFunctionInSingleStatementContext,this.state.startLoc);}this.next();return this.parseFunctionStatement(node,true,!allowDeclaration&&allowFunctionDeclaration);}}}var maybeName=this.state.value;var expr=this.parseExpression();if(tokenIsIdentifier(startType)&&expr.type===\"Identifier\"&&this.eat(14)){return this.parseLabeledStatement(node,maybeName,expr,flags);}else{return this.parseExpressionStatement(node,expr,decorators);}}},{key:\"assertModuleNodeAllowed\",value:function assertModuleNodeAllowed(node){if(!(this.optionFlags&8)&&!this.inModule){this.raise(Errors.ImportOutsideModule,node);}}},{key:\"decoratorsEnabledBeforeExport\",value:function decoratorsEnabledBeforeExport(){if(this.hasPlugin(\"decorators-legacy\"))return true;return this.hasPlugin(\"decorators\")&&this.getPluginOption(\"decorators\",\"decoratorsBeforeExport\")!==false;}},{key:\"maybeTakeDecorators\",value:function maybeTakeDecorators(maybeDecorators,classNode,exportNode){if(maybeDecorators){var _classNode$decorators;if((_classNode$decorators=classNode.decorators)!=null&&_classNode$decorators.length){var _classNode$decorators2;if(typeof this.getPluginOption(\"decorators\",\"decoratorsBeforeExport\")!==\"boolean\"){this.raise(Errors.DecoratorsBeforeAfterExport,classNode.decorators[0]);}(_classNode$decorators2=classNode.decorators).unshift.apply(_classNode$decorators2,_toConsumableArray(maybeDecorators));}else{classNode.decorators=maybeDecorators;}this.resetStartLocationFromNode(classNode,maybeDecorators[0]);if(exportNode)this.resetStartLocationFromNode(exportNode,classNode);}return classNode;}},{key:\"canHaveLeadingDecorator\",value:function canHaveLeadingDecorator(){return this.match(80);}},{key:\"parseDecorators\",value:function parseDecorators(allowExport){var decorators=[];do{decorators.push(this.parseDecorator());}while(this.match(26));if(this.match(82)){if(!allowExport){this.unexpected();}if(!this.decoratorsEnabledBeforeExport()){this.raise(Errors.DecoratorExportClass,this.state.startLoc);}}else if(!this.canHaveLeadingDecorator()){throw this.raise(Errors.UnexpectedLeadingDecorator,this.state.startLoc);}return decorators;}},{key:\"parseDecorator\",value:function parseDecorator(){this.expectOnePlugin([\"decorators\",\"decorators-legacy\"]);var node=this.startNode();this.next();if(this.hasPlugin(\"decorators\")){var startLoc=this.state.startLoc;var expr;if(this.match(10)){var _startLoc=this.state.startLoc;this.next();expr=this.parseExpression();this.expect(11);expr=this.wrapParenthesis(_startLoc,expr);var paramsStartLoc=this.state.startLoc;node.expression=this.parseMaybeDecoratorArguments(expr,_startLoc);if(this.getPluginOption(\"decorators\",\"allowCallParenthesized\")===false&&node.expression!==expr){this.raise(Errors.DecoratorArgumentsOutsideParentheses,paramsStartLoc);}}else{expr=this.parseIdentifier(false);while(this.eat(16)){var _node12=this.startNodeAt(startLoc);_node12.object=expr;if(this.match(139)){this.classScope.usePrivateName(this.state.value,this.state.startLoc);_node12.property=this.parsePrivateName();}else{_node12.property=this.parseIdentifier(true);}_node12.computed=false;expr=this.finishNode(_node12,\"MemberExpression\");}node.expression=this.parseMaybeDecoratorArguments(expr,startLoc);}}else{node.expression=this.parseExprSubscripts();}return this.finishNode(node,\"Decorator\");}},{key:\"parseMaybeDecoratorArguments\",value:function parseMaybeDecoratorArguments(expr,startLoc){if(this.eat(10)){var node=this.startNodeAt(startLoc);node.callee=expr;node.arguments=this.parseCallExpressionArguments();this.toReferencedList(node.arguments);return this.finishNode(node,\"CallExpression\");}return expr;}},{key:\"parseBreakContinueStatement\",value:function parseBreakContinueStatement(node,isBreak){this.next();if(this.isLineTerminator()){node.label=null;}else{node.label=this.parseIdentifier();this.semicolon();}this.verifyBreakContinue(node,isBreak);return this.finishNode(node,isBreak?\"BreakStatement\":\"ContinueStatement\");}},{key:\"verifyBreakContinue\",value:function verifyBreakContinue(node,isBreak){var i;for(i=0;i<this.state.labels.length;++i){var lab=this.state.labels[i];if(node.label==null||lab.name===node.label.name){if(lab.kind!=null&&(isBreak||lab.kind===1)){break;}if(node.label&&isBreak)break;}}if(i===this.state.labels.length){var type=isBreak?\"BreakStatement\":\"ContinueStatement\";this.raise(Errors.IllegalBreakContinue,node,{type:type});}}},{key:\"parseDebuggerStatement\",value:function parseDebuggerStatement(node){this.next();this.semicolon();return this.finishNode(node,\"DebuggerStatement\");}},{key:\"parseHeaderExpression\",value:function parseHeaderExpression(){this.expect(10);var val=this.parseExpression();this.expect(11);return val;}},{key:\"parseDoWhileStatement\",value:function parseDoWhileStatement(node){var _this57=this;this.next();this.state.labels.push(loopLabel);node.body=this.withSmartMixTopicForbiddingContext(function(){return _this57.parseStatement();});this.state.labels.pop();this.expect(92);node.test=this.parseHeaderExpression();this.eat(13);return this.finishNode(node,\"DoWhileStatement\");}},{key:\"parseForStatement\",value:function parseForStatement(node){this.next();this.state.labels.push(loopLabel);var awaitAt=null;if(this.isContextual(96)&&this.recordAwaitIfAllowed()){awaitAt=this.state.startLoc;this.next();}this.scope.enter(0);this.expect(10);if(this.match(13)){if(awaitAt!==null){this.unexpected(awaitAt);}return this.parseFor(node,null);}var startsWithLet=this.isContextual(100);{var startsWithAwaitUsing=this.isAwaitUsing();var starsWithUsingDeclaration=startsWithAwaitUsing||this.isForUsing();var isLetOrUsing=startsWithLet&&this.hasFollowingBindingAtom()||starsWithUsingDeclaration;if(this.match(74)||this.match(75)||isLetOrUsing){var initNode=this.startNode();var kind;if(startsWithAwaitUsing){kind=\"await using\";if(!this.recordAwaitIfAllowed()){this.raise(Errors.AwaitUsingNotInAsyncContext,this.state.startLoc);}this.next();}else{kind=this.state.value;}this.next();this.parseVar(initNode,true,kind);var _init=this.finishNode(initNode,\"VariableDeclaration\");var isForIn=this.match(58);if(isForIn&&starsWithUsingDeclaration){this.raise(Errors.ForInUsing,_init);}if((isForIn||this.isContextual(102))&&_init.declarations.length===1){return this.parseForIn(node,_init,awaitAt);}if(awaitAt!==null){this.unexpected(awaitAt);}return this.parseFor(node,_init);}}var startsWithAsync=this.isContextual(95);var refExpressionErrors=new ExpressionErrors();var init=this.parseExpression(true,refExpressionErrors);var isForOf=this.isContextual(102);if(isForOf){if(startsWithLet){this.raise(Errors.ForOfLet,init);}if(awaitAt===null&&startsWithAsync&&init.type===\"Identifier\"){this.raise(Errors.ForOfAsync,init);}}if(isForOf||this.match(58)){this.checkDestructuringPrivate(refExpressionErrors);this.toAssignable(init,true);var type=isForOf?\"ForOfStatement\":\"ForInStatement\";this.checkLVal(init,{type:type});return this.parseForIn(node,init,awaitAt);}else{this.checkExpressionErrors(refExpressionErrors,true);}if(awaitAt!==null){this.unexpected(awaitAt);}return this.parseFor(node,init);}},{key:\"parseFunctionStatement\",value:function parseFunctionStatement(node,isAsync,isHangingDeclaration){this.next();return this.parseFunction(node,1|(isHangingDeclaration?2:0)|(isAsync?8:0));}},{key:\"parseIfStatement\",value:function parseIfStatement(node){this.next();node.test=this.parseHeaderExpression();node.consequent=this.parseStatementOrSloppyAnnexBFunctionDeclaration();node.alternate=this.eat(66)?this.parseStatementOrSloppyAnnexBFunctionDeclaration():null;return this.finishNode(node,\"IfStatement\");}},{key:\"parseReturnStatement\",value:function parseReturnStatement(node){if(!this.prodParam.hasReturn){this.raise(Errors.IllegalReturn,this.state.startLoc);}this.next();if(this.isLineTerminator()){node.argument=null;}else{node.argument=this.parseExpression();this.semicolon();}return this.finishNode(node,\"ReturnStatement\");}},{key:\"parseSwitchStatement\",value:function parseSwitchStatement(node){this.next();node.discriminant=this.parseHeaderExpression();var cases=node.cases=[];this.expect(5);this.state.labels.push(switchLabel);this.scope.enter(256);var cur;for(var sawDefault;!this.match(8);){if(this.match(61)||this.match(65)){var isCase=this.match(61);if(cur)this.finishNode(cur,\"SwitchCase\");cases.push(cur=this.startNode());cur.consequent=[];this.next();if(isCase){cur.test=this.parseExpression();}else{if(sawDefault){this.raise(Errors.MultipleDefaultsInSwitch,this.state.lastTokStartLoc);}sawDefault=true;cur.test=null;}this.expect(14);}else{if(cur){cur.consequent.push(this.parseStatementListItem());}else{this.unexpected();}}}this.scope.exit();if(cur)this.finishNode(cur,\"SwitchCase\");this.next();this.state.labels.pop();return this.finishNode(node,\"SwitchStatement\");}},{key:\"parseThrowStatement\",value:function parseThrowStatement(node){this.next();if(this.hasPrecedingLineBreak()){this.raise(Errors.NewlineAfterThrow,this.state.lastTokEndLoc);}node.argument=this.parseExpression();this.semicolon();return this.finishNode(node,\"ThrowStatement\");}},{key:\"parseCatchClauseParam\",value:function parseCatchClauseParam(){var param=this.parseBindingAtom();this.scope.enter(this.options.annexB&&param.type===\"Identifier\"?8:0);this.checkLVal(param,{type:\"CatchClause\"},9);return param;}},{key:\"parseTryStatement\",value:function parseTryStatement(node){var _this58=this;this.next();node.block=this.parseBlock();node.handler=null;if(this.match(62)){var clause=this.startNode();this.next();if(this.match(10)){this.expect(10);clause.param=this.parseCatchClauseParam();this.expect(11);}else{clause.param=null;this.scope.enter(0);}clause.body=this.withSmartMixTopicForbiddingContext(function(){return _this58.parseBlock(false,false);});this.scope.exit();node.handler=this.finishNode(clause,\"CatchClause\");}node.finalizer=this.eat(67)?this.parseBlock():null;if(!node.handler&&!node.finalizer){this.raise(Errors.NoCatchOrFinally,node);}return this.finishNode(node,\"TryStatement\");}},{key:\"parseVarStatement\",value:function parseVarStatement(node,kind){var allowMissingInitializer=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;this.next();this.parseVar(node,false,kind,allowMissingInitializer);this.semicolon();return this.finishNode(node,\"VariableDeclaration\");}},{key:\"parseWhileStatement\",value:function parseWhileStatement(node){var _this59=this;this.next();node.test=this.parseHeaderExpression();this.state.labels.push(loopLabel);node.body=this.withSmartMixTopicForbiddingContext(function(){return _this59.parseStatement();});this.state.labels.pop();return this.finishNode(node,\"WhileStatement\");}},{key:\"parseWithStatement\",value:function parseWithStatement(node){var _this60=this;if(this.state.strict){this.raise(Errors.StrictWith,this.state.startLoc);}this.next();node.object=this.parseHeaderExpression();node.body=this.withSmartMixTopicForbiddingContext(function(){return _this60.parseStatement();});return this.finishNode(node,\"WithStatement\");}},{key:\"parseEmptyStatement\",value:function parseEmptyStatement(node){this.next();return this.finishNode(node,\"EmptyStatement\");}},{key:\"parseLabeledStatement\",value:function parseLabeledStatement(node,maybeName,expr,flags){var _iterator11=_createForOfIteratorHelper(this.state.labels),_step11;try{for(_iterator11.s();!(_step11=_iterator11.n()).done;){var _label=_step11.value;if(_label.name===maybeName){this.raise(Errors.LabelRedeclaration,expr,{labelName:maybeName});}}}catch(err){_iterator11.e(err);}finally{_iterator11.f();}var kind=tokenIsLoop(this.state.type)?1:this.match(71)?2:null;for(var i=this.state.labels.length-1;i>=0;i--){var label=this.state.labels[i];if(label.statementStart===node.start){label.statementStart=this.sourceToOffsetPos(this.state.start);label.kind=kind;}else{break;}}this.state.labels.push({name:maybeName,kind:kind,statementStart:this.sourceToOffsetPos(this.state.start)});node.body=flags&8?this.parseStatementOrSloppyAnnexBFunctionDeclaration(true):this.parseStatement();this.state.labels.pop();node.label=expr;return this.finishNode(node,\"LabeledStatement\");}},{key:\"parseExpressionStatement\",value:function parseExpressionStatement(node,expr,decorators){node.expression=expr;this.semicolon();return this.finishNode(node,\"ExpressionStatement\");}},{key:\"parseBlock\",value:function parseBlock(){var allowDirectives=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var createNewLexicalScope=arguments.length>1&&arguments[1]!==undefined?arguments[1]:true;var afterBlockParse=arguments.length>2?arguments[2]:undefined;var node=this.startNode();if(allowDirectives){this.state.strictErrors.clear();}this.expect(5);if(createNewLexicalScope){this.scope.enter(0);}this.parseBlockBody(node,allowDirectives,false,8,afterBlockParse);if(createNewLexicalScope){this.scope.exit();}return this.finishNode(node,\"BlockStatement\");}},{key:\"isValidDirective\",value:function isValidDirective(stmt){return stmt.type===\"ExpressionStatement\"&&stmt.expression.type===\"StringLiteral\"&&!stmt.expression.extra.parenthesized;}},{key:\"parseBlockBody\",value:function parseBlockBody(node,allowDirectives,topLevel,end,afterBlockParse){var body=node.body=[];var directives=node.directives=[];this.parseBlockOrModuleBlockBody(body,allowDirectives?directives:undefined,topLevel,end,afterBlockParse);}},{key:\"parseBlockOrModuleBlockBody\",value:function parseBlockOrModuleBlockBody(body,directives,topLevel,end,afterBlockParse){var oldStrict=this.state.strict;var hasStrictModeDirective=false;var parsedNonDirective=false;while(!this.match(end)){var stmt=topLevel?this.parseModuleItem():this.parseStatementListItem();if(directives&&!parsedNonDirective){if(this.isValidDirective(stmt)){var directive=this.stmtToDirective(stmt);directives.push(directive);if(!hasStrictModeDirective&&directive.value.value===\"use strict\"){hasStrictModeDirective=true;this.setStrict(true);}continue;}parsedNonDirective=true;this.state.strictErrors.clear();}body.push(stmt);}afterBlockParse==null||afterBlockParse.call(this,hasStrictModeDirective);if(!oldStrict){this.setStrict(false);}this.next();}},{key:\"parseFor\",value:function parseFor(node,init){var _this61=this;node.init=init;this.semicolon(false);node.test=this.match(13)?null:this.parseExpression();this.semicolon(false);node.update=this.match(11)?null:this.parseExpression();this.expect(11);node.body=this.withSmartMixTopicForbiddingContext(function(){return _this61.parseStatement();});this.scope.exit();this.state.labels.pop();return this.finishNode(node,\"ForStatement\");}},{key:\"parseForIn\",value:function parseForIn(node,init,awaitAt){var _this62=this;var isForIn=this.match(58);this.next();if(isForIn){if(awaitAt!==null)this.unexpected(awaitAt);}else{node.await=awaitAt!==null;}if(init.type===\"VariableDeclaration\"&&init.declarations[0].init!=null&&(!isForIn||!this.options.annexB||this.state.strict||init.kind!==\"var\"||init.declarations[0].id.type!==\"Identifier\")){this.raise(Errors.ForInOfLoopInitializer,init,{type:isForIn?\"ForInStatement\":\"ForOfStatement\"});}if(init.type===\"AssignmentPattern\"){this.raise(Errors.InvalidLhs,init,{ancestor:{type:\"ForStatement\"}});}node.left=init;node.right=isForIn?this.parseExpression():this.parseMaybeAssignAllowIn();this.expect(11);node.body=this.withSmartMixTopicForbiddingContext(function(){return _this62.parseStatement();});this.scope.exit();this.state.labels.pop();return this.finishNode(node,isForIn?\"ForInStatement\":\"ForOfStatement\");}},{key:\"parseVar\",value:function parseVar(node,isFor,kind){var allowMissingInitializer=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;var declarations=node.declarations=[];node.kind=kind;for(;;){var decl=this.startNode();this.parseVarId(decl,kind);decl.init=!this.eat(29)?null:isFor?this.parseMaybeAssignDisallowIn():this.parseMaybeAssignAllowIn();if(decl.init===null&&!allowMissingInitializer){if(decl.id.type!==\"Identifier\"&&!(isFor&&(this.match(58)||this.isContextual(102)))){this.raise(Errors.DeclarationMissingInitializer,this.state.lastTokEndLoc,{kind:\"destructuring\"});}else if((kind===\"const\"||kind===\"using\"||kind===\"await using\")&&!(this.match(58)||this.isContextual(102))){this.raise(Errors.DeclarationMissingInitializer,this.state.lastTokEndLoc,{kind:kind});}}declarations.push(this.finishNode(decl,\"VariableDeclarator\"));if(!this.eat(12))break;}return node;}},{key:\"parseVarId\",value:function parseVarId(decl,kind){var id=this.parseBindingAtom();if(kind===\"using\"||kind===\"await using\"){if(id.type===\"ArrayPattern\"||id.type===\"ObjectPattern\"){this.raise(Errors.UsingDeclarationHasBindingPattern,id.loc.start);}}else{if(id.type===\"VoidPattern\"){this.raise(Errors.UnexpectedVoidPattern,id.loc.start);}}this.checkLVal(id,{type:\"VariableDeclarator\"},kind===\"var\"?5:8201);decl.id=id;}},{key:\"parseAsyncFunctionExpression\",value:function parseAsyncFunctionExpression(node){return this.parseFunction(node,8);}},{key:\"parseFunction\",value:function parseFunction(node){var _this63=this;var flags=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var hangingDeclaration=flags&2;var isDeclaration=!!(flags&1);var requireId=isDeclaration&&!(flags&4);var isAsync=!!(flags&8);this.initFunction(node,isAsync);if(this.match(55)){if(hangingDeclaration){this.raise(Errors.GeneratorInSingleStatementContext,this.state.startLoc);}this.next();node.generator=true;}if(isDeclaration){node.id=this.parseFunctionId(requireId);}var oldMaybeInArrowParameters=this.state.maybeInArrowParameters;this.state.maybeInArrowParameters=false;this.scope.enter(514);this.prodParam.enter(functionFlags(isAsync,node.generator));if(!isDeclaration){node.id=this.parseFunctionId();}this.parseFunctionParams(node,false);this.withSmartMixTopicForbiddingContext(function(){_this63.parseFunctionBodyAndFinish(node,isDeclaration?\"FunctionDeclaration\":\"FunctionExpression\");});this.prodParam.exit();this.scope.exit();if(isDeclaration&&!hangingDeclaration){this.registerFunctionStatementId(node);}this.state.maybeInArrowParameters=oldMaybeInArrowParameters;return node;}},{key:\"parseFunctionId\",value:function parseFunctionId(requireId){return requireId||tokenIsIdentifier(this.state.type)?this.parseIdentifier():null;}},{key:\"parseFunctionParams\",value:function parseFunctionParams(node,isConstructor){this.expect(10);this.expressionScope.enter(newParameterDeclarationScope());node.params=this.parseBindingList(11,41,2|(isConstructor?4:0));this.expressionScope.exit();}},{key:\"registerFunctionStatementId\",value:function registerFunctionStatementId(node){if(!node.id)return;this.scope.declareName(node.id.name,!this.options.annexB||this.state.strict||node.generator||node.async?this.scope.treatFunctionsAsVar?5:8201:17,node.id.loc.start);}},{key:\"parseClass\",value:function parseClass(node,isStatement,optionalId){this.next();var oldStrict=this.state.strict;this.state.strict=true;this.parseClassId(node,isStatement,optionalId);this.parseClassSuper(node);node.body=this.parseClassBody(!!node.superClass,oldStrict);return this.finishNode(node,isStatement?\"ClassDeclaration\":\"ClassExpression\");}},{key:\"isClassProperty\",value:function isClassProperty(){return this.match(29)||this.match(13)||this.match(8);}},{key:\"isClassMethod\",value:function isClassMethod(){return this.match(10);}},{key:\"nameIsConstructor\",value:function nameIsConstructor(key){return key.type===\"Identifier\"&&key.name===\"constructor\"||key.type===\"StringLiteral\"&&key.value===\"constructor\";}},{key:\"isNonstaticConstructor\",value:function isNonstaticConstructor(method){return!method.computed&&!method.static&&this.nameIsConstructor(method.key);}},{key:\"parseClassBody\",value:function parseClassBody(hadSuperClass,oldStrict){var _this64=this;this.classScope.enter();var state={hadConstructor:false,hadSuperClass:hadSuperClass};var decorators=[];var classBody=this.startNode();classBody.body=[];this.expect(5);this.withSmartMixTopicForbiddingContext(function(){while(!_this64.match(8)){if(_this64.eat(13)){if(decorators.length>0){throw _this64.raise(Errors.DecoratorSemicolon,_this64.state.lastTokEndLoc);}continue;}if(_this64.match(26)){decorators.push(_this64.parseDecorator());continue;}var member=_this64.startNode();if(decorators.length){member.decorators=decorators;_this64.resetStartLocationFromNode(member,decorators[0]);decorators=[];}_this64.parseClassMember(classBody,member,state);if(member.kind===\"constructor\"&&member.decorators&&member.decorators.length>0){_this64.raise(Errors.DecoratorConstructor,member);}}});this.state.strict=oldStrict;this.next();if(decorators.length){throw this.raise(Errors.TrailingDecorator,this.state.startLoc);}this.classScope.exit();return this.finishNode(classBody,\"ClassBody\");}},{key:\"parseClassMemberFromModifier\",value:function parseClassMemberFromModifier(classBody,member){var key=this.parseIdentifier(true);if(this.isClassMethod()){var method=member;method.kind=\"method\";method.computed=false;method.key=key;method.static=false;this.pushClassMethod(classBody,method,false,false,false,false);return true;}else if(this.isClassProperty()){var prop=member;prop.computed=false;prop.key=key;prop.static=false;classBody.body.push(this.parseClassProperty(prop));return true;}this.resetPreviousNodeTrailingComments(key);return false;}},{key:\"parseClassMember\",value:function parseClassMember(classBody,member,state){var isStatic=this.isContextual(106);if(isStatic){if(this.parseClassMemberFromModifier(classBody,member)){return;}if(this.eat(5)){this.parseClassStaticBlock(classBody,member);return;}}this.parseClassMemberWithIsStatic(classBody,member,state,isStatic);}},{key:\"parseClassMemberWithIsStatic\",value:function parseClassMemberWithIsStatic(classBody,member,state,isStatic){var publicMethod=member;var privateMethod=member;var publicProp=member;var privateProp=member;var accessorProp=member;var method=publicMethod;var publicMember=publicMethod;member.static=isStatic;this.parsePropertyNamePrefixOperator(member);if(this.eat(55)){method.kind=\"method\";var isPrivateName=this.match(139);this.parseClassElementName(method);this.parsePostMemberNameModifiers(method);if(isPrivateName){this.pushClassPrivateMethod(classBody,privateMethod,true,false);return;}if(this.isNonstaticConstructor(publicMethod)){this.raise(Errors.ConstructorIsGenerator,publicMethod.key);}this.pushClassMethod(classBody,publicMethod,true,false,false,false);return;}var isContextual=!this.state.containsEsc&&tokenIsIdentifier(this.state.type);var key=this.parseClassElementName(member);var maybeContextualKw=isContextual?key.name:null;var isPrivate=this.isPrivateName(key);var maybeQuestionTokenStartLoc=this.state.startLoc;this.parsePostMemberNameModifiers(publicMember);if(this.isClassMethod()){method.kind=\"method\";if(isPrivate){this.pushClassPrivateMethod(classBody,privateMethod,false,false);return;}var isConstructor=this.isNonstaticConstructor(publicMethod);var allowsDirectSuper=false;if(isConstructor){publicMethod.kind=\"constructor\";if(state.hadConstructor&&!this.hasPlugin(\"typescript\")){this.raise(Errors.DuplicateConstructor,key);}if(isConstructor&&this.hasPlugin(\"typescript\")&&member.override){this.raise(Errors.OverrideOnConstructor,key);}state.hadConstructor=true;allowsDirectSuper=state.hadSuperClass;}this.pushClassMethod(classBody,publicMethod,false,false,isConstructor,allowsDirectSuper);}else if(this.isClassProperty()){if(isPrivate){this.pushClassPrivateProperty(classBody,privateProp);}else{this.pushClassProperty(classBody,publicProp);}}else if(maybeContextualKw===\"async\"&&!this.isLineTerminator()){this.resetPreviousNodeTrailingComments(key);var isGenerator=this.eat(55);if(publicMember.optional){this.unexpected(maybeQuestionTokenStartLoc);}method.kind=\"method\";var _isPrivate=this.match(139);this.parseClassElementName(method);this.parsePostMemberNameModifiers(publicMember);if(_isPrivate){this.pushClassPrivateMethod(classBody,privateMethod,isGenerator,true);}else{if(this.isNonstaticConstructor(publicMethod)){this.raise(Errors.ConstructorIsAsync,publicMethod.key);}this.pushClassMethod(classBody,publicMethod,isGenerator,true,false,false);}}else if((maybeContextualKw===\"get\"||maybeContextualKw===\"set\")&&!(this.match(55)&&this.isLineTerminator())){this.resetPreviousNodeTrailingComments(key);method.kind=maybeContextualKw;var _isPrivate2=this.match(139);this.parseClassElementName(publicMethod);if(_isPrivate2){this.pushClassPrivateMethod(classBody,privateMethod,false,false);}else{if(this.isNonstaticConstructor(publicMethod)){this.raise(Errors.ConstructorIsAccessor,publicMethod.key);}this.pushClassMethod(classBody,publicMethod,false,false,false,false);}this.checkGetterSetterParams(publicMethod);}else if(maybeContextualKw===\"accessor\"&&!this.isLineTerminator()){this.expectPlugin(\"decoratorAutoAccessors\");this.resetPreviousNodeTrailingComments(key);var _isPrivate3=this.match(139);this.parseClassElementName(publicProp);this.pushClassAccessorProperty(classBody,accessorProp,_isPrivate3);}else if(this.isLineTerminator()){if(isPrivate){this.pushClassPrivateProperty(classBody,privateProp);}else{this.pushClassProperty(classBody,publicProp);}}else{this.unexpected();}}},{key:\"parseClassElementName\",value:function parseClassElementName(member){var _this$state7=this.state,type=_this$state7.type,value=_this$state7.value;if((type===132||type===134)&&member.static&&value===\"prototype\"){this.raise(Errors.StaticPrototype,this.state.startLoc);}if(type===139){if(value===\"constructor\"){this.raise(Errors.ConstructorClassPrivateField,this.state.startLoc);}var key=this.parsePrivateName();member.key=key;return key;}this.parsePropertyName(member);return member.key;}},{key:\"parseClassStaticBlock\",value:function parseClassStaticBlock(classBody,member){var _member$decorators;this.scope.enter(576|128|16);var oldLabels=this.state.labels;this.state.labels=[];this.prodParam.enter(0);var body=member.body=[];this.parseBlockOrModuleBlockBody(body,undefined,false,8);this.prodParam.exit();this.scope.exit();this.state.labels=oldLabels;classBody.body.push(this.finishNode(member,\"StaticBlock\"));if((_member$decorators=member.decorators)!=null&&_member$decorators.length){this.raise(Errors.DecoratorStaticBlock,member);}}},{key:\"pushClassProperty\",value:function pushClassProperty(classBody,prop){if(!prop.computed&&this.nameIsConstructor(prop.key)){this.raise(Errors.ConstructorClassField,prop.key);}classBody.body.push(this.parseClassProperty(prop));}},{key:\"pushClassPrivateProperty\",value:function pushClassPrivateProperty(classBody,prop){var node=this.parseClassPrivateProperty(prop);classBody.body.push(node);this.classScope.declarePrivateName(this.getPrivateNameSV(node.key),0,node.key.loc.start);}},{key:\"pushClassAccessorProperty\",value:function pushClassAccessorProperty(classBody,prop,isPrivate){if(!isPrivate&&!prop.computed&&this.nameIsConstructor(prop.key)){this.raise(Errors.ConstructorClassField,prop.key);}var node=this.parseClassAccessorProperty(prop);classBody.body.push(node);if(isPrivate){this.classScope.declarePrivateName(this.getPrivateNameSV(node.key),0,node.key.loc.start);}}},{key:\"pushClassMethod\",value:function pushClassMethod(classBody,method,isGenerator,isAsync,isConstructor,allowsDirectSuper){classBody.body.push(this.parseMethod(method,isGenerator,isAsync,isConstructor,allowsDirectSuper,\"ClassMethod\",true));}},{key:\"pushClassPrivateMethod\",value:function pushClassPrivateMethod(classBody,method,isGenerator,isAsync){var node=this.parseMethod(method,isGenerator,isAsync,false,false,\"ClassPrivateMethod\",true);classBody.body.push(node);var kind=node.kind===\"get\"?node.static?6:2:node.kind===\"set\"?node.static?5:1:0;this.declareClassPrivateMethodInScope(node,kind);}},{key:\"declareClassPrivateMethodInScope\",value:function declareClassPrivateMethodInScope(node,kind){this.classScope.declarePrivateName(this.getPrivateNameSV(node.key),kind,node.key.loc.start);}},{key:\"parsePostMemberNameModifiers\",value:function parsePostMemberNameModifiers(methodOrProp){}},{key:\"parseClassPrivateProperty\",value:function parseClassPrivateProperty(node){this.parseInitializer(node);this.semicolon();return this.finishNode(node,\"ClassPrivateProperty\");}},{key:\"parseClassProperty\",value:function parseClassProperty(node){this.parseInitializer(node);this.semicolon();return this.finishNode(node,\"ClassProperty\");}},{key:\"parseClassAccessorProperty\",value:function parseClassAccessorProperty(node){this.parseInitializer(node);this.semicolon();return this.finishNode(node,\"ClassAccessorProperty\");}},{key:\"parseInitializer\",value:function parseInitializer(node){this.scope.enter(576|16);this.expressionScope.enter(newExpressionScope());this.prodParam.enter(0);node.value=this.eat(29)?this.parseMaybeAssignAllowIn():null;this.expressionScope.exit();this.prodParam.exit();this.scope.exit();}},{key:\"parseClassId\",value:function parseClassId(node,isStatement,optionalId){var bindingType=arguments.length>3&&arguments[3]!==undefined?arguments[3]:8331;if(tokenIsIdentifier(this.state.type)){node.id=this.parseIdentifier();if(isStatement){this.declareNameFromIdentifier(node.id,bindingType);}}else{if(optionalId||!isStatement){node.id=null;}else{throw this.raise(Errors.MissingClassName,this.state.startLoc);}}}},{key:\"parseClassSuper\",value:function parseClassSuper(node){node.superClass=this.eat(81)?this.parseExprSubscripts():null;}},{key:\"parseExport\",value:function parseExport(node,decorators){var maybeDefaultIdentifier=this.parseMaybeImportPhase(node,true);var hasDefault=this.maybeParseExportDefaultSpecifier(node,maybeDefaultIdentifier);var parseAfterDefault=!hasDefault||this.eat(12);var hasStar=parseAfterDefault&&this.eatExportStar(node);var hasNamespace=hasStar&&this.maybeParseExportNamespaceSpecifier(node);var parseAfterNamespace=parseAfterDefault&&(!hasNamespace||this.eat(12));var isFromRequired=hasDefault||hasStar;if(hasStar&&!hasNamespace){if(hasDefault)this.unexpected();if(decorators){throw this.raise(Errors.UnsupportedDecoratorExport,node);}this.parseExportFrom(node,true);this.sawUnambiguousESM=true;return this.finishNode(node,\"ExportAllDeclaration\");}var hasSpecifiers=this.maybeParseExportNamedSpecifiers(node);if(hasDefault&&parseAfterDefault&&!hasStar&&!hasSpecifiers){this.unexpected(null,5);}if(hasNamespace&&parseAfterNamespace){this.unexpected(null,98);}var hasDeclaration;if(isFromRequired||hasSpecifiers){hasDeclaration=false;if(decorators){throw this.raise(Errors.UnsupportedDecoratorExport,node);}this.parseExportFrom(node,isFromRequired);}else{hasDeclaration=this.maybeParseExportDeclaration(node);}if(isFromRequired||hasSpecifiers||hasDeclaration){var _node2$declaration;var node2=node;this.checkExport(node2,true,false,!!node2.source);if(((_node2$declaration=node2.declaration)==null?void 0:_node2$declaration.type)===\"ClassDeclaration\"){this.maybeTakeDecorators(decorators,node2.declaration,node2);}else if(decorators){throw this.raise(Errors.UnsupportedDecoratorExport,node);}this.sawUnambiguousESM=true;return this.finishNode(node2,\"ExportNamedDeclaration\");}if(this.eat(65)){var _node13=node;var decl=this.parseExportDefaultExpression();_node13.declaration=decl;if(decl.type===\"ClassDeclaration\"){this.maybeTakeDecorators(decorators,decl,_node13);}else if(decorators){throw this.raise(Errors.UnsupportedDecoratorExport,node);}this.checkExport(_node13,true,true);this.sawUnambiguousESM=true;return this.finishNode(_node13,\"ExportDefaultDeclaration\");}this.unexpected(null,5);}},{key:\"eatExportStar\",value:function eatExportStar(node){return this.eat(55);}},{key:\"maybeParseExportDefaultSpecifier\",value:function maybeParseExportDefaultSpecifier(node,maybeDefaultIdentifier){if(maybeDefaultIdentifier||this.isExportDefaultSpecifier()){this.expectPlugin(\"exportDefaultFrom\",maybeDefaultIdentifier==null?void 0:maybeDefaultIdentifier.loc.start);var id=maybeDefaultIdentifier||this.parseIdentifier(true);var specifier=this.startNodeAtNode(id);specifier.exported=id;node.specifiers=[this.finishNode(specifier,\"ExportDefaultSpecifier\")];return true;}return false;}},{key:\"maybeParseExportNamespaceSpecifier\",value:function maybeParseExportNamespaceSpecifier(node){if(this.isContextual(93)){var _ref,_ref$specifiers;(_ref$specifiers=(_ref=node).specifiers)!=null?_ref$specifiers:_ref.specifiers=[];var specifier=this.startNodeAt(this.state.lastTokStartLoc);this.next();specifier.exported=this.parseModuleExportName();node.specifiers.push(this.finishNode(specifier,\"ExportNamespaceSpecifier\"));return true;}return false;}},{key:\"maybeParseExportNamedSpecifiers\",value:function maybeParseExportNamedSpecifiers(node){if(this.match(5)){var _node2$specifiers;var node2=node;if(!node2.specifiers)node2.specifiers=[];var isTypeExport=node2.exportKind===\"type\";(_node2$specifiers=node2.specifiers).push.apply(_node2$specifiers,_toConsumableArray(this.parseExportSpecifiers(isTypeExport)));node2.source=null;if(this.hasPlugin(\"importAssertions\")){node2.assertions=[];}else{node2.attributes=[];}node2.declaration=null;return true;}return false;}},{key:\"maybeParseExportDeclaration\",value:function maybeParseExportDeclaration(node){if(this.shouldParseExportDeclaration()){node.specifiers=[];node.source=null;if(this.hasPlugin(\"importAssertions\")){node.assertions=[];}else{node.attributes=[];}node.declaration=this.parseExportDeclaration(node);return true;}return false;}},{key:\"isAsyncFunction\",value:function isAsyncFunction(){if(!this.isContextual(95))return false;var next=this.nextTokenInLineStart();return this.isUnparsedContextual(next,\"function\");}},{key:\"parseExportDefaultExpression\",value:function parseExportDefaultExpression(){var expr=this.startNode();if(this.match(68)){this.next();return this.parseFunction(expr,1|4);}else if(this.isAsyncFunction()){this.next();this.next();return this.parseFunction(expr,1|4|8);}if(this.match(80)){return this.parseClass(expr,true,true);}if(this.match(26)){if(this.hasPlugin(\"decorators\")&&this.getPluginOption(\"decorators\",\"decoratorsBeforeExport\")===true){this.raise(Errors.DecoratorBeforeExport,this.state.startLoc);}return this.parseClass(this.maybeTakeDecorators(this.parseDecorators(false),this.startNode()),true,true);}if(this.match(75)||this.match(74)||this.isLet()||this.isUsing()||this.isAwaitUsing()){throw this.raise(Errors.UnsupportedDefaultExport,this.state.startLoc);}var res=this.parseMaybeAssignAllowIn();this.semicolon();return res;}},{key:\"parseExportDeclaration\",value:function parseExportDeclaration(node){if(this.match(80)){var _node14=this.parseClass(this.startNode(),true,false);return _node14;}return this.parseStatementListItem();}},{key:\"isExportDefaultSpecifier\",value:function isExportDefaultSpecifier(){var type=this.state.type;if(tokenIsIdentifier(type)){if(type===95&&!this.state.containsEsc||type===100){return false;}if((type===130||type===129)&&!this.state.containsEsc){var _next=this.nextTokenStart();var nextChar=this.input.charCodeAt(_next);if(nextChar===123||this.chStartsBindingIdentifier(nextChar,_next)&&!this.input.startsWith(\"from\",_next)){this.expectOnePlugin([\"flow\",\"typescript\"]);return false;}}}else if(!this.match(65)){return false;}var next=this.nextTokenStart();var hasFrom=this.isUnparsedContextual(next,\"from\");if(this.input.charCodeAt(next)===44||tokenIsIdentifier(this.state.type)&&hasFrom){return true;}if(this.match(65)&&hasFrom){var nextAfterFrom=this.input.charCodeAt(this.nextTokenStartSince(next+4));return nextAfterFrom===34||nextAfterFrom===39;}return false;}},{key:\"parseExportFrom\",value:function parseExportFrom(node,expect){if(this.eatContextual(98)){node.source=this.parseImportSource();this.checkExport(node);this.maybeParseImportAttributes(node);this.checkJSONModuleImport(node);}else if(expect){this.unexpected();}this.semicolon();}},{key:\"shouldParseExportDeclaration\",value:function shouldParseExportDeclaration(){var type=this.state.type;if(type===26){this.expectOnePlugin([\"decorators\",\"decorators-legacy\"]);if(this.hasPlugin(\"decorators\")){if(this.getPluginOption(\"decorators\",\"decoratorsBeforeExport\")===true){this.raise(Errors.DecoratorBeforeExport,this.state.startLoc);}return true;}}if(this.isUsing()){this.raise(Errors.UsingDeclarationExport,this.state.startLoc);return true;}if(this.isAwaitUsing()){this.raise(Errors.UsingDeclarationExport,this.state.startLoc);return true;}return type===74||type===75||type===68||type===80||this.isLet()||this.isAsyncFunction();}},{key:\"checkExport\",value:function checkExport(node,checkNames,isDefault,isFrom){if(checkNames){var _node$specifiers;if(isDefault){this.checkDuplicateExports(node,\"default\");if(this.hasPlugin(\"exportDefaultFrom\")){var _declaration$extra;var declaration=node.declaration;if(declaration.type===\"Identifier\"&&declaration.name===\"from\"&&declaration.end-declaration.start===4&&!((_declaration$extra=declaration.extra)!=null&&_declaration$extra.parenthesized)){this.raise(Errors.ExportDefaultFromAsIdentifier,declaration);}}}else if((_node$specifiers=node.specifiers)!=null&&_node$specifiers.length){var _iterator12=_createForOfIteratorHelper(node.specifiers),_step12;try{for(_iterator12.s();!(_step12=_iterator12.n()).done;){var specifier=_step12.value;var exported=specifier.exported;var exportName=exported.type===\"Identifier\"?exported.name:exported.value;this.checkDuplicateExports(specifier,exportName);if(!isFrom&&specifier.local){var local=specifier.local;if(local.type!==\"Identifier\"){this.raise(Errors.ExportBindingIsString,specifier,{localName:local.value,exportName:exportName});}else{this.checkReservedWord(local.name,local.loc.start,true,false);this.scope.checkLocalExport(local);}}}}catch(err){_iterator12.e(err);}finally{_iterator12.f();}}else if(node.declaration){var decl=node.declaration;if(decl.type===\"FunctionDeclaration\"||decl.type===\"ClassDeclaration\"){var id=decl.id;if(!id)throw new Error(\"Assertion failure\");this.checkDuplicateExports(node,id.name);}else if(decl.type===\"VariableDeclaration\"){var _iterator13=_createForOfIteratorHelper(decl.declarations),_step13;try{for(_iterator13.s();!(_step13=_iterator13.n()).done;){var _declaration=_step13.value;this.checkDeclaration(_declaration.id);}}catch(err){_iterator13.e(err);}finally{_iterator13.f();}}}}}},{key:\"checkDeclaration\",value:function checkDeclaration(node){if(node.type===\"Identifier\"){this.checkDuplicateExports(node,node.name);}else if(node.type===\"ObjectPattern\"){var _iterator14=_createForOfIteratorHelper(node.properties),_step14;try{for(_iterator14.s();!(_step14=_iterator14.n()).done;){var prop=_step14.value;this.checkDeclaration(prop);}}catch(err){_iterator14.e(err);}finally{_iterator14.f();}}else if(node.type===\"ArrayPattern\"){var _iterator15=_createForOfIteratorHelper(node.elements),_step15;try{for(_iterator15.s();!(_step15=_iterator15.n()).done;){var elem=_step15.value;if(elem){this.checkDeclaration(elem);}}}catch(err){_iterator15.e(err);}finally{_iterator15.f();}}else if(node.type===\"ObjectProperty\"){this.checkDeclaration(node.value);}else if(node.type===\"RestElement\"){this.checkDeclaration(node.argument);}else if(node.type===\"AssignmentPattern\"){this.checkDeclaration(node.left);}}},{key:\"checkDuplicateExports\",value:function checkDuplicateExports(node,exportName){if(this.exportedIdentifiers.has(exportName)){if(exportName===\"default\"){this.raise(Errors.DuplicateDefaultExport,node);}else{this.raise(Errors.DuplicateExport,node,{exportName:exportName});}}this.exportedIdentifiers.add(exportName);}},{key:\"parseExportSpecifiers\",value:function parseExportSpecifiers(isInTypeExport){var nodes=[];var first=true;this.expect(5);while(!this.eat(8)){if(first){first=false;}else{this.expect(12);if(this.eat(8))break;}var isMaybeTypeOnly=this.isContextual(130);var isString=this.match(134);var node=this.startNode();node.local=this.parseModuleExportName();nodes.push(this.parseExportSpecifier(node,isString,isInTypeExport,isMaybeTypeOnly));}return nodes;}},{key:\"parseExportSpecifier\",value:function parseExportSpecifier(node,isString,isInTypeExport,isMaybeTypeOnly){if(this.eatContextual(93)){node.exported=this.parseModuleExportName();}else if(isString){node.exported=this.cloneStringLiteral(node.local);}else if(!node.exported){node.exported=this.cloneIdentifier(node.local);}return this.finishNode(node,\"ExportSpecifier\");}},{key:\"parseModuleExportName\",value:function parseModuleExportName(){if(this.match(134)){var result=this.parseStringLiteral(this.state.value);var surrogate=loneSurrogate.exec(result.value);if(surrogate){this.raise(Errors.ModuleExportNameHasLoneSurrogate,result,{surrogateCharCode:surrogate[0].charCodeAt(0)});}return result;}return this.parseIdentifier(true);}},{key:\"isJSONModuleImport\",value:function isJSONModuleImport(node){if(node.assertions!=null){return node.assertions.some(function(_ref80){var key=_ref80.key,value=_ref80.value;return value.value===\"json\"&&(key.type===\"Identifier\"?key.name===\"type\":key.value===\"type\");});}return false;}},{key:\"checkImportReflection\",value:function checkImportReflection(node){var specifiers=node.specifiers;var singleBindingType=specifiers.length===1?specifiers[0].type:null;if(node.phase===\"source\"){if(singleBindingType!==\"ImportDefaultSpecifier\"){this.raise(Errors.SourcePhaseImportRequiresDefault,specifiers[0].loc.start);}}else if(node.phase===\"defer\"){if(singleBindingType!==\"ImportNamespaceSpecifier\"){this.raise(Errors.DeferImportRequiresNamespace,specifiers[0].loc.start);}}else if(node.module){var _node$assertions;if(singleBindingType!==\"ImportDefaultSpecifier\"){this.raise(Errors.ImportReflectionNotBinding,specifiers[0].loc.start);}if(((_node$assertions=node.assertions)==null?void 0:_node$assertions.length)>0){this.raise(Errors.ImportReflectionHasAssertion,specifiers[0].loc.start);}}}},{key:\"checkJSONModuleImport\",value:function checkJSONModuleImport(node){if(this.isJSONModuleImport(node)&&node.type!==\"ExportAllDeclaration\"){var specifiers=node.specifiers;if(specifiers!=null){var nonDefaultNamedSpecifier=specifiers.find(function(specifier){var imported;if(specifier.type===\"ExportSpecifier\"){imported=specifier.local;}else if(specifier.type===\"ImportSpecifier\"){imported=specifier.imported;}if(imported!==undefined){return imported.type===\"Identifier\"?imported.name!==\"default\":imported.value!==\"default\";}});if(nonDefaultNamedSpecifier!==undefined){this.raise(Errors.ImportJSONBindingNotDefault,nonDefaultNamedSpecifier.loc.start);}}}}},{key:\"isPotentialImportPhase\",value:function isPotentialImportPhase(isExport){if(isExport)return false;return this.isContextual(105)||this.isContextual(97)||this.isContextual(127);}},{key:\"applyImportPhase\",value:function applyImportPhase(node,isExport,phase,loc){if(isExport){return;}if(phase===\"module\"){this.expectPlugin(\"importReflection\",loc);node.module=true;}else if(this.hasPlugin(\"importReflection\")){node.module=false;}if(phase===\"source\"){this.expectPlugin(\"sourcePhaseImports\",loc);node.phase=\"source\";}else if(phase===\"defer\"){this.expectPlugin(\"deferredImportEvaluation\",loc);node.phase=\"defer\";}else if(this.hasPlugin(\"sourcePhaseImports\")){node.phase=null;}}},{key:\"parseMaybeImportPhase\",value:function parseMaybeImportPhase(node,isExport){if(!this.isPotentialImportPhase(isExport)){this.applyImportPhase(node,isExport,null);return null;}var phaseIdentifier=this.startNode();var phaseIdentifierName=this.parseIdentifierName(true);var type=this.state.type;var isImportPhase=tokenIsKeywordOrIdentifier(type)?type!==98||this.lookaheadCharCode()===102:type!==12;if(isImportPhase){this.applyImportPhase(node,isExport,phaseIdentifierName,phaseIdentifier.loc.start);return null;}else{this.applyImportPhase(node,isExport,null);return this.createIdentifier(phaseIdentifier,phaseIdentifierName);}}},{key:\"isPrecedingIdImportPhase\",value:function isPrecedingIdImportPhase(phase){var type=this.state.type;return tokenIsIdentifier(type)?type!==98||this.lookaheadCharCode()===102:type!==12;}},{key:\"parseImport\",value:function parseImport(node){if(this.match(134)){return this.parseImportSourceAndAttributes(node);}return this.parseImportSpecifiersAndAfter(node,this.parseMaybeImportPhase(node,false));}},{key:\"parseImportSpecifiersAndAfter\",value:function parseImportSpecifiersAndAfter(node,maybeDefaultIdentifier){node.specifiers=[];var hasDefault=this.maybeParseDefaultImportSpecifier(node,maybeDefaultIdentifier);var parseNext=!hasDefault||this.eat(12);var hasStar=parseNext&&this.maybeParseStarImportSpecifier(node);if(parseNext&&!hasStar)this.parseNamedImportSpecifiers(node);this.expectContextual(98);return this.parseImportSourceAndAttributes(node);}},{key:\"parseImportSourceAndAttributes\",value:function parseImportSourceAndAttributes(node){var _node$specifiers2;(_node$specifiers2=node.specifiers)!=null?_node$specifiers2:node.specifiers=[];node.source=this.parseImportSource();this.maybeParseImportAttributes(node);this.checkImportReflection(node);this.checkJSONModuleImport(node);this.semicolon();this.sawUnambiguousESM=true;return this.finishNode(node,\"ImportDeclaration\");}},{key:\"parseImportSource\",value:function parseImportSource(){if(!this.match(134))this.unexpected();return this.parseExprAtom();}},{key:\"parseImportSpecifierLocal\",value:function parseImportSpecifierLocal(node,specifier,type){specifier.local=this.parseIdentifier();node.specifiers.push(this.finishImportSpecifier(specifier,type));}},{key:\"finishImportSpecifier\",value:function finishImportSpecifier(specifier,type){var bindingType=arguments.length>2&&arguments[2]!==undefined?arguments[2]:8201;this.checkLVal(specifier.local,{type:type},bindingType);return this.finishNode(specifier,type);}},{key:\"parseImportAttributes\",value:function parseImportAttributes(){this.expect(5);var attrs=[];var attrNames=new Set();do{if(this.match(8)){break;}var node=this.startNode();var keyName=this.state.value;if(attrNames.has(keyName)){this.raise(Errors.ModuleAttributesWithDuplicateKeys,this.state.startLoc,{key:keyName});}attrNames.add(keyName);if(this.match(134)){node.key=this.parseStringLiteral(keyName);}else{node.key=this.parseIdentifier(true);}this.expect(14);if(!this.match(134)){throw this.raise(Errors.ModuleAttributeInvalidValue,this.state.startLoc);}node.value=this.parseStringLiteral(this.state.value);attrs.push(this.finishNode(node,\"ImportAttribute\"));}while(this.eat(12));this.expect(8);return attrs;}},{key:\"parseModuleAttributes\",value:function parseModuleAttributes(){var attrs=[];var attributes=new Set();do{var node=this.startNode();node.key=this.parseIdentifier(true);if(node.key.name!==\"type\"){this.raise(Errors.ModuleAttributeDifferentFromType,node.key);}if(attributes.has(node.key.name)){this.raise(Errors.ModuleAttributesWithDuplicateKeys,node.key,{key:node.key.name});}attributes.add(node.key.name);this.expect(14);if(!this.match(134)){throw this.raise(Errors.ModuleAttributeInvalidValue,this.state.startLoc);}node.value=this.parseStringLiteral(this.state.value);attrs.push(this.finishNode(node,\"ImportAttribute\"));}while(this.eat(12));return attrs;}},{key:\"maybeParseImportAttributes\",value:function maybeParseImportAttributes(node){var attributes;{var useWith=false;}if(this.match(76)){if(this.hasPrecedingLineBreak()&&this.lookaheadCharCode()===40){return;}this.next();if(this.hasPlugin(\"moduleAttributes\")){attributes=this.parseModuleAttributes();this.addExtra(node,\"deprecatedWithLegacySyntax\",true);}else{attributes=this.parseImportAttributes();}{useWith=true;}}else if(this.isContextual(94)&&!this.hasPrecedingLineBreak()){if(!this.hasPlugin(\"deprecatedImportAssert\")&&!this.hasPlugin(\"importAssertions\")){this.raise(Errors.ImportAttributesUseAssert,this.state.startLoc);}if(!this.hasPlugin(\"importAssertions\")){this.addExtra(node,\"deprecatedAssertSyntax\",true);}this.next();attributes=this.parseImportAttributes();}else{attributes=[];}if(!useWith&&this.hasPlugin(\"importAssertions\")){node.assertions=attributes;}else{node.attributes=attributes;}}},{key:\"maybeParseDefaultImportSpecifier\",value:function maybeParseDefaultImportSpecifier(node,maybeDefaultIdentifier){if(maybeDefaultIdentifier){var specifier=this.startNodeAtNode(maybeDefaultIdentifier);specifier.local=maybeDefaultIdentifier;node.specifiers.push(this.finishImportSpecifier(specifier,\"ImportDefaultSpecifier\"));return true;}else if(tokenIsKeywordOrIdentifier(this.state.type)){this.parseImportSpecifierLocal(node,this.startNode(),\"ImportDefaultSpecifier\");return true;}return false;}},{key:\"maybeParseStarImportSpecifier\",value:function maybeParseStarImportSpecifier(node){if(this.match(55)){var specifier=this.startNode();this.next();this.expectContextual(93);this.parseImportSpecifierLocal(node,specifier,\"ImportNamespaceSpecifier\");return true;}return false;}},{key:\"parseNamedImportSpecifiers\",value:function parseNamedImportSpecifiers(node){var first=true;this.expect(5);while(!this.eat(8)){if(first){first=false;}else{if(this.eat(14)){throw this.raise(Errors.DestructureNamedImport,this.state.startLoc);}this.expect(12);if(this.eat(8))break;}var specifier=this.startNode();var importedIsString=this.match(134);var isMaybeTypeOnly=this.isContextual(130);specifier.imported=this.parseModuleExportName();var importSpecifier=this.parseImportSpecifier(specifier,importedIsString,node.importKind===\"type\"||node.importKind===\"typeof\",isMaybeTypeOnly,undefined);node.specifiers.push(importSpecifier);}}},{key:\"parseImportSpecifier\",value:function parseImportSpecifier(specifier,importedIsString,isInTypeOnlyImport,isMaybeTypeOnly,bindingType){if(this.eatContextual(93)){specifier.local=this.parseIdentifier();}else{var imported=specifier.imported;if(importedIsString){throw this.raise(Errors.ImportBindingIsString,specifier,{importName:imported.value});}this.checkReservedWord(imported.name,specifier.loc.start,true,true);if(!specifier.local){specifier.local=this.cloneIdentifier(imported);}}return this.finishImportSpecifier(specifier,\"ImportSpecifier\",bindingType);}},{key:\"isThisParam\",value:function isThisParam(param){return param.type===\"Identifier\"&&param.name===\"this\";}}]);}(ExpressionParser);var Parser=/*#__PURE__*/function(_StatementParser){function Parser(options,input,pluginsMap){var _this65;_classCallCheck(this,Parser);options=getOptions(options);_this65=_callSuper(this,Parser,[options,input]);_this65.options=options;_this65.initializeScopes();_this65.plugins=pluginsMap;_this65.filename=options.sourceFilename;_this65.startIndex=options.startIndex;var optionFlags=0;if(options.allowAwaitOutsideFunction){optionFlags|=1;}if(options.allowReturnOutsideFunction){optionFlags|=2;}if(options.allowImportExportEverywhere){optionFlags|=8;}if(options.allowSuperOutsideMethod){optionFlags|=16;}if(options.allowUndeclaredExports){optionFlags|=64;}if(options.allowNewTargetOutsideFunction){optionFlags|=4;}if(options.allowYieldOutsideFunction){optionFlags|=32;}if(options.ranges){optionFlags|=128;}if(options.tokens){optionFlags|=256;}if(options.createImportExpressions){optionFlags|=512;}if(options.createParenthesizedExpressions){optionFlags|=1024;}if(options.errorRecovery){optionFlags|=2048;}if(options.attachComment){optionFlags|=4096;}if(options.annexB){optionFlags|=8192;}_this65.optionFlags=optionFlags;return _this65;}_inherits(Parser,_StatementParser);return _createClass(Parser,[{key:\"getScopeHandler\",value:function getScopeHandler(){return ScopeHandler;}},{key:\"parse\",value:function parse(){this.enterInitialScopes();var file=this.startNode();var program=this.startNode();this.nextToken();file.errors=null;this.parseTopLevel(file,program);file.errors=this.state.errors;file.comments.length=this.state.commentsLen;return file;}}]);}(StatementParser);function parse(input,options){var _options;if(((_options=options)==null?void 0:_options.sourceType)===\"unambiguous\"){options=Object.assign({},options);try{options.sourceType=\"module\";var parser=getParser(options,input);var ast=parser.parse();if(parser.sawUnambiguousESM){return ast;}if(parser.ambiguousScriptDifferentAst){try{options.sourceType=\"script\";return getParser(options,input).parse();}catch(_unused){}}else{ast.program.sourceType=\"script\";}return ast;}catch(moduleError){try{options.sourceType=\"script\";return getParser(options,input).parse();}catch(_unused2){}throw moduleError;}}else{return getParser(options,input).parse();}}function parseExpression(input,options){var parser=getParser(options,input);if(parser.options.strictMode){parser.state.strict=true;}return parser.getExpression();}function generateExportedTokenTypes(internalTokenTypes){var tokenTypes={};for(var _i6=0,_Object$keys4=Object.keys(internalTokenTypes);_i6<_Object$keys4.length;_i6++){var typeName=_Object$keys4[_i6];tokenTypes[typeName]=getExportedToken(internalTokenTypes[typeName]);}return tokenTypes;}var tokTypes=generateExportedTokenTypes(tt);function getParser(options,input){var cls=Parser;var pluginsMap=new Map();if(options!=null&&options.plugins){var _iterator16=_createForOfIteratorHelper(options.plugins),_step16;try{for(_iterator16.s();!(_step16=_iterator16.n()).done;){var plugin=_step16.value;var name=void 0,opts=void 0;if(typeof plugin===\"string\"){name=plugin;}else{var _plugin=_slicedToArray(plugin,2);name=_plugin[0];opts=_plugin[1];}if(!pluginsMap.has(name)){pluginsMap.set(name,opts||{});}}}catch(err){_iterator16.e(err);}finally{_iterator16.f();}validatePlugins(pluginsMap);cls=getParserClass(pluginsMap);}return new cls(options,input,pluginsMap);}var parserClassCache=new Map();function getParserClass(pluginsMap){var pluginList=[];var _iterator17=_createForOfIteratorHelper(mixinPluginNames),_step17;try{for(_iterator17.s();!(_step17=_iterator17.n()).done;){var name=_step17.value;if(pluginsMap.has(name)){pluginList.push(name);}}}catch(err){_iterator17.e(err);}finally{_iterator17.f();}var key=pluginList.join(\"|\");var cls=parserClassCache.get(key);if(!cls){cls=Parser;var _iterator18=_createForOfIteratorHelper(pluginList),_step18;try{for(_iterator18.s();!(_step18=_iterator18.n()).done;){var plugin=_step18.value;cls=mixinPlugins[plugin](cls);}}catch(err){_iterator18.e(err);}finally{_iterator18.f();}parserClassCache.set(key,cls);}return cls;}exports.parse=parse;exports.parseExpression=parseExpression;exports.tokTypes=tokTypes;", null]}