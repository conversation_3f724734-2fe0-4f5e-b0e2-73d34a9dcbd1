{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\AppMain.vue?vue&type=template&id=078753dd&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\AppMain.vue", "mtime": 1754050582335}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<section class=\"app-main\">\n  <transition name=\"fade-transform\" mode=\"out-in\">\n    <keep-alive :include=\"cachedViews\">\n      <router-view :key=\"key\" />\n    </keep-alive>\n  </transition>\n  <div class=\"footers\">\n    <el-link\n      v-for=\"item in links\"\n      :key=\"item.key\"\n      :href=\"item.href\"\n      target=\"_blank\"\n      class=\"mr15 mb20\"\n      >{{ item.title }}</el-link\n    >\n    <div class=\"title mb15\" v-html=\"$t('appMain.copyright')\"></div>\n  </div>\n</section>\n", null]}