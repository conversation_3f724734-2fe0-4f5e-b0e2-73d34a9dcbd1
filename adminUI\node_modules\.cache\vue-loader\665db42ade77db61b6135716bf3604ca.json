{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\adminList\\edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\adminList\\edit.vue", "mtime": 1754050582500}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport * as roleApi from \"@/api/role.js\";\r\nimport * as systemAdminApi from \"@/api/systemadmin.js\";\r\nimport { Debounce } from \"@/utils/validate\";\r\nexport default {\r\n  // name: \"edit\"\r\n  components: {},\r\n  props: {\r\n    isCreate: {\r\n      type: Number,\r\n      required: true\r\n    },\r\n    editData: {\r\n      type: Object,\r\n      default: () => {\r\n        return { rules: [] };\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    const validatePhone = (rule, value, callback) => {\r\n      if (!value) {\r\n        return callback(\r\n          new Error(this.$t(\"admin.system.admin.validatePhone.required\"))\r\n        );\r\n      } else if (!/^1[3456789]\\d{9}$/.test(value)) {\r\n        callback(\r\n          new Error(this.$t(\"admin.system.admin.validatePhone.formatError\"))\r\n        );\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    const validatePass = (rule, value, callback) => {\r\n      if (value === \"\") {\r\n        callback(\r\n          new Error(this.$t(\"admin.system.admin.validatePass.required\"))\r\n        );\r\n      } else if (value !== this.pram.pwd) {\r\n        callback(\r\n          new Error(this.$t(\"admin.system.admin.validatePass.notMatch\"))\r\n        );\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n      constants: this.$constants,\r\n      pram: {\r\n        account: null,\r\n        level: null,\r\n        pwd: null,\r\n        repwd: null,\r\n        realName: null,\r\n        roles: [],\r\n        status: null,\r\n        id: null,\r\n        phone: null\r\n      },\r\n      roleList: [],\r\n      rules: {\r\n        account: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"admin.system.admin.validateAccount.required\"),\r\n            trigger: [\"blur\", \"change\"]\r\n          }\r\n        ],\r\n        pwd: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"admin.system.admin.validatePassword.required\"),\r\n            trigger: [\"blur\", \"change\"]\r\n          }\r\n        ],\r\n        repwd: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\r\n              \"admin.system.admin.validateConfirmPassword.required\"\r\n            ),\r\n            validator: validatePass,\r\n            trigger: [\"blur\", \"change\"]\r\n          }\r\n        ],\r\n        realName: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"admin.system.admin.validateRealName.required\"),\r\n            trigger: [\"blur\", \"change\"]\r\n          }\r\n        ],\r\n        roles: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"admin.system.admin.validateRoles.required\"),\r\n            trigger: [\"blur\", \"change\"]\r\n          }\r\n        ],\r\n        phone: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"admin.system.admin.validatePhone.required\"),\r\n            trigger: [\"blur\", \"change\"]\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.initEditData();\r\n    this.handleGetRoleList();\r\n  },\r\n  methods: {\r\n    close() {\r\n      this.$emit(\"hideEditDialog\");\r\n    },\r\n    handleGetRoleList() {\r\n      const _pram = {\r\n        page: 1,\r\n        limit: this.constants.page.limit[4],\r\n        status: 1\r\n      };\r\n      roleApi.getRoleList(_pram).then(data => {\r\n        this.roleList = data;\r\n        let arr = [];\r\n        data.list.forEach(item => {\r\n          arr.push(item.id);\r\n        });\r\n        if (!arr.includes(Number.parseInt(this.pram.roles))) {\r\n          this.$set(this.pram, \"roles\", []);\r\n        }\r\n      });\r\n    },\r\n    initEditData() {\r\n      if (this.isCreate !== 1) return;\r\n      const {\r\n        account,\r\n        realName,\r\n        roles,\r\n        level,\r\n        status,\r\n        id,\r\n        phone\r\n      } = this.editData;\r\n      this.pram.account = account;\r\n      this.pram.realName = realName;\r\n      const _roles = [];\r\n      if (roles.length > 0 && !roles.includes(\",\")) {\r\n        //如果权限id集合有长度并且是只有一个，就将它Push进_roles这个数组\r\n        _roles.push(Number.parseInt(roles));\r\n      } else {\r\n        //否则就将多个id集合解构以后push进roles并且转换为整型\r\n        _roles.push(...roles.split(\",\").map(item => Number.parseInt(item)));\r\n      }\r\n      this.pram.roles = _roles;\r\n      this.pram.status = status;\r\n      this.pram.id = id;\r\n      this.pram.phone = phone;\r\n      this.rules.pwd = [];\r\n      this.rules.repwd = [];\r\n    },\r\n    handlerSubmit: Debounce(function(form) {\r\n      this.$refs[form].validate(valid => {\r\n        if (!valid) return;\r\n        if (this.isCreate === 0) {\r\n          this.handlerSave();\r\n        } else {\r\n          this.handlerEdit();\r\n        }\r\n      });\r\n    }),\r\n    handlerSave() {\r\n      systemAdminApi.adminAdd(this.pram).then(data => {\r\n        this.$message.success(\r\n          this.$t(\"admin.system.admin.message.createSuccess\")\r\n        );\r\n        this.$emit(\"hideEditDialog\");\r\n      });\r\n    },\r\n    handlerEdit() {\r\n      this.pram.roles = this.pram.roles.join(\",\");\r\n      systemAdminApi.adminUpdate(this.pram).then(data => {\r\n        this.$message.success(\r\n          this.$t(\"admin.system.admin.message.updateSuccess\")\r\n        );\r\n        this.$emit(\"hideEditDialog\");\r\n      });\r\n    },\r\n    rulesSelect(selectKeys) {\r\n      this.pram.rules = selectKeys;\r\n    },\r\n    handlerPwdInput(val) {\r\n      if (!val) {\r\n        this.rules.pwd = [];\r\n        this.rules.repwd = [];\r\n        return;\r\n      }\r\n      this.rules.pwd = [\r\n        {\r\n          required: true,\r\n          message: this.$t(\"admin.system.admin.validatePassword.required\"),\r\n          trigger: [\"blur\", \"change\"]\r\n        },\r\n        {\r\n          min: 6,\r\n          max: 20,\r\n          message: this.$t(\"admin.system.admin.validatePassword.lengthError\"),\r\n          trigger: [\"blur\", \"change\"]\r\n        }\r\n      ];\r\n      this.rules.repwd = [\r\n        {\r\n          required: true,\r\n          message: this.$t(\r\n            \"admin.system.admin.validateConfirmPassword.required\"\r\n          ),\r\n          validator: (rule, value, callback) => {\r\n            if (value === \"\") {\r\n              callback(\r\n                new Error(this.$t(\"admin.system.admin.validatePass.notMatch\"))\r\n              );\r\n            } else if (value !== this.pram.pwd) {\r\n              callback(\r\n                new Error(this.$t(\"admin.system.admin.validatePass.notMatch\"))\r\n              );\r\n            } else {\r\n              callback();\r\n            }\r\n          },\r\n          trigger: [\"blur\", \"change\"]\r\n        }\r\n      ];\r\n    }\r\n  }\r\n};\r\n", null]}