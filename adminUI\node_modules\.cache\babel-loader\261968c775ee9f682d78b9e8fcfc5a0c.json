{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\FixiOSBug.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\Sidebar\\FixiOSBug.js", "mtime": 1754050582337}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754554385233}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _default = exports.default = {\n  computed: {\n    device: function device() {\n      return this.$store.state.app.device;\n    }\n  },\n  mounted: function mounted() {\n    // In order to fix the click on menu on the ios device will trigger the mouseleave bug\n    // https://github.com/PanJiaChen/vue-element-admin/issues/1135\n    this.fixBugIniOS();\n  },\n  methods: {\n    fixBugIniOS: function fixBugIniOS() {\n      var _this = this;\n      var $subMenu = this.$refs.subMenu;\n      if ($subMenu) {\n        var handleMouseleave = $subMenu.handleMouseleave;\n        $subMenu.handleMouseleave = function (e) {\n          if (_this.device === 'mobile') {\n            return;\n          }\n          handleMouseleave(e);\n        };\n      }\n    }\n  }\n};", null]}