{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\TagsView\\ScrollPane.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\layout\\components\\TagsView\\ScrollPane.vue", "mtime": 1754050582341}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n//\n//\n//\n//\n//\n//\n\nvar tagAndTagSpacing = 4; // tagAndTagSpacing\nvar _default = exports.default = {\n  name: 'ScrollPane',\n  data: function data() {\n    return {\n      left: 0\n    };\n  },\n  computed: {\n    scrollWrapper: function scrollWrapper() {\n      return this.$refs.scrollContainer.$refs.wrap;\n    }\n  },\n  methods: {\n    handleScroll: function handleScroll(e) {\n      var eventDelta = e.wheelDelta || -e.deltaY * 40;\n      var $scrollWrapper = this.scrollWrapper;\n      $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft + eventDelta / 4;\n    },\n    moveToTarget: function moveToTarget(currentTag) {\n      var $container = this.$refs.scrollContainer.$el;\n      var $containerWidth = $container.offsetWidth;\n      var $scrollWrapper = this.scrollWrapper;\n      var tagList = this.$parent.$refs.tag;\n      var firstTag = null;\n      var lastTag = null;\n\n      // find first tag and last tag\n      if (tagList.length > 0) {\n        firstTag = tagList[0];\n        lastTag = tagList[tagList.length - 1];\n      }\n      if (firstTag === currentTag) {\n        $scrollWrapper.scrollLeft = 0;\n      } else if (lastTag === currentTag) {\n        $scrollWrapper.scrollLeft = $scrollWrapper.scrollWidth - $containerWidth;\n      } else {\n        // find preTag and nextTag\n        var currentIndex = tagList.findIndex(function (item) {\n          return item === currentTag;\n        });\n        var prevTag = tagList[currentIndex - 1];\n        var nextTag = tagList[currentIndex + 1];\n\n        // the tag's offsetLeft after of nextTag\n        var afterNextTagOffsetLeft = nextTag.$el.offsetLeft + nextTag.$el.offsetWidth + tagAndTagSpacing;\n\n        // the tag's offsetLeft before of prevTag\n        var beforePrevTagOffsetLeft = prevTag.$el.offsetLeft - tagAndTagSpacing;\n        if (afterNextTagOffsetLeft > $scrollWrapper.scrollLeft + $containerWidth) {\n          $scrollWrapper.scrollLeft = afterNextTagOffsetLeft - $containerWidth;\n        } else if (beforePrevTagOffsetLeft < $scrollWrapper.scrollLeft) {\n          $scrollWrapper.scrollLeft = beforePrevTagOffsetLeft;\n        }\n      }\n    }\n  }\n};", null]}