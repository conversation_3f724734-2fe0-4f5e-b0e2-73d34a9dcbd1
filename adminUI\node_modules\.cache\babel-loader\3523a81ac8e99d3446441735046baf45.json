{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\integral\\integralLog\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\integral\\integralLog\\index.vue", "mtime": 1754050582451}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _marketing = require(\"@/api/marketing\");\nvar _index = _interopRequireDefault(require(\"@/components/cards/index\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  components: {\n    cardsData: _index.default\n  },\n  data: function data() {\n    return {\n      loading: false,\n      options: [],\n      fromList: this.$constants.fromList,\n      listLoading: false,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        dateLimit: '',\n        keywords: ''\n      },\n      userIdList: [],\n      userList: [],\n      timeVal: [],\n      values: []\n    };\n  },\n  mounted: function mounted() {\n    this.getList();\n    // this.getUserList()\n  },\n  methods: {\n    seachList: function seachList() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 选择时间\n    selectChange: function selectChange(tab) {\n      this.tableFrom.dateLimit = tab;\n      this.tableFrom.page = 1;\n      this.timeVal = [];\n      this.getList();\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : '';\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 列表\n    getList: function getList() {\n      var _this = this;\n      this.listLoading = true;\n      (0, _marketing.integralListApi)({\n        limit: this.tableFrom.limit,\n        page: this.tableFrom.page\n      }, this.tableFrom).then(function (res) {\n        _this.tableData.data = res.list;\n        _this.tableData.total = res.total;\n        _this.listLoading = false;\n      }).catch(function (res) {\n        _this.listLoading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    }\n  }\n};", null]}