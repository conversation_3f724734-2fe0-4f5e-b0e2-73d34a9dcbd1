{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\goodList\\goodListFrom\\index.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\goodList\\goodListFrom\\index.js", "mtime": 1754550276402}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754052677050}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _elementUi = _interopRequireDefault(require(\"element-ui\"));\nrequire(\"@/styles/element-variables.scss\");\nvar _index = _interopRequireDefault(require(\"./index.vue\"));\nvar _vue = _interopRequireDefault(require(\"vue\"));\nvar _jsCookie = _interopRequireDefault(require(\"js-cookie\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n_vue.default.use(_elementUi.default, {\n  size: _jsCookie.default.get('size') || 'medium' // set element-ui default size\n});\nvar goodListFrom = {};\ngoodListFrom.install = function (Vue, options) {\n  var ToastConstructor = Vue.extend(_index.default);\n  // 生成一个该子类的实例\n  var instance = new ToastConstructor();\n  instance.$mount(document.createElement('div'));\n  document.body.appendChild(instance.$el);\n  Vue.prototype.$modalGoodList = function (callback, handleNum, row) {\n    instance.visible = true;\n    instance.callback = callback;\n    instance.handleNum = handleNum;\n    instance.checked = row;\n  };\n};\nvar _default = exports.default = goodListFrom;", null]}