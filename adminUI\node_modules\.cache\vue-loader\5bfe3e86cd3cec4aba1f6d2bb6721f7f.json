{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\forgetPassword.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\sms\\smsConfig\\components\\forgetPassword.vue", "mtime": 1754050582485}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { updatePasswordApi, captchaApi, configApi } from '@/api/sms';\nexport default {\n    name: 'forgetPassword',\n    data () {\n        const validatePhone = (rule, value, callback) => {\n            if (!value) {\n                return callback(new Error('请填写手机号'));\n            } else if (!/^1[3456789]\\d{9}$/.test(value)) {\n                callback(new Error('手机号格式不正确!'));\n            } else {\n                callback();\n            }\n        };\n        var validatePass = (rule, value, callback) => {\n            if (value === '') {\n                callback(new Error('请输入密码'));\n            }else {\n                if(this.current === 1){\n                    if (this.formInline.checkPass !== '') {\n                        this.$refs.formInline.validateField('checkPass');\n                    }\n                    callback();\n                }else{\n                    if(value !== this.formInline.checkPass){\n                        callback(new Error('请输入正确密码!'));\n                    }\n                    callback();\n                }\n            }\n        };\n        var validatePass2 = (rule, value, callback) => {\n            if (value === '') {\n                callback(new Error('请再次输入密码'));\n            } else if (value !== this.formInline.password) {\n                callback(new Error('两次输入密码不一致!'));\n            } else {\n                callback();\n            }\n        };\n        return {\n            isReadonly: false,\n            cutNUm: '获取验证码',\n            canClick: true,\n            current: 0,\n            formInline: {\n                account: '',\n                phone: '',\n                code: '',\n                password: '',\n                checkPass: '',\n            },\n            ruleInline: {\n                phone: [\n                    { required: true, validator: validatePhone, trigger: 'blur' }\n                ],\n                code: [\n                    { required: true, message: '请输入验证码', trigger: 'blur' }\n                ],\n                password: [\n                    { validator: validatePass, trigger: 'blur' }\n                ],\n                checkPass: [\n                    { validator: validatePass2, trigger: 'blur' }\n                ],\n            }\n        }\n    },\n    props:{\n      infoData: {\n        type: Object,\n        default: null\n      }\n    },\n    mounted(){\n      this.infoData ? this.formInline.phone = this.infoData.phone : this.formInline.phone = '';\n    },\n    methods: {\n        // 短信验证码\n        cutDown () {\n            if (this.formInline.phone) {\n                if (!this.canClick) return;\n                this.canClick = false;\n                this.cutNUm = 60;\n                let data = {\n                    phone: this.formInline.phone,\n                    types: 1\n                };\n                captchaApi(data).then(async res => {\n                    this.$message.success(res.msg);\n                })\n                let time = setInterval(() => {\n                    this.cutNUm--;\n                    if (this.cutNUm === 0) {\n                        this.cutNUm = '获取验证码';\n                        this.canClick = true;\n                        clearInterval(time)\n                    }\n                }, 1000)\n            } else {\n                this.$message.warning('请填写手机号!');\n            }\n        },\n        handleSubmit1 (name, current) {\n            this.$refs[name].validate((valid) => {\n                if (valid) {\n                  this.current = 1;\n                } else {\n                    return false;\n                }\n            })\n        },\n        handleSubmit2(name) {\n            this.formInline.account = this.formInline.phone\n            this.$refs[name].validate((valid) => {\n                if (valid) {\n                  updatePasswordApi(this.formInline).then(async res => {\n                        this.$message.success('修改成功');\n                        this.current = 2;\n                    })\n                } else {\n                    return false;\n                }\n            })\n        },\n        //登录\n        handleSubmit (name) {\n            this.$refs[name].validate((valid) => {\n                if (valid) {\n                    configApi({\n                        account: this.formInline.account,\n                        password: this.formInline.password\n                    }).then(async res => {\n                        this.$message.success('登录成功!');\n                        this.$emit('on-Login');\n                    })\n                } else {\n                    return false;\n                }\n            })\n        },\n        returns () {\n            this.current === 0 ? this.$emit('goback'): this.current = 0\n        }\n    }\n}\n", null]}