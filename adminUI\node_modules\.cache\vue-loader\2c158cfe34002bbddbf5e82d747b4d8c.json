{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillList\\index.vue?vue&type=template&id=2046c57d&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillList\\index.vue", "mtime": 1754050582454}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <div class=\"container\">\n        <el-form inline>\n          <el-form-item label=\"是否显示：\">\n            <el-select v-model=\"tableFrom.status\" placeholder=\"请选择\" class=\"filter-item selWidth\" @change=\"getList(1)\" clearable>\n              <el-option label=\"关闭\" :value=\"0\" />\n              <el-option label=\"开启\" :value=\"1\" />\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"配置名称：\">\n            <el-select v-model=\"tableFrom.timeId\" placeholder=\"请选择\" class=\"selWidth\" @change=\"getList(1)\" clearable>\n              <el-option\n                v-for=\"item in seckillTime\"\n                :key=\"item.id\"\n                :label=\"item.name +' - ' + item.time\"\n                :value=\"item.id\">\n              </el-option>\n            </el-select>\n            <!--<el-input v-model=\"tableFrom.name\" placeholder=\"请输入秒杀名称\" class=\"selWidth\">-->\n              <!--<el-button slot=\"append\" icon=\"el-icon-search\" @click=\"getList(1)\"/>-->\n            <!--</el-input>-->\n          </el-form-item>\n          <el-form-item label=\"商品搜索：\">\n            <el-input v-model=\"tableFrom.keywords\" placeholder=\"请输入商品ID/名称\" class=\"selWidth\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"getList(1)\"/>\n            </el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n      <router-link :to=\" { path:'/marketing/seckill/creatSeckill/creat' }\">\n        <el-button size=\"small\" type=\"primary\" class=\"mr10\" v-hasPermi=\"['admin:seckill:save']\">添加秒杀商品</el-button>\n      </router-link>\n    </div>\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      ref=\"multipleTable\"\n      :header-cell-style=\" {fontWeight:'bold'}\"\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        min-width=\"50\"\n      />\n      <el-table-column\n        label=\"配置\"\n        min-width=\"160\"\n      >\n        <template slot-scope=\"scope\">\n          <div>{{scope.row.storeSeckillManagerResponse ? scope.row.storeSeckillManagerResponse.name : '-'}}</div>\n          <div>{{scope.row.startTime + ' - ' + scope.row.stopTime}}</div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"name\"\n        label=\"秒杀时段\"\n        min-width=\"130\"\n      >\n        <template slot-scope=\"scope\">\n          <div>{{ scope.row.storeSeckillManagerResponse ? scope.row.storeSeckillManagerResponse.time.split(',').join(' - ') : '-'}}</div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"商品图片\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"scope.row.image\"\n              :preview-src-list=\"[scope.row.image]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"商品标题\" prop=\"title\" min-width=\"300\" :show-overflow-tooltip=\"true\">\n      </el-table-column>\n      <el-table-column label=\"活动简介\" min-width=\"300\" prop=\"info\" :show-overflow-tooltip=\"true\"></el-table-column>\n      <el-table-column\n        label=\"原价\"\n        prop=\"otPrice\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        label=\"秒杀价\"\n        min-width=\"100\"\n        prop=\"price\"\n      />\n      <el-table-column\n        label=\"限量\"\n        prop=\"quotaShow\"\n        min-width=\"80\"\n      />\n      <el-table-column\n        label=\"限量剩余\"\n        min-width=\"80\"\n        prop=\"quota\"\n      />\n      <el-table-column\n        label=\"秒杀状态\"\n        min-width=\"100\"\n        prop=\"statusName\"\n      />\n      <el-table-column\n        label=\"创建时间\"\n        prop=\"createTime\"\n        min-width=\"150\"\n      />\n      <el-table-column\n        label=\"状态\"\n        min-width=\"80\"\n        fixed=\"right\"\n      >\n        <template slot-scope=\"scope\" v-if=\"checkPermi(['admin:seckill:update:status'])\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            :active-value=\"1\"\n            :inactive-value=\"0\"\n            active-text=\"开启\"\n            inactive-text=\"关闭\"\n            @change=\"onchangeIsShow(scope.row)\"\n          />\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" min-width=\"120\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <router-link :to=\"{ path:'/marketing/seckill/creatSeckill/updeta/' + scope.row.productId + '/' + scope.row.id}\">\n            <el-button type=\"text\" size=\"small\" v-hasPermi=\"['admin:seckill:info']\">编辑</el-button>\n          </router-link>\n          <el-button v-if=\"scope.row.killStatus !== 2 \" \n          type=\"text\" size=\"small\" \n          @click=\"handleDelete(scope.row.id, scope.$index)\"  \n          class=\"mr10\"\n          v-hasPermi=\"['admin:seckill:delete']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block mb20\">\n      <el-pagination\n        :page-sizes=\"[10, 20, 30, 40]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n</div>\n", null]}