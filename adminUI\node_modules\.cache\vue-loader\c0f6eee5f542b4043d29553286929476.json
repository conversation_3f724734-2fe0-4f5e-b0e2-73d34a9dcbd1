{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\groupBuy\\groupGoods\\index.vue?vue&type=template&id=486b090f&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\groupBuy\\groupGoods\\index.vue", "mtime": 1754050582446}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <div class=\"container\">\n        <el-form inline>\n          <el-form-item label=\"拼团状态：\">\n            <el-select v-model=\"tableFrom.isShow\" placeholder=\"请选择\" class=\"filter-item selWidth mr20\" @change=\"getList(1)\" clearable>\n              <el-option label=\"关闭\" :value=\"0\" />\n              <el-option label=\"开启\" :value=\"1\" />\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"商品搜索：\">\n            <el-input v-model=\"tableFrom.keywords\" placeholder=\"请输入商品名称、ID\" class=\"selWidth\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"getList(1)\"/>\n            </el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n      <router-link :to=\" { path:'/marketing/groupBuy/creatGroup' }\">\n        <el-button size=\"mini\" type=\"primary\" class=\"mr10\" v-hasPermi=\"['admin:combination:save']\">添加拼团商品</el-button>\n      </router-link>\n      <el-button size=\"mini\" class=\"mr10\" @click=\"exportList\" v-hasPermi=\"['admin:export:excel:combiantion']\">导出</el-button>\n    </div>\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      ref=\"multipleTable\"\n      :header-cell-style=\" {fontWeight:'bold'}\"\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        min-width=\"50\"\n      />\n      <el-table-column label=\"拼团图片\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"scope.row.image\"\n              :preview-src-list=\"[scope.row.image]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"拼团名称\" prop=\"title\" min-width=\"300\">\n        <template slot-scope=\"scope\">\n          <el-popover trigger=\"hover\" placement=\"right\" :open-delay=\"800\">\n            <div class=\"text_overflow\" slot=\"reference\">{{scope.row.title}}</div>\n            <div class=\"pup_card\">{{scope.row.title}}</div>\n          </el-popover>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"原价\"\n        prop=\"otPrice\"\n        min-width=\"100\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"拼团价\"\n        prop=\"price\"\n        min-width=\"100\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"拼团人数\"\n        prop=\"countPeople\"\n        min-width=\"100\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"参与人数\"\n        prop=\"countPeopleAll\"\n        min-width=\"100\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"成团数量\"\n        prop=\"countPeoplePink\"\n        min-width=\"100\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"限量\"\n        min-width=\"100\"\n        prop=\"quotaShow\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"限量剩余\"\n        prop=\"remainingQuota\"\n        min-width=\"100\"\n        align=\"center\"\n      />\n      <el-table-column\n        prop=\"stopTime\"\n        label=\"结束时间\"\n        min-width=\"130\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.stopTime| formatDate}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"拼团状态\"\n        min-width=\"150\"\n      >\n        <template slot-scope=\"scope\" v-if=\"checkPermi(['admin:combination:update:status'])\">\n          <el-switch\n            v-model=\"scope.row.isShow\"\n            :active-value=\"true\"\n            :inactive-value=\"false\"\n            active-text=\"开启\"\n            inactive-text=\"关闭\"\n            @change=\"onchangeIsShow(scope.row)\"\n          />\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" min-width=\"150\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <router-link :to=\"{ path:'/marketing/groupBuy/creatGroup/' + scope.row.id}\">\n            <el-button type=\"text\" size=\"small\" v-hasPermi=\"['admin:combination:info']\">编辑</el-button>\n          </router-link>\n          <el-button type=\"text\" size=\"small\" @click=\"handleDelete(scope.row.id, scope.$index)\"  class=\"mr10\" v-hasPermi=\"['admin:combination:delete']\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block mb20\">\n      <el-pagination\n        :page-sizes=\"[10, 20, 30, 40]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n</div>\n", null]}