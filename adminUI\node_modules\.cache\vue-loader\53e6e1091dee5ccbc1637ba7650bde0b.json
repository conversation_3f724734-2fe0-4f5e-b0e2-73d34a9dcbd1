{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\marketing\\seckill\\seckillList\\index.vue", "mtime": 1754050582454}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { seckillStoreListApi, seckillStoreDeleteApi, seckill<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, seckill<PERSON><PERSON><PERSON><PERSON>us<PERSON><PERSON> } from '@/api/marketing'\nimport { getSeckillList } from '@/libs/public'\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nexport default {\n  name: \"SeckillList\",\n  data() {\n    return {\n      listLoading: true,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        timeId: '',\n        status: '',\n        keywords: ''\n      },\n      seckillTime: []\n    }\n  },\n  mounted() {\n    getSeckillList().then((res) => {\n      this.seckillTime = res.list\n    })\n    this.tableFrom.timeId = Number(this.$route.params.timeId) || ''\n    this.getList()\n  },\n  methods: {\n    checkPermi,\n    // 订单删除\n    handleDelete(id, idx) {\n      this.$modalSure().then(() => {\n        seckillStoreDeleteApi({ id: id }).then(() => {\n          this.$message.success('删除成功')\n          this.tableData.data.splice(idx, 1)\n        })\n      })\n    },\n    // 列表\n    getList(num) {\n      this.listLoading = true\n      this.tableFrom.page = num ? num : this.tableFrom.page;\n      seckillStoreListApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.listLoading = false\n      }).catch((res) => {\n        this.listLoading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      this.getList()\n    },\n    onchangeIsShow(row) {\n      seckillStoreStatusApi({id: row.id, status: row.status})\n        .then(async () => {\n          this.$message.success('修改成功');\n          this.getList()\n        }).catch(()=>{\n        row.status = !row.status\n      })\n    }\n  }\n}\n", null]}