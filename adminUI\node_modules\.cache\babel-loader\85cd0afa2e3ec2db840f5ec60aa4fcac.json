{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\setting\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\setting\\index.vue", "mtime": 1754050582515}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _Parser = _interopRequireDefault(require(\"@/components/FormGenerator/components/parser/Parser\"));\nvar categoryApi = _interopRequireWildcard(require(\"@/api/categoryApi.js\"));\nvar selfUtil = _interopRequireWildcard(require(\"@/utils/ZBKJIutil.js\"));\nvar systemFormConfigApi = _interopRequireWildcard(require(\"@/api/systemFormConfig.js\"));\nvar systemSettingApi = _interopRequireWildcard(require(\"@/api/systemSetting.js\"));\nvar systemConfigApi = _interopRequireWildcard(require(\"@/api/systemConfig.js\"));\nvar _index = _interopRequireDefault(require(\"@/views/appSetting/wxAccount/wxTemplate/index\"));\nvar _utils = require(\"@/components/FormGenerator/utils\");\nvar _permission = require(\"@/utils/permission\");\nvar _validate = require(\"@/utils/validate\");\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// 权限判断函数\nvar _default = exports.default = {\n  // name: \"index\",\n  components: {\n    Template: _index.default,\n    parser: _Parser.default\n  },\n  data: function data() {\n    return {\n      loading: false,\n      formConf: {\n        content: {\n          fields: []\n        },\n        id: null,\n        render: false,\n        isEdit: false\n      },\n      formConfChild: {\n        content: {\n          fields: []\n        },\n        id: null,\n        render: false,\n        isEdit: false\n      },\n      activeNamel1: null,\n      activeNamel2: '',\n      //针对文件特殊处理\n      treeList: [],\n      editDataChild: {},\n      isCreate: 0,\n      currentEditId: null,\n      currentEditData: null,\n      currentSelectedUploadFlag: null\n    };\n  },\n  mounted: function mounted() {\n    this.handlerGetTreeList();\n    this.getCurrentUploadSelectedFlag();\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    handleTabClick: function handleTabClick(tab) {\n      this.activeNamel2 = tab.$children[0].panes[0].name;\n      this.handlerGetLevel2FormConfig(this.activeNamel2);\n    },\n    handlerGetLevel1FormConfig: function handlerGetLevel1FormConfig(id) {\n      var _this = this;\n      var formPram = {\n        id: id\n      };\n      this.currentEditId = id;\n      this.formConf.content = {\n        fields: []\n      };\n      this.formConf.render = false;\n      this.loading = true;\n      systemFormConfigApi.getFormConfigInfo(formPram).then(function (data) {\n        var id = data.id,\n          name = data.name,\n          info = data.info,\n          content = data.content;\n        _this.formConf.content = JSON.parse(content);\n        _this.formConf.id = id;\n        _this.handlerGetSettingInfo(id, 1);\n        _this.loading = false;\n      }).catch(function () {\n        _this.loading = false;\n      });\n    },\n    handleItemTabClick: function handleItemTabClick(tab, event) {\n      //这里对tabs=tab.name和radio=id做了兼容\n      var _id = tab.name ? tab.name : tab;\n      if (!_id) return this.$message.error('表单配置不正确，请关联正确表单后使用');\n      this.handlerGetLevel2FormConfig(_id);\n    },\n    handlerGetLevel2FormConfig: function handlerGetLevel2FormConfig(id) {\n      var _this2 = this;\n      var formPram = {\n        id: id\n      };\n      this.currentEditId = id;\n      this.formConfChild.content = {\n        fields: []\n      };\n      this.formConfChild.render = false;\n      this.loading = true;\n      systemFormConfigApi.getFormConfigInfo(formPram).then(function (data) {\n        var id = data.id,\n          name = data.name,\n          info = data.info,\n          content = data.content;\n        _this2.formConfChild.content = JSON.parse(content);\n        _this2.formConfChild.id = id;\n        _this2.handlerGetSettingInfo(id, 2);\n        _this2.loading = false;\n      }).catch(function () {\n        _this2.loading = false;\n      });\n    },\n    handlerGetSettingInfo: function handlerGetSettingInfo(id, level) {\n      var _this3 = this;\n      systemSettingApi.systemConfigInfo({\n        id: id\n      }).then(function (data) {\n        _this3.currentEditData = data;\n        if (level === 1) {\n          _this3.formConf.isEdit = _this3.currentEditData !== null;\n          _this3.formConf.render = true;\n        } else {\n          _this3.formConfChild.isEdit = _this3.currentEditData !== null;\n          _this3.formConfChild.render = true;\n        }\n      });\n    },\n    handlerSubmit: (0, _validate.Debounce)(function (formValue) {\n      this.handlerSave(formValue);\n    }),\n    handlerSave: function handlerSave(formValue) {\n      var _this4 = this;\n      var _pram = this.buildFormPram(formValue);\n      var _formId = 0;\n      systemSettingApi.systemConfigSave(_pram).then(function (data) {\n        _this4.$message.success('添加数据成功');\n      });\n    },\n    handlerGetTreeList: function handlerGetTreeList() {\n      var _this5 = this;\n      var _pram = {\n        type: this.$constants.categoryType[5].value,\n        status: 1\n      };\n      this.loading = true;\n      categoryApi.treeCategroy(_pram).then(function (data) {\n        _this5.treeList = _this5.handleAddArrt(data);\n        if (_this5.treeList.length > 0) _this5.activeNamel1 = _this5.treeList[0].id.toString();\n        if (_this5.treeList.length > 0 && _this5.treeList[0].child.length > 0) {\n          _this5.activeNamel2 = _this5.treeList[0].child[0].extra;\n        }\n        if (_this5.activeNamel2) {\n          _this5.handlerGetLevel2FormConfig(_this5.treeList[0].child[0].extra);\n        }\n        // else {\n        //  this.handlerGetLevel1FormConfig(this.treeList[0].extra)\n        //}\n        _this5.loading = false;\n      }).catch(function () {\n        _this5.loading = false;\n      });\n    },\n    handleAddArrt: function handleAddArrt(treeData) {\n      // let _result = this.addTreeListLabel(treeData)\n      var _result = selfUtil.addTreeListLabel(treeData);\n      return _result;\n    },\n    buildFormPram: function buildFormPram(formValue) {\n      var _pram = {\n        fields: [],\n        id: this.currentEditId,\n        sort: 0,\n        // 参数暂时无用\n        status: true // 参数暂时无用\n      };\n      var _fields = [];\n      Object.keys(formValue).forEach(function (key) {\n        _fields.push({\n          name: key,\n          title: key,\n          value: formValue[key]\n        });\n      });\n      _pram.fields = _fields;\n      return _pram;\n    },\n    getCurrentUploadSelectedFlag: function getCurrentUploadSelectedFlag() {\n      var _this6 = this;\n      systemConfigApi.configGetUniq({\n        key: \"uploadType\"\n      }).then(function (data) {\n        _this6.currentSelectedUploadFlag = parseInt(data);\n      });\n    }\n  }\n};", null]}