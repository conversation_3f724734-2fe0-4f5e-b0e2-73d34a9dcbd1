{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\Breadcrumb\\index.vue?vue&type=template&id=b50ef614&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\components\\Breadcrumb\\index.vue", "mtime": 1754381016270}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754554389593}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\n<el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\n  <transition-group name=\"breadcrumb\">\n    <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\n      <span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ $t('dashboard.'+item.meta.title) }}</span>\n      <a v-else @click.prevent=\"handleLink(item)\">{{ $t('dashboard.'+item.meta.title) }}</a>\n    </el-breadcrumb-item>\n  </transition-group>\n</el-breadcrumb>\n", null]}