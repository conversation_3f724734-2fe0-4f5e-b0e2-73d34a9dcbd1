{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\notification\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\views\\systemSetting\\notification\\index.vue", "mtime": 1754050582514}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754554387118}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754554386628}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754554388384}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _systemFormConfig = require(\"@/api/systemFormConfig\");\nvar _wxApi = require(\"@/api/wxApi\");\nvar _validate = require(\"@/utils/validate\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  data: function data() {\n    return {\n      modalTitle: \"\",\n      notificationModal: false,\n      headerList: [{\n        label: \"通知会员\",\n        value: \"1\"\n      }, {\n        label: \"通知平台\",\n        value: \"2\"\n      }],\n      id: 0,\n      levelLists: [],\n      currentTab: \"1\",\n      loading: false,\n      formData: {},\n      industry: null,\n      loadingList: false,\n      centerDialogVisible: false,\n      infoList: [],\n      infoList1: [{\n        label: \"短信\",\n        value: \"sms\"\n      }],\n      form: {\n        content: '',\n        name: '',\n        id: '',\n        status: null,\n        tempId: '',\n        tempKey: '',\n        title: ''\n      },\n      detailType: '',\n      infoTab: ''\n    };\n  },\n  created: function created() {\n    this.getNotificationList(Number(this.currentTab));\n  },\n  methods: {\n    changeTab: function changeTab(data) {\n      this.getNotificationList(data.name);\n    },\n    //获取消息列表\n    getNotificationList: function getNotificationList(id) {\n      var _this = this;\n      this.loadingList = true;\n      (0, _systemFormConfig.notificationListApi)({\n        sendType: id\n      }).then(function (res) {\n        _this.loadingList = false;\n        _this.levelLists = res;\n      }).catch(function (res) {\n        _this.loadingList = false;\n      });\n    },\n    //公众号消息开关\n    changeWechat: function changeWechat(row) {\n      var _this2 = this;\n      (0, _systemFormConfig.notificationWechat)(row.id).then(function (res) {\n        _this2.$modal.msgSuccess(\"修改成功\");\n      });\n    },\n    //小程序消息开关\n    changeRoutine: function changeRoutine(row) {\n      var _this3 = this;\n      (0, _systemFormConfig.notificationRoutine)(row.id).then(function (res) {\n        _this3.$modal.msgSuccess(\"修改成功\");\n      });\n    },\n    //短信消息开关\n    changeSms: function changeSms(row) {\n      var _this4 = this;\n      (0, _systemFormConfig.notificationSms)(row.id).then(function (res) {\n        _this4.$modal.msgSuccess(\"修改成功\");\n      });\n    },\n    //详情tab切换\n    changeInfo: function changeInfo(data) {\n      this.getNotificationDetail(data);\n    },\n    //详情数据\n    getNotificationDetail: function getNotificationDetail(param) {\n      var _this5 = this;\n      var data = {\n        id: this.id,\n        type: param.name\n      };\n      this.$set(this, 'detailType', data.type);\n      (0, _systemFormConfig.notificationDetail)(data).then(function (res) {\n        _this5.form = res;\n        _this5.$set(_this5.form, 'status', res.status.toString());\n      });\n    },\n    // 设置\n    setting: function setting(row) {\n      this.infoList = [];\n      this.id = row.id;\n      this.centerDialogVisible = true;\n      if (row.isWechat !== 0) {\n        this.infoList.push({\n          label: \"公众号模板消息\",\n          value: \"wechat\"\n        });\n      }\n      if (row.isRoutine !== 0) {\n        this.infoList.push({\n          label: \"小程序订阅消息\",\n          value: \"routine\"\n        });\n      }\n      if (row.isSms !== 0) {\n        this.infoList.push({\n          label: \"短信\",\n          value: \"sms\"\n        });\n      }\n      this.infoTab = this.infoList[0].value;\n      this.getNotificationDetail({\n        name: this.infoTab\n      });\n    },\n    //修改通知\n    submit: (0, _validate.Debounce)(function () {\n      var _this6 = this;\n      var data = {\n        id: this.id,\n        status: Number(this.form.status),\n        tempId: this.form.tempId,\n        type: this.detailType\n      };\n      (0, _systemFormConfig.notificationUpdate)(data).then(function (res) {\n        _this6.$modal.msgSuccess(\"修改成功\");\n        _this6.centerDialogVisible = false;\n        _this6.getNotificationList();\n      });\n    }),\n    syncWechat: function syncWechat() {\n      var _this7 = this;\n      (0, _wxApi.wechatAsyncApi)().then(function (res) {\n        _this7.$message.success('同步成功');\n      });\n    },\n    syncRoutine: function syncRoutine() {\n      var _this8 = this;\n      (0, _wxApi.routineAsyncApi)().then(function (res) {\n        _this8.$message.success('同步成功');\n      });\n    }\n  }\n};", null]}